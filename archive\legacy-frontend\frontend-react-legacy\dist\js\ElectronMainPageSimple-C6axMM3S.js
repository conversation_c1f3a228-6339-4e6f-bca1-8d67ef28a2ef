var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
import { j as jsxRuntimeExports, u as useQueryClient, b as useQuery } from "./state-management-CeNIv-64.js";
import { r as reactExports, R as React } from "./router-DbSvV1fW.js";
import { c as cn, u as useAppStore, q as queryKeys } from "./index-BaeIiao7.js";
import { u as useCases, d as useCase, e as useCaseFiles } from "./useCases-mlPph2ta.js";
import { F as FileUpload, i as invalidateFileRelatedCaches } from "./cacheUtils-BRH6C8y1.js";
import { M as Modal } from "./caseService-rs6u721W.js";
import { S as SearchBox } from "./SearchBox-CwJS6ooP.js";
import { B as Button, D as DangerButton, P as PrimaryButton, S as SecondaryButton } from "./Button-CswqCd84.js";
import { b as buildQueryParams, a as api, u as uploadFile, h as handleApiError } from "./client-BIlsoe8d.js";
import "./react-vendor-ZA51SWXd.js";
import "./ui-vendor-DgYk2OaC.js";
import "./index-DEw2ppt0.js";
class SearchApi {
  // 基础文件搜索
  searchFiles(caseId, request) {
    return __async(this, null, function* () {
      const params = buildQueryParams(request);
      const url = `/api/v1/search/cases/${caseId}/search${params ? `?${params}` : ""}`;
      return api.get(url);
    });
  }
  // 基于标签的文件筛选
  filterFiles(caseId, request) {
    return __async(this, null, function* () {
      const params = buildQueryParams(request);
      const url = `/api/v1/search/cases/${caseId}/filter${params ? `?${params}` : ""}`;
      return api.get(url);
    });
  }
  // 高级搜索
  advancedSearch(caseId, request) {
    return __async(this, null, function* () {
      return api.post(`/api/v1/search/cases/${caseId}/advanced-search`, request);
    });
  }
  // 获取搜索建议
  getSearchSuggestions(caseId, query, limit = 10) {
    return __async(this, null, function* () {
      const params = buildQueryParams({ q: query, limit });
      return api.get(`/api/v1/search/cases/${caseId}/search-suggestions?${params}`);
    });
  }
  // 组合搜索（同时支持文本搜索和标签筛选）
  combinedSearch(caseId, options) {
    return __async(this, null, function* () {
      const request = {
        text_query: options.textQuery,
        metadata_filters: options.metadataFilters,
        user_tags: options.userTags,
        ai_tags: options.aiTags,
        quality_range: options.qualityRange,
        limit: options.limit || 50,
        offset: options.offset || 0,
        sort_by: options.sortBy || "date_desc"
      };
      return this.advancedSearch(caseId, request);
    });
  }
  // 快速筛选（常用筛选条件的快捷方法）
  quickFilter(caseId, filterType, options) {
    return __async(this, null, function* () {
      let searchRequest = {
        limit: (options == null ? void 0 : options.limit) || 50,
        offset: (options == null ? void 0 : options.offset) || 0
      };
      switch (filterType) {
        case "high_quality":
          const highQualityResult = yield this.advancedSearch(caseId, {
            quality_range: [80, 100],
            limit: searchRequest.limit,
            offset: searchRequest.offset,
            sort_by: "quality_desc"
          });
          return {
            files: highQualityResult.files,
            total_count: highQualityResult.total_count,
            limit: highQualityResult.limit,
            offset: highQualityResult.offset,
            query: "高质量文件"
          };
        case "recent":
          const recentResult = yield this.advancedSearch(caseId, {
            limit: searchRequest.limit,
            offset: searchRequest.offset,
            sort_by: "date_desc"
          });
          return {
            files: recentResult.files,
            total_count: recentResult.total_count,
            limit: recentResult.limit,
            offset: recentResult.offset,
            query: "最近文件"
          };
        case "images_only":
          searchRequest.q = "image";
          break;
        case "videos_only":
          searchRequest.q = "video";
          break;
      }
      return this.searchFiles(caseId, searchRequest);
    });
  }
  // 保存搜索历史（本地存储）
  saveSearchHistory(query) {
    const history = this.getSearchHistory();
    const newHistory = [query, ...history.filter((h) => h !== query)].slice(0, 10);
    localStorage.setItem("search_history", JSON.stringify(newHistory));
  }
  // 获取搜索历史
  getSearchHistory() {
    try {
      const history = localStorage.getItem("search_history");
      return history ? JSON.parse(history) : [];
    } catch (e) {
      return [];
    }
  }
  // 清除搜索历史
  clearSearchHistory() {
    localStorage.removeItem("search_history");
  }
}
const searchApi = new SearchApi();
var define_process_env_default = {};
class FilesApi {
  // 单文件上传
  uploadFile(caseId, file, onProgress) {
    return __async(this, null, function* () {
      var _a, _b;
      const formData = new FormData();
      formData.append("file", file);
      try {
        const response = yield uploadFile(
          `/api/v1/cases/${caseId}/files/upload-and-copy`,
          file,
          onProgress
        );
        return response.data;
      } catch (error) {
        console.error("文件上传失败:", error);
        throw new Error(((_b = (_a = error.response) == null ? void 0 : _a.data) == null ? void 0 : _b.detail) || "文件上传失败");
      }
    });
  }
  // 批量文件上传
  uploadFiles(caseId, files, onProgress) {
    return __async(this, null, function* () {
      const result = {
        successful: [],
        failed: [],
        total: files.length
      };
      const concurrencyLimit = 3;
      const chunks = this.chunkArray(files, concurrencyLimit);
      for (const chunk of chunks) {
        const promises = chunk.map((file, index) => __async(this, null, function* () {
          const fileId = `${file.name}-${Date.now()}-${index}`;
          try {
            onProgress == null ? void 0 : onProgress(fileId, {
              fileId,
              fileName: file.name,
              progress: 0,
              status: "uploading"
            });
            const uploadedFile = yield this.uploadFile(caseId, file, (progress) => {
              onProgress == null ? void 0 : onProgress(fileId, {
                fileId,
                fileName: file.name,
                progress,
                status: "uploading"
              });
            });
            onProgress == null ? void 0 : onProgress(fileId, {
              fileId,
              fileName: file.name,
              progress: 100,
              status: "processing"
            });
            try {
              yield this.waitForFileProcessing(uploadedFile.id, caseId);
            } catch (processingError) {
              console.warn(`文件 ${file.name} 后处理可能未完成，但上传成功:`, processingError);
            }
            onProgress == null ? void 0 : onProgress(fileId, {
              fileId,
              fileName: file.name,
              progress: 100,
              status: "completed"
            });
            result.successful.push(uploadedFile);
          } catch (error) {
            console.error(`文件 ${file.name} 上传失败:`, error);
            onProgress == null ? void 0 : onProgress(fileId, {
              fileId,
              fileName: file.name,
              progress: 0,
              status: "error",
              error: error.message
            });
            result.failed.push({
              fileName: file.name,
              error: error.message
            });
          }
        }));
        yield Promise.allSettled(promises);
      }
      return result;
    });
  }
  // 等待文件处理完成
  waitForFileProcessing(fileId, caseId, maxAttempts = 15) {
    return __async(this, null, function* () {
      console.log(`🔄 开始等待文件 ${fileId} 处理完成...`);
      for (let attempt = 0; attempt < maxAttempts; attempt++) {
        try {
          const file = yield this.getFile(fileId, caseId);
          if (!file) {
            console.warn(`⚠️ 文件 ${fileId} 不存在 (尝试 ${attempt + 1}/${maxAttempts})`);
            yield new Promise((resolve) => setTimeout(resolve, 1e3));
            continue;
          }
          if (file.thumbnail_small_path) {
            console.log(`✅ 文件 ${fileId} 处理完成，缩略图路径:`, file.thumbnail_small_path);
            return;
          }
          console.log(`⏳ 文件 ${fileId} 处理中... (尝试 ${attempt + 1}/${maxAttempts})`);
          const waitTime = Math.min(1e3 * (attempt + 1), 3e3);
          yield new Promise((resolve) => setTimeout(resolve, waitTime));
        } catch (error) {
          console.warn(`检查文件处理状态失败 (尝试 ${attempt + 1}/${maxAttempts}):`, error.message || error);
          if (error.status === 404) {
            console.log(`📄 文件 ${fileId} 还未创建完成，继续等待...`);
          }
          yield new Promise((resolve) => setTimeout(resolve, 1e3));
        }
      }
      console.warn(`⏰ 文件 ${fileId} 处理超时，但上传可能已经成功`);
    });
  }
  // 获取文件详情 (需要案例ID)
  getFile(fileId, caseId) {
    return __async(this, null, function* () {
      return api.get(`/api/v1/cases/${caseId}/files/${fileId}`);
    });
  }
  // 获取案例文件列表
  getCaseFiles(caseId) {
    return __async(this, null, function* () {
      const response = yield api.get(`/api/v1/cases/${caseId}/files`);
      return response.files || [];
    });
  }
  // 删除文件 (需要案例ID)
  deleteFile(fileId, caseId) {
    return __async(this, null, function* () {
      return api.delete(`/api/v1/cases/${caseId}/files/${fileId}`);
    });
  }
  // 批量删除文件 (需要案例ID)
  deleteFiles(fileIds, caseId) {
    return __async(this, null, function* () {
      const result = {
        successful: [],
        failed: []
      };
      const promises = fileIds.map((fileId) => __async(this, null, function* () {
        try {
          yield this.deleteFile(fileId, caseId);
          result.successful.push(fileId);
        } catch (error) {
          result.failed.push({
            fileId,
            error: error.message || "删除失败"
          });
        }
      }));
      yield Promise.allSettled(promises);
      return result;
    });
  }
  // 获取文件缩略图URL
  getThumbnailUrl(file, size = "small") {
    const baseUrl = define_process_env_default.REACT_APP_API_BASE_URL || "http://localhost:8000";
    const sizeParam = size === "small" ? 300 : 600;
    return `${baseUrl}/api/v1/cases/${file.case_id}/files/${file.id}/thumbnail?size=${sizeParam}`;
  }
  // 获取文件预览URL
  getPreviewUrl(file) {
    const baseUrl = define_process_env_default.REACT_APP_API_BASE_URL || "http://localhost:8000";
    return `${baseUrl}/api/v1/cases/${file.case_id}/files/${file.id}/view`;
  }
  // 工具方法：将数组分块
  chunkArray(array, chunkSize) {
    const chunks = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }
  // 验证文件类型
  validateFile(file, allowedTypes = ["image/*", "video/*"]) {
    const maxSize = 100 * 1024 * 1024;
    if (file.size > maxSize) {
      return {
        valid: false,
        error: `文件大小超过限制 (${Math.round(file.size / 1024 / 1024)}MB > 100MB)`
      };
    }
    const isValidType = allowedTypes.some((type) => {
      if (type.endsWith("/*")) {
        const category = type.split("/")[0];
        return file.type.startsWith(category + "/");
      }
      return file.type === type;
    });
    if (!isValidType) {
      return {
        valid: false,
        error: `不支持的文件类型: ${file.type}`
      };
    }
    return { valid: true };
  }
  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }
}
const filesApi = new FilesApi();
const FileMetadataPanel = ({ file }) => {
  if (!file) {
    return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mb-4 p-3 bg-secondary-bg rounded", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-sm font-medium mb-2", children: "📊 元数据面板" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-1 text-xs text-secondary", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: "文件名: 未选择" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: "尺寸: --" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: "大小: --" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: "格式: --" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: "创建时间: --" })
      ] })
    ] });
  }
  const formatFileSize = (bytes) => {
    if (!bytes || bytes === 0) return "未知";
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + " " + sizes[i];
  };
  const formatDate = (dateString) => {
    if (!dateString) return "未知";
    try {
      const date = new Date(dateString);
      return date.toLocaleString("zh-CN");
    } catch (e) {
      return "无效日期";
    }
  };
  const parseExifData = (tags) => {
    if (!tags || !Array.isArray(tags)) return {};
    const exifData2 = {};
    tags.forEach((tag) => {
      if (tag.tag_category === "exif" || tag.tag_category === "metadata") {
        try {
          const value = typeof tag.tag_value === "string" ? JSON.parse(tag.tag_value) : tag.tag_value;
          exifData2[tag.tag_name] = value;
        } catch (e) {
          exifData2[tag.tag_name] = tag.tag_value;
        }
      }
    });
    return exifData2;
  };
  const exifData = parseExifData(file.tags || []);
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-4", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-3 bg-secondary-bg rounded", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-sm font-medium mb-2", children: "📊 基本信息" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-1 text-xs", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-secondary", children: "文件名:" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium truncate ml-2", title: file.file_name, children: file.file_name })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-secondary", children: "尺寸:" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium", children: file.width && file.height ? `${file.width} × ${file.height}` : "未知" })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-secondary", children: "大小:" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium", children: formatFileSize(file.file_size) })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-secondary", children: "格式:" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium", children: file.file_type || "未知" })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-secondary", children: "创建时间:" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium", children: formatDate(file.created_at) })
        ] }),
        file.taken_at && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-secondary", children: "拍摄时间:" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium", children: formatDate(file.taken_at) })
        ] })
      ] })
    ] }),
    Object.keys(exifData).length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-3 bg-secondary-bg rounded", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-sm font-medium mb-2", children: "📷 EXIF数据" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "space-y-1 text-xs max-h-48 overflow-y-auto", children: Object.entries(exifData).map(([key, value]) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "text-secondary", children: [
          key,
          ":"
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium ml-2 truncate", title: String(value), children: String(value) })
      ] }, key)) })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-3 bg-secondary-bg rounded", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-sm font-medium mb-2", children: "🔧 技术信息" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-1 text-xs", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-secondary", children: "文件ID:" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium", children: file.id })
        ] }),
        file.width && file.height && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-secondary", children: "像素总数:" }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "font-medium", children: [
            (file.width * file.height).toLocaleString(),
            " 像素"
          ] })
        ] }),
        file.file_path && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-1", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-secondary", children: "存储方式:" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium text-xs", children: file.file_path.includes("uploads") ? "复制到案例目录" : "记录原始路径" })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-secondary", children: "文件路径:" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium text-xs truncate ml-2", title: file.file_path, children: file.file_path.split("\\").pop() || file.file_path.split("/").pop() })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-secondary", children: "完整路径:" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium text-xs truncate ml-2", title: file.file_path, children: file.file_path.length > 40 ? "..." + file.file_path.slice(-40) : file.file_path })
          ] })
        ] }),
        file.cluster_id && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-secondary", children: "聚类ID:" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium", children: file.cluster_id })
        ] })
      ] })
    ] }),
    file.tags && file.tags.length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-3 bg-secondary-bg rounded", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-sm font-medium mb-2", children: "🏷️ 标签信息" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "space-y-2 max-h-32 overflow-y-auto", children: file.tags.filter((tag) => tag.tag_category !== "exif" && tag.tag_category !== "metadata").map((tag, index) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: `px-2 py-1 text-xs rounded ${tag.tag_category === "user" ? "bg-blue-600 text-white" : "bg-gray-600 text-white"}`, children: tag.tag_name }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-xs text-secondary", children: tag.tag_category })
      ] }, index)) })
    ] })
  ] });
};
const FileCardImage = ({ src, alt, onError }) => {
  const [imageLoaded, setImageLoaded] = reactExports.useState(false);
  const [imageError, setImageError] = reactExports.useState(false);
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "aspect-square bg-secondary-bg flex items-center justify-center relative overflow-hidden", children: [
    !imageError && /* @__PURE__ */ jsxRuntimeExports.jsx(
      "img",
      {
        src,
        alt,
        className: "w-full h-full object-cover transition-transform duration-200 group-hover:scale-105",
        onError: (e) => {
          console.error("🖼️ 图片加载失败:", {
            src,
            alt,
            fileName: alt,
            error: e
          });
          setImageError(true);
          setImageLoaded(false);
          onError == null ? void 0 : onError();
        },
        onLoad: () => {
          console.log("✅ 图片加载成功:", { src, alt });
          setImageLoaded(true);
          setImageError(false);
        }
      }
    ),
    imageError && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "w-full h-full flex items-center justify-center flex-col bg-secondary-bg", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-4xl mb-2", children: "🖼️" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-xs text-secondary text-center px-2", children: "缩略图加载失败" })
    ] })
  ] });
};
const FileCardInfo = ({
  file,
  showType = true,
  showSize = false
}) => {
  var _a, _b;
  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-3", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-sm font-medium text-primary truncate", title: file.file_name, children: file.file_name }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between mt-1", children: [
      showType && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-xs text-secondary", children: ((_b = (_a = file.file_type) == null ? void 0 : _a.split("/")[1]) == null ? void 0 : _b.toUpperCase()) || "FILE" }),
      showSize && file.file_size && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-xs text-secondary", children: formatFileSize(file.file_size) })
    ] }),
    file.width && file.height && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-xs text-tertiary mt-1", children: [
      file.width,
      " × ",
      file.height
    ] })
  ] });
};
const FileCardBadge = ({
  type = "selected",
  children
}) => {
  const badgeStyles = {
    selected: "bg-highlight-border text-white",
    processing: "bg-yellow-500 text-white",
    error: "bg-red-500 text-white",
    new: "bg-green-500 text-white"
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: cn(
    "absolute top-2 right-2 px-2 py-1 rounded-full text-xs font-medium",
    badgeStyles[type]
  ), children: children || (type === "selected" ? "✓" : type) });
};
const FileCardActions = ({
  onPreview,
  onEdit,
  onDelete,
  onDownload
}) => {
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2", children: [
    onPreview && /* @__PURE__ */ jsxRuntimeExports.jsx(
      "button",
      {
        onClick: (e) => {
          e.stopPropagation();
          onPreview();
        },
        className: "p-2 bg-white rounded-full hover:bg-gray-100 transition-colors",
        title: "预览",
        children: "👁️"
      }
    ),
    onEdit && /* @__PURE__ */ jsxRuntimeExports.jsx(
      "button",
      {
        onClick: (e) => {
          e.stopPropagation();
          onEdit();
        },
        className: "p-2 bg-white rounded-full hover:bg-gray-100 transition-colors",
        title: "编辑",
        children: "✏️"
      }
    ),
    onDownload && /* @__PURE__ */ jsxRuntimeExports.jsx(
      "button",
      {
        onClick: (e) => {
          e.stopPropagation();
          onDownload();
        },
        className: "p-2 bg-white rounded-full hover:bg-gray-100 transition-colors",
        title: "下载",
        children: "⬇️"
      }
    ),
    onDelete && /* @__PURE__ */ jsxRuntimeExports.jsx(
      "button",
      {
        onClick: (e) => {
          e.stopPropagation();
          onDelete();
        },
        className: "p-2 bg-white rounded-full hover:bg-gray-100 transition-colors",
        title: "删除",
        children: "🗑️"
      }
    )
  ] }) });
};
const FileCard = ({
  file,
  selected = false,
  showActions = false,
  showType = true,
  showSize = false,
  getImageUrl,
  onSelect,
  onDoubleClick,
  onPreview,
  onEdit,
  onDelete,
  onDownload,
  className
}) => {
  const handleClick = (e) => {
    e.preventDefault();
    onSelect == null ? void 0 : onSelect(file);
  };
  const handleDoubleClick = (e) => {
    e.preventDefault();
    onDoubleClick == null ? void 0 : onDoubleClick(file);
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(
    "div",
    {
      className: cn(
        "group relative bg-secondary-bg rounded-lg overflow-hidden cursor-pointer transition-all duration-200",
        "hover:bg-secondary-bg-hover hover:shadow-md",
        selected && "ring-2 ring-highlight-border shadow-lg",
        className
      ),
      onClick: handleClick,
      onDoubleClick: handleDoubleClick,
      "data-file-id": file.id,
      children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          FileCardImage,
          {
            src: getImageUrl(file),
            alt: file.file_name
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          FileCardInfo,
          {
            file,
            showType,
            showSize
          }
        ),
        selected && /* @__PURE__ */ jsxRuntimeExports.jsx(FileCardBadge, { type: "selected" }),
        showActions && /* @__PURE__ */ jsxRuntimeExports.jsx(
          FileCardActions,
          {
            onPreview: () => onPreview == null ? void 0 : onPreview(file),
            onEdit: () => onEdit == null ? void 0 : onEdit(file),
            onDelete: () => onDelete == null ? void 0 : onDelete(file),
            onDownload: () => onDownload == null ? void 0 : onDownload(file)
          }
        )
      ]
    }
  );
};
const ElectronMainPageSimple = () => {
  var _a;
  const [catalogWidth, setCatalogWidth] = reactExports.useState(280);
  const [infoWidth, setInfoWidth] = reactExports.useState(320);
  const [workbenchVisible, setWorkbenchVisible] = reactExports.useState(true);
  const [showUploadModal, setShowUploadModal] = reactExports.useState(false);
  const { data: cases = [], isLoading: casesLoading } = useCases();
  const { addNotification } = useAppStore();
  const [selectedCaseId, setSelectedCaseId] = reactExports.useState();
  const { data: selectedCase } = useCase(selectedCaseId);
  const selectedCaseIdNumber = selectedCaseId ? Number(selectedCaseId) : void 0;
  const {
    data: caseFiles,
    isLoading: filesLoading,
    error: filesError
  } = useCaseFiles(selectedCaseIdNumber);
  const caseFilesArray = caseFiles || [];
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [selectedFiles, setSelectedFiles] = reactExports.useState(/* @__PURE__ */ new Set());
  const [batchMode, setBatchMode] = reactExports.useState(false);
  const [currentImage, setCurrentImage] = reactExports.useState(null);
  const [showImageModal, setShowImageModal] = reactExports.useState(false);
  const [useAdvancedSearch, setUseAdvancedSearch] = reactExports.useState(false);
  const [currentInfoFile, setCurrentInfoFile] = reactExports.useState(null);
  const queryClient = useQueryClient();
  const {
    data: searchResults,
    isLoading: searchLoading,
    error: searchError
  } = useQuery({
    queryKey: ["search", selectedCaseIdNumber, searchTerm],
    queryFn: () => searchApi.searchFiles(selectedCaseIdNumber, {
      q: searchTerm,
      limit: 200
    }),
    enabled: !!selectedCaseIdNumber && !!searchTerm && searchTerm.length > 0,
    staleTime: 1e4
    // 10秒内不重新搜索
  });
  const filteredFiles = reactExports.useMemo(() => {
    if (searchTerm && searchResults) {
      return searchResults.files.map((file) => ({
        id: file.id,
        file_name: file.file_name,
        file_path: file.file_path,
        file_type: file.file_type,
        thumbnail_small_path: file.thumbnail_small_path,
        width: file.width,
        height: file.height,
        quality_score: file.quality_score,
        created_at: file.created_at,
        tags: file.tags
      }));
    } else if (searchTerm && !searchLoading) {
      return caseFilesArray.filter(
        (file) => file.file_name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    } else {
      return caseFilesArray;
    }
  }, [caseFilesArray, searchTerm, searchResults, searchLoading]);
  const handleFileSelect = (fileId) => {
    const newSelected = new Set(selectedFiles);
    if (newSelected.has(fileId)) {
      newSelected.delete(fileId);
    } else {
      newSelected.add(fileId);
    }
    setSelectedFiles(newSelected);
  };
  const handleImageClick = (file) => {
    if (batchMode) {
      handleFileSelect(file.id);
    } else {
      setCurrentInfoFile(file);
    }
  };
  const handleImageDoubleClick = (file) => {
    if (!batchMode) {
      setCurrentImage(file);
      setShowImageModal(true);
    }
  };
  const getImageUrl = (file) => {
    if (file.thumbnail_small_path) {
      const normalizedPath = file.thumbnail_small_path.replace(/\\/g, "/");
      const fileUrl = `file:///${normalizedPath}`;
      console.log("🖼️ 生成缩略图URL:", {
        originalPath: file.thumbnail_small_path,
        normalizedPath,
        fileUrl,
        fileName: file.file_name
      });
      return fileUrl;
    }
    const apiUrl = `http://localhost:8000/api/v1/cases/${selectedCaseId}/files/${file.id}/thumbnail`;
    console.log("🌐 使用API缩略图URL:", apiUrl);
    return apiUrl;
  };
  const getOriginalImageUrl = (file) => {
    if (file.file_path) {
      const normalizedPath = file.file_path.replace(/\\/g, "/");
      return `file:///${normalizedPath}`;
    }
    return `http://localhost:8000/api/v1/cases/${selectedCaseId}/files/${file.id}/view`;
  };
  React.useEffect(() => {
    console.log("📁 ElectronMainPageSimple Files Debug:", {
      selectedCaseId,
      filesLoading,
      filesError: filesError == null ? void 0 : filesError.message,
      caseFilesRaw: caseFiles,
      caseFilesCount: caseFilesArray.length,
      filteredFilesCount: filteredFiles.length,
      firstFile: caseFilesArray[0],
      lastFile: caseFilesArray[caseFilesArray.length - 1]
    });
  }, [selectedCaseId, filesLoading, filesError, caseFiles, caseFilesArray.length, filteredFiles.length]);
  console.log("🔄 ElectronMainPageSimple 渲染状态:", {
    selectedCaseId,
    selectedCaseIdNumber,
    filesLoading,
    filesError: filesError == null ? void 0 : filesError.message,
    caseFilesRaw: caseFiles,
    caseFilesArrayLength: caseFilesArray.length,
    filteredFilesLength: filteredFiles.length,
    renderTime: (/* @__PURE__ */ new Date()).toISOString()
  });
  if (selectedCaseIdNumber) {
    console.log("🔑 查询键信息:", {
      queryKey: ["cases", selectedCaseIdNumber, "files"],
      expectedKey: queryKeys.caseFiles(selectedCaseIdNumber)
    });
  }
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "h-screen w-screen overflow-hidden flex bg-main text-primary", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      "div",
      {
        className: "bg-sidebar border-r border-secondary-bg flex-shrink-0",
        style: { width: `${catalogWidth}px` },
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "h-full flex flex-col p-4", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex items-center justify-between mb-4", children: /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-lg font-semibold", children: "目录栏" }) }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mb-4", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-xs font-medium text-secondary mb-2", children: "档案库" }),
            casesLoading ? /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-2 bg-secondary-bg rounded text-sm text-secondary", children: "加载案例中..." }) : /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-2", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-2 bg-secondary-bg rounded", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-sm font-medium", children: selectedCase ? selectedCase.case_name : "请选择案例" }),
                (selectedCase == null ? void 0 : selectedCase.description) && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-xs text-secondary mt-1", children: selectedCase.description })
              ] }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "max-h-48 overflow-y-auto space-y-1", children: cases.map((caseItem) => /* @__PURE__ */ jsxRuntimeExports.jsx(
                Button,
                {
                  onClick: () => setSelectedCaseId(caseItem.id),
                  variant: selectedCaseId === caseItem.id ? "primary" : "ghost",
                  size: "sm",
                  fullWidth: true,
                  className: "text-left justify-start",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
                    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "font-medium truncate", children: caseItem.case_name }),
                    caseItem.description && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-xs opacity-75 truncate mt-1", children: caseItem.description })
                  ] })
                },
                caseItem.id
              )) })
            ] })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mb-4", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-xs font-medium text-secondary mb-2", children: "文件操作" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "space-y-2", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
              Button,
              {
                onClick: () => setShowUploadModal(true),
                disabled: !selectedCaseId,
                className: "w-full",
                size: "sm",
                children: "📤 上传文件"
              }
            ) })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex-1 overflow-hidden", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-xs font-medium text-secondary mb-2", children: "标签筛选" }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-2 bg-secondary-bg rounded text-sm text-secondary", children: [
              "标签筛选功能",
              /* @__PURE__ */ jsxRuntimeExports.jsx("br", {}),
              /* @__PURE__ */ jsxRuntimeExports.jsx("small", { children: "（将集成原有标签系统）" })
            ] })
          ] })
        ] })
      }
    ),
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      "div",
      {
        className: "flex-1 flex flex-col",
        style: {
          height: workbenchVisible ? "calc(100% - 200px)" : "100%"
        },
        children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1", style: { backgroundColor: "#141414" }, children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "h-full flex flex-col p-4", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between mb-4", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-lg font-semibold", children: "画廊" }),
              selectedCase && /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "text-xs text-secondary", children: [
                selectedCase.case_name,
                " (",
                filteredFiles.length,
                " 个文件)"
              ] })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
              selectedCaseId && /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx(
                  SearchBox,
                  {
                    value: searchTerm,
                    onChange: setSearchTerm,
                    placeholder: searchLoading ? "搜索中..." : "搜索文件...",
                    size: "sm",
                    className: "w-48",
                    disabled: searchLoading
                  }
                ),
                /* @__PURE__ */ jsxRuntimeExports.jsx(
                  Button,
                  {
                    onClick: () => setBatchMode(!batchMode),
                    variant: batchMode ? "warning" : "secondary",
                    size: "xs",
                    children: batchMode ? "退出批量" : "批量选择"
                  }
                )
              ] }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                Button,
                {
                  onClick: () => setShowUploadModal(true),
                  disabled: !selectedCaseId,
                  size: "sm",
                  variant: "primary",
                  children: "📤 上传"
                }
              )
            ] })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 bg-secondary-bg rounded p-4 overflow-y-auto overflow-x-hidden", children: !selectedCaseId ? /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "h-full flex items-center justify-center text-center", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-4xl mb-4", children: "📁" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-lg font-medium mb-2", children: "请选择案例" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-sm text-secondary", children: "从左侧目录栏选择一个案例来查看其文件" })
          ] }) }) : filesLoading || searchLoading ? /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "h-full flex items-center justify-center text-center", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4 mx-auto" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-sm text-secondary", children: searchLoading ? "搜索中..." : "加载文件中..." })
          ] }) }) : searchError ? /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "h-full flex items-center justify-center text-center", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-4xl mb-4", children: "❌" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-lg font-medium mb-2", children: "搜索失败" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-sm text-secondary mb-4", children: handleApiError(searchError) }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              Button,
              {
                onClick: () => setSearchTerm(""),
                variant: "ghost",
                size: "sm",
                children: "清除搜索"
              }
            )
          ] }) }) : filteredFiles.length === 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "h-full flex items-center justify-center text-center", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-4xl mb-4", children: "📄" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-lg font-medium mb-2", children: searchTerm ? "没有找到匹配的文件" : "暂无文件" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-sm text-secondary", children: searchTerm ? "尝试使用不同的关键词搜索" : "这个案例中还没有添加任何文件" }),
            searchTerm && /* @__PURE__ */ jsxRuntimeExports.jsx(
              Button,
              {
                onClick: () => setSearchTerm(""),
                variant: "ghost",
                size: "sm",
                className: "mt-2",
                children: "清除搜索"
              }
            )
          ] }) }) : /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            searchTerm && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-sm text-blue-800", children: [
                '🔍 搜索 "',
                searchTerm,
                '" 找到 ',
                filteredFiles.length,
                " 个文件",
                searchResults && /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "ml-2 text-blue-600", children: [
                  "(共 ",
                  searchResults.total_count,
                  " 个结果)"
                ] })
              ] }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                Button,
                {
                  onClick: () => setSearchTerm(""),
                  variant: "ghost",
                  size: "xs",
                  className: "text-blue-600 hover:text-blue-800",
                  children: "清除"
                }
              )
            ] }) }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4", children: filteredFiles.map((file) => {
              console.log("🖼️ 渲染文件:", {
                id: file.id,
                fileName: file.file_name,
                thumbnailPath: file.thumbnail_small_path,
                imageUrl: getImageUrl(file)
              });
              return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "relative", children: [
                batchMode && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "absolute top-2 left-2 z-10", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                  "input",
                  {
                    type: "checkbox",
                    checked: selectedFiles.has(file.id),
                    onChange: (e) => {
                      e.stopPropagation();
                      handleFileSelect(file.id);
                    },
                    className: "w-4 h-4 text-attention-border bg-main border-secondary-bg rounded"
                  }
                ) }),
                /* @__PURE__ */ jsxRuntimeExports.jsx(
                  FileCard,
                  {
                    file,
                    selected: batchMode ? selectedFiles.has(file.id) : (currentInfoFile == null ? void 0 : currentInfoFile.id) === file.id,
                    showType: true,
                    showSize: false,
                    getImageUrl,
                    onSelect: handleImageClick,
                    onDoubleClick: handleImageDoubleClick,
                    className: cn(
                      "transition-all duration-200",
                      "hover:shadow-lg hover:scale-105"
                    )
                  }
                )
              ] }, file.id);
            }) })
          ] }) }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between mt-4 pt-2 border-t border-secondary-bg", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                Button,
                {
                  onClick: () => {
                    console.log("Toggling workbench, current:", workbenchVisible);
                    setWorkbenchVisible(!workbenchVisible);
                  },
                  variant: "secondary",
                  size: "xs",
                  children: workbenchVisible ? "收起工作台" : "展开工作台"
                }
              ),
              batchMode && selectedFiles.size > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
                /* @__PURE__ */ jsxRuntimeExports.jsxs(
                  DangerButton,
                  {
                    onClick: () => {
                      console.log("批量删除文件:", Array.from(selectedFiles));
                      addNotification({
                        type: "info",
                        message: `选中了 ${selectedFiles.size} 个文件`
                      });
                    },
                    size: "xs",
                    children: [
                      "删除选中 (",
                      selectedFiles.size,
                      ")"
                    ]
                  }
                ),
                /* @__PURE__ */ jsxRuntimeExports.jsx(
                  PrimaryButton,
                  {
                    onClick: () => {
                      console.log("移动到工作台:", Array.from(selectedFiles));
                      addNotification({
                        type: "info",
                        message: `将 ${selectedFiles.size} 个文件移动到工作台`
                      });
                    },
                    size: "xs",
                    children: "移动到工作台"
                  }
                )
              ] })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-xs text-secondary", children: selectedCaseId && filteredFiles.length > 0 ? `${filteredFiles.length} 个文件${batchMode && selectedFiles.size > 0 ? ` (已选择 ${selectedFiles.size} 个)` : ""}${currentInfoFile ? ` | 已选中: ${currentInfoFile.file_name}` : " | 单击选择，双击预览"}` : "画廊视图" })
          ] })
        ] }) })
      }
    ),
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      "div",
      {
        className: "bg-sidebar border-l border-secondary-bg flex-shrink-0",
        style: { width: `${infoWidth}px` },
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "h-full flex flex-col p-4", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between mb-4", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-lg font-semibold", children: "信息栏" }),
            currentInfoFile && /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-xs text-secondary", children: "已选择文件" })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 overflow-y-auto", children: /* @__PURE__ */ jsxRuntimeExports.jsx(FileMetadataPanel, { file: currentInfoFile }) }),
          currentInfoFile && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mt-4 pt-4 border-t border-secondary-bg space-y-2", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              PrimaryButton,
              {
                onClick: () => {
                  console.log("编辑标签:", currentInfoFile.id);
                },
                fullWidth: true,
                icon: "🏷️",
                children: "编辑标签"
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              SecondaryButton,
              {
                onClick: () => {
                  console.log("编辑文件详情:", currentInfoFile.id);
                },
                fullWidth: true,
                icon: "✏️",
                children: "编辑详情"
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              Button,
              {
                onClick: () => {
                  setCurrentImage(currentInfoFile);
                  setShowImageModal(true);
                },
                variant: "success",
                fullWidth: true,
                icon: "👁️",
                children: "预览文件"
              }
            )
          ] }),
          !currentInfoFile && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-4", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-3 bg-secondary-bg rounded", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-sm font-medium mb-2", children: "🏷️ 研究者标签规则" }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-xs text-secondary", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: "文件名生成标签" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mt-1", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                  Button,
                  {
                    variant: "ghost",
                    size: "xs",
                    onClick: () => {
                      if (selectedCaseId) {
                        window.location.hash = `#/cases/${selectedCaseId}/rules`;
                      }
                    },
                    children: "配置规则"
                  }
                ) })
              ] })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-3 bg-secondary-bg rounded", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-sm font-medium mb-2", children: "🤖 计算机视觉规则" }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-xs text-secondary", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: "质量评分映射" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mt-1", children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { variant: "ghost", size: "xs", children: "配置映射" }) })
              ] })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-3 bg-secondary-bg rounded", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-sm font-medium mb-2", children: "🧠 AI分析" }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-xs text-secondary", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: "模型配置和向量提取" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mt-1", children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { variant: "ghost", size: "xs", children: "配置模型" }) })
              ] })
            ] })
          ] })
        ] })
      }
    ),
    workbenchVisible && /* @__PURE__ */ jsxRuntimeExports.jsx(
      "div",
      {
        className: "fixed bottom-0 border-t border-secondary-bg z-40",
        style: {
          backgroundColor: "#141414",
          height: "200px",
          left: `${catalogWidth}px`,
          right: `${infoWidth}px`
        },
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "h-full flex flex-col", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between p-3 border-b border-secondary-bg bg-secondary-bg", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-3", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-sm font-semibold", children: "工作台" }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-1", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { variant: "warning", size: "xs", children: "剪贴板" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { variant: "ghost", size: "xs", children: "图像簇整理" })
              ] })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-xs text-secondary", children: "拖拽文件到此处进行整理" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                Button,
                {
                  onClick: () => {
                    console.log("Workbench close button clicked");
                    setWorkbenchVisible(false);
                  },
                  variant: "ghost",
                  size: "xs",
                  title: "收起工作台",
                  children: "▼"
                }
              )
            ] })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 p-4", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "h-full bg-secondary-bg rounded-lg border-2 border-dashed border-secondary-bg-hover flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center text-secondary", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-3xl mb-2", children: "📋" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-sm font-medium mb-1", children: "剪贴板工作区" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-xs", children: "拖拽图片到这里进行批量操作和整理" })
          ] }) }) })
        ] })
      }
    ),
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      Modal,
      {
        isOpen: showUploadModal,
        onClose: () => setShowUploadModal(false),
        title: "文件上传",
        size: "lg",
        children: selectedCaseId && /* @__PURE__ */ jsxRuntimeExports.jsx(
          FileUpload,
          {
            onUpload: (files) => __async(null, null, function* () {
              try {
                const result = yield filesApi.uploadFiles(
                  Number(selectedCaseId),
                  files,
                  (fileId, progress) => {
                    console.log(`文件 ${progress.fileName}: ${progress.progress}% (${progress.status})`);
                  }
                );
                yield invalidateFileRelatedCaches(queryClient, selectedCaseIdNumber);
                addNotification({
                  type: "success",
                  title: "文件上传完成",
                  message: `成功上传 ${result.successful.length} 个文件${result.failed.length > 0 ? `，${result.failed.length} 个文件失败` : ""}`
                });
                if (result.failed.length > 0) {
                  console.error("上传失败的文件:", result.failed);
                }
                setShowUploadModal(false);
              } catch (error) {
                addNotification({
                  type: "error",
                  title: "文件上传失败",
                  message: handleApiError(error)
                });
              }
            }),
            maxFiles: 20,
            disabled: false
          }
        )
      }
    ),
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      Modal,
      {
        isOpen: showImageModal,
        onClose: () => setShowImageModal(false),
        title: (currentImage == null ? void 0 : currentImage.file_name) || "图片预览",
        size: "xl",
        children: currentImage && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col h-full", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 flex items-center justify-center bg-black rounded-lg overflow-hidden", children: ((_a = currentImage.file_type) == null ? void 0 : _a.startsWith("image/")) ? /* @__PURE__ */ jsxRuntimeExports.jsx(
            "img",
            {
              src: getOriginalImageUrl(currentImage),
              alt: currentImage.file_name,
              className: "max-w-full max-h-full object-contain"
            }
          ) : /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center text-secondary", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-6xl mb-4", children: "📄" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: "此文件类型不支持预览" })
          ] }) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mt-4 p-4 bg-secondary-bg rounded-lg", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid grid-cols-2 gap-4 text-sm", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-secondary", children: "文件名：" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium", children: currentImage.file_name })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-secondary", children: "文件类型：" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium", children: currentImage.file_type || "未知" })
            ] }),
            currentImage.width && currentImage.height && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-secondary", children: "尺寸：" }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "font-medium", children: [
                currentImage.width,
                " × ",
                currentImage.height
              ] })
            ] }),
            currentImage.file_size && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-secondary", children: "大小：" }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "font-medium", children: [
                (currentImage.file_size / 1024 / 1024).toFixed(2),
                " MB"
              ] })
            ] })
          ] }) })
        ] })
      }
    )
  ] });
};
export {
  ElectronMainPageSimple as default
};
