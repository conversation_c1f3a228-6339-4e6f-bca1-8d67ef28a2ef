import * as core_dist_types from 'storybook/internal/types';

interface TestParameters {
    /**
     * Test addon configuration
     *
     * @see https://storybook.js.org/docs/next/writing-tests/integrations/vitest-addon
     */
    test: {
        /** Ignore unhandled errors during test execution */
        dangerouslyIgnoreUnhandledErrors?: boolean;
        /** Whether to throw exceptions coming from the play function */
        throwPlayFunctionExceptions?: boolean;
    };
}

declare const _default: () => core_dist_types.ProjectAnnotations<core_dist_types.Renderer>;

export { TestParameters, _default as default };
