# 标签管理页面 React 组件化分析

## 🎯 组件层级结构设计

基于现有的 `tag-management.html` 和 `tag-management.js`，我们将其拆解为以下React组件层级：

```
TagManagementPage (页面级根组件)
├── TagPanel (左侧标签面板)
│   ├── TagPanelHeader
│   │   ├── BackButton
│   │   ├── RefreshButton
│   │   └── CaseInfo
│   ├── TagSearchInput
│   ├── TagStatistics
│   ├── CustomTagSection
│   │   ├── CustomTagHeader
│   │   ├── AddCustomTagButton
│   │   └── CustomTagList
│   │       └── CustomTagItem (循环渲染)
│   └── SystemTagSection
│       └── TagCategory (可折叠的分类)
│           ├── TagCategoryHeader
│           └── TagList
│               └── TagItem (循环渲染)
├── PanelDivider (分栏调整器)
└── GalleryPanel (右侧画廊面板)
    ├── GalleryHeader
    │   ├── GalleryTitle
    │   ├── FileCounter
    │   └── SelectionControls
    │       ├── SelectModeButton
    │       └── SelectionActions
    ├── BatchActionsBar
    │   ├── BatchTagSelect
    │   ├── BatchAddTagButton
    │   └── BatchRemoveTagButton
    ├── GalleryGrid
    │   └── ImageThumbnail (循环渲染)
    ├── EmptyState
    └── LoadingState

全局组件:
├── ImageDetailModal (图片详情模态框)
│   ├── ImageViewer
│   ├── ImageInfoPanel
│   │   ├── ImageTags
│   │   └── AddTagButton
│   └── CloseButton
├── AddTagModal (添加标签模态框)
├── NotificationContainer (通知容器)
└── ConfirmDialog (确认对话框)
```

## 📊 状态管理策略

### TanStack Query (服务端状态)

```typescript
// 标签相关查询
useTags(caseId) - 获取标签树数据
useCustomTags(caseId) - 获取自定义标签
useFilesByTag(caseId, tagFilter) - 根据标签筛选文件
useCaseInfo(caseId) - 获取案例信息

// 标签相关变更
useCreateCustomTag() - 创建自定义标签
useUpdateCustomTag() - 更新自定义标签
useDeleteCustomTag() - 删除自定义标签
useAddTagToFiles() - 为文件添加标签
useRemoveTagFromFiles() - 从文件移除标签
```

### Zustand (客户端状态)

```typescript
interface TagManagementState {
  // UI 状态
  searchQuery: string;
  selectedTags: Set<string>;
  expandedCategories: Set<string>;
  
  // 画廊状态
  selectedFiles: Set<number>;
  isSelectionMode: boolean;
  currentImageId: number | null;
  
  // 面板状态
  panelWidth: number;
  isResizing: boolean;
  
  // 模态框状态
  showImageModal: boolean;
  showAddTagModal: boolean;
  
  // Actions
  setSearchQuery: (query: string) => void;
  toggleTagSelection: (tagId: string) => void;
  toggleCategoryExpansion: (category: string) => void;
  toggleFileSelection: (fileId: number) => void;
  enterSelectionMode: () => void;
  exitSelectionMode: () => void;
  // ... 其他actions
}
```

## 🔄 数据流设计

### 1. 标签选择流程
```
用户点击标签 → 更新selectedTags状态 → 触发useFilesByTag查询 → 更新画廊显示
```

### 2. 文件选择流程
```
用户点击文件 → 更新selectedFiles状态 → 显示批量操作栏 → 执行批量操作
```

### 3. 搜索流程
```
用户输入搜索 → 更新searchQuery状态 → 过滤标签显示 → 实时搜索结果
```

## 🎨 组件设计原则

### 1. 单一职责
- 每个组件只负责一个特定功能
- 复杂组件拆分为更小的子组件

### 2. 数据向下，事件向上
- 父组件传递数据给子组件
- 子组件通过回调函数向父组件传递事件

### 3. 状态提升
- 共享状态提升到最近的公共父组件
- 使用Zustand管理跨组件状态

### 4. 性能优化
- 使用React.memo优化重渲染
- 使用useMemo和useCallback优化计算
- 虚拟滚动处理大量数据

## 📝 实现优先级

### Phase 1: 核心结构
1. TagManagementPage - 页面框架
2. TagPanel - 左侧面板基础结构
3. GalleryPanel - 右侧面板基础结构
4. 基础的状态管理设置

### Phase 2: 标签功能
1. TagSearchInput - 搜索功能
2. CustomTagSection - 自定义标签管理
3. SystemTagSection - 系统标签显示
4. 标签选择和过滤逻辑

### Phase 3: 画廊功能
1. GalleryGrid - 图片网格显示
2. ImageThumbnail - 图片缩略图
3. SelectionControls - 选择模式
4. BatchActionsBar - 批量操作

### Phase 4: 高级功能
1. ImageDetailModal - 图片详情模态框
2. PanelDivider - 面板大小调整
3. 拖拽功能
4. 性能优化

## 🔧 技术实现要点

### 1. 响应式设计
- 使用Tailwind CSS的响应式类
- 支持不同屏幕尺寸的布局调整

### 2. 键盘快捷键
- Ctrl+F: 聚焦搜索框
- ESC: 关闭模态框/清除搜索
- 空格: 选择/取消选择文件

### 3. 无障碍支持
- 适当的ARIA标签
- 键盘导航支持
- 屏幕阅读器友好

### 4. 错误处理
- 网络错误的优雅降级
- 用户友好的错误提示
- 重试机制
