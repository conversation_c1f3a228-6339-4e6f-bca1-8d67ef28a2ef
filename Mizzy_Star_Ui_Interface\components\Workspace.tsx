import React, { useState } from 'react';
import { 
  ChevronDown, 
  ChevronUp, 
  Settings, 
  X, 
  Minimize2,
  Code,
  BarChart3,
  Brain,
  Clipboard,
  Layers,
  Search,
  Download,
  Upload,
  Zap,
  Filter,
  FileText,
  Image,
  Palette,
  Crop,
  RotateCcw,
  Scissors,
  Database,
  Tags,
  Archive,
  Target,
  Workflow,
  History,
  Play,
  Pause,
  CheckSquare,
  RefreshCw,
  Trash2,
  Copy,
  Move,
  Eye,
  Share2,
  Lock,
  Unlock,
  Calendar,
  Clock,
  Gauge
} from 'lucide-react';
import { Button } from './ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from './ui/dropdown-menu';
import { Input } from './ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Slider } from './ui/slider';
import { Checkbox } from './ui/checkbox';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { Switch } from './ui/switch';
import { Progress } from './ui/progress';
import { Badge } from './ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Separator } from './ui/separator';
import { ScrollArea } from './ui/scroll-area';

interface WorkspaceProps {
  onToggleWorkspace: () => void;
  onSwapGalleryWorkspace: () => void;
  galleryWorkspaceSwapped: boolean;
  selectedImages: string[];
  selectedTags: string[];
}

interface WorkspaceTab {
  id: string;
  title: string;
  icon: React.ComponentType;
  component: React.ComponentType<any>;
}

// 规则配置面板
const RuleConfiguration: React.FC = () => (
  <div className="p-4 h-full overflow-auto" style={{ color: 'var(--mizzy-content)' }}>
    <h3 className="mb-4" style={{ color: 'var(--mizzy-title)' }}>规则配置</h3>
    <div className="space-y-4">
      <div>
        <label className="block mb-2" style={{ color: 'var(--mizzy-icon)' }}>规则名称</label>
        <input 
          type="text" 
          className="w-full p-2 rounded border"
          style={{ 
            background: 'var(--mizzy-input)',
            borderColor: 'var(--mizzy-border)',
            color: 'var(--mizzy-content)'
          }}
          placeholder="输入规则名称"
        />
      </div>
      <div>
        <label className="block mb-2" style={{ color: 'var(--mizzy-icon)' }}>规则类型</label>
        <select 
          className="w-full p-2 rounded border"
          style={{ 
            background: 'var(--mizzy-input)',
            borderColor: 'var(--mizzy-border)',
            color: 'var(--mizzy-content)'
          }}
        >
          <option>文件名规则</option>
          <option>路径规则</option>
          <option>元数据规则</option>
          <option>内容规则</option>
        </select>
      </div>
      <div>
        <label className="block mb-2" style={{ color: 'var(--mizzy-icon)' }}>匹配条件</label>
        <textarea 
          className="w-full p-2 rounded border h-24"
          style={{ 
            background: 'var(--mizzy-input)',
            borderColor: 'var(--mizzy-border)',
            color: 'var(--mizzy-content)'
          }}
          placeholder="输入匹配条件或正则表达式"
        />
      </div>
      <div className="flex gap-2">
        <Button 
          className="flex-1"
          style={{ background: 'var(--mizzy-highlight)', color: 'white' }}
        >
          保存规则
        </Button>
        <Button variant="outline" className="flex-1">
          测试规则
        </Button>
      </div>
    </div>
  </div>
);

// 质量分析面板
const QualityAnalysis: React.FC = () => (
  <div className="p-4 h-full overflow-auto" style={{ color: 'var(--mizzy-content)' }}>
    <h3 className="mb-4" style={{ color: 'var(--mizzy-title)' }}>质量分析</h3>
    <div className="space-y-4">
      <div>
        <div className="flex justify-between mb-2">
          <span style={{ color: 'var(--mizzy-icon)' }}>清晰度权重</span>
          <span style={{ color: 'var(--mizzy-content)' }}>85%</span>
        </div>
        <input 
          type="range" 
          className="w-full" 
          min="0" 
          max="100" 
          defaultValue="85"
        />
      </div>
      <div>
        <div className="flex justify-between mb-2">
          <span style={{ color: 'var(--mizzy-icon)' }}>构图权重</span>
          <span style={{ color: 'var(--mizzy-content)' }}>70%</span>
        </div>
        <input 
          type="range" 
          className="w-full" 
          min="0" 
          max="100" 
          defaultValue="70"
        />
      </div>
      <div>
        <div className="flex justify-between mb-2">
          <span style={{ color: 'var(--mizzy-icon)' }}>色彩权重</span>
          <span style={{ color: 'var(--mizzy-content)' }}>60%</span>
        </div>
        <input 
          type="range" 
          className="w-full" 
          min="0" 
          max="100" 
          defaultValue="60"
        />
      </div>
      <div>
        <div className="flex justify-between mb-2">
          <span style={{ color: 'var(--mizzy-icon)' }}>噪点容忍度</span>
          <span style={{ color: 'var(--mizzy-content)' }}>30%</span>
        </div>
        <input 
          type="range" 
          className="w-full" 
          min="0" 
          max="100" 
          defaultValue="30"
        />
      </div>
      <div className="pt-4">
        <Button 
          className="w-full"
          style={{ background: 'var(--mizzy-highlight)', color: 'white' }}
        >
          重新计算质量分
        </Button>
      </div>
    </div>
  </div>
);

// AI分析面板
const AIAnalysis: React.FC = () => (
  <div className="p-4 h-full">
    <h3 className="mb-4" style={{ color: 'var(--mizzy-title)' }}>AI分析</h3>
    <div className="space-y-4">
      <div>
        <h4 className="mb-2" style={{ color: 'var(--mizzy-icon)' }}>内容识别设置</h4>
        <div className="space-y-2">
          <label className="flex items-center gap-2">
            <input type="checkbox" defaultChecked />
            <span style={{ color: 'var(--mizzy-content)' }}>识别人物</span>
          </label>
          <label className="flex items-center gap-2">
            <input type="checkbox" defaultChecked />
            <span style={{ color: 'var(--mizzy-content)' }}>识别物体</span>
          </label>
          <label className="flex items-center gap-2">
            <input type="checkbox" defaultChecked />
            <span style={{ color: 'var(--mizzy-content)' }}>识别场景</span>
          </label>
          <label className="flex items-center gap-2">
            <input type="checkbox" />
            <span style={{ color: 'var(--mizzy-content)' }}>情感分析</span>
          </label>
        </div>
      </div>
      
      <div>
        <h4 className="mb-2" style={{ color: 'var(--mizzy-icon)' }}>语义识别设置</h4>
        <div className="space-y-2">
          <label className="flex items-center gap-2">
            <input type="checkbox" defaultChecked />
            <span style={{ color: 'var(--mizzy-content)' }}>风格识别</span>
          </label>
          <label className="flex items-center gap-2">
            <input type="checkbox" defaultChecked />
            <span style={{ color: 'var(--mizzy-content)' }}>主题分析</span>
          </label>
          <label className="flex items-center gap-2">
            <input type="checkbox" />
            <span style={{ color: 'var(--mizzy-content)' }}>艺术流派识别</span>
          </label>
        </div>
      </div>

      <div className="pt-4 space-y-2">
        <Button 
          className="w-full"
          style={{ background: 'var(--mizzy-highlight)', color: 'white' }}
        >
          重新生成AI标签
        </Button>
        <Button variant="outline" className="w-full">
          导出AI分析结果
        </Button>
      </div>
    </div>
  </div>
);

// 剪贴板面板
const Clipboard: React.FC = () => (
  <div className="p-4 h-full">
    <h3 className="mb-4" style={{ color: 'var(--mizzy-title)' }}>剪贴板</h3>
    <div className="border-2 border-dashed rounded h-64 flex items-center justify-center"
         style={{ borderColor: 'var(--mizzy-border)' }}>
      <div className="text-center">
        <Clipboard className="h-8 w-8 mx-auto mb-2" style={{ color: 'var(--mizzy-icon)' }} />
        <p style={{ color: 'var(--mizzy-icon)' }}>
          拖拽图像到此处创建自由排布
        </p>
      </div>
    </div>
    <div className="mt-4 flex gap-2">
      <Button variant="outline" size="sm">网格排列</Button>
      <Button variant="outline" size="sm">自由排布</Button>
      <Button variant="outline" size="sm">清空剪贴板</Button>
    </div>
  </div>
);

// 图像簇整理面板
const ImageClustering: React.FC = () => (
  <div className="p-4 h-full">
    <h3 className="mb-4" style={{ color: 'var(--mizzy-title)' }}>图像簇整理</h3>
    <div className="space-y-4">
      <div>
        <h4 className="mb-2" style={{ color: 'var(--mizzy-icon)' }}>主图</h4>
        <div className="w-full h-32 border rounded flex items-center justify-center"
             style={{ borderColor: 'var(--mizzy-border)' }}>
          <span style={{ color: 'var(--mizzy-icon)' }}>拖拽设置主图</span>
        </div>
      </div>
      
      <div>
        <h4 className="mb-2" style={{ color: 'var(--mizzy-icon)' }}>相似图像</h4>
        <div className="grid grid-cols-4 gap-2">
          {[1, 2, 3, 4].map(i => (
            <div 
              key={i}
              className="aspect-square border rounded flex items-center justify-center"
              style={{ borderColor: 'var(--mizzy-border)' }}
            >
              <span className="text-xs" style={{ color: 'var(--mizzy-icon)' }}>
                {i}
              </span>
            </div>
          ))}
        </div>
      </div>

      <div>
        <h4 className="mb-2" style={{ color: 'var(--mizzy-icon)' }}>版本图像</h4>
        <div className="grid grid-cols-4 gap-2">
          {[1, 2].map(i => (
            <div 
              key={i}
              className="aspect-square border rounded flex items-center justify-center"
              style={{ borderColor: 'var(--mizzy-border)' }}
            >
              <span className="text-xs" style={{ color: 'var(--mizzy-icon)' }}>
                V{i}
              </span>
            </div>
          ))}
        </div>
      </div>

      <div className="flex gap-2">
        <Button 
          className="flex-1"
          style={{ background: 'var(--mizzy-highlight)', color: 'white' }}
        >
          保存图像簇
        </Button>
        <Button variant="outline" className="flex-1">
          解散图像簇
        </Button>
      </div>
    </div>
  </div>
);

// 批量操作面板
const BatchOperations: React.FC = () => {
  const [selectedOperation, setSelectedOperation] = useState('');
  const [processing, setProcessing] = useState(false);
  const [progress, setProgress] = useState(0);

  return (
    <ScrollArea className="h-full p-4">
      <h3 className="mb-4" style={{ color: 'var(--mizzy-title)' }}>批量操作</h3>
      
      <div className="space-y-4">
        {/* 操作选择 */}
        <Card>
          <CardHeader>
            <CardTitle style={{ color: 'var(--mizzy-title)' }}>选择操作</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Select value={selectedOperation} onValueChange={setSelectedOperation}>
              <SelectTrigger>
                <SelectValue placeholder="选择批量操作" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="tag">批量标记</SelectItem>
                <SelectItem value="move">批量移动</SelectItem>
                <SelectItem value="copy">批量复制</SelectItem>
                <SelectItem value="delete">批量删除</SelectItem>
                <SelectItem value="rename">批量重命名</SelectItem>
                <SelectItem value="resize">批量调整尺寸</SelectItem>
                <SelectItem value="format">批量格式转换</SelectItem>
                <SelectItem value="compress">批量压缩</SelectItem>
                <SelectItem value="metadata">批量元数据编辑</SelectItem>
              </SelectContent>
            </Select>
            
            {selectedOperation && (
              <div className="grid grid-cols-2 gap-2">
                <Button variant="outline" size="sm">
                  <Play className="h-4 w-4 mr-2" />
                  开始执行
                </Button>
                <Button variant="outline" size="sm">
                  <Pause className="h-4 w-4 mr-2" />
                  暂停
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 进度显示 */}
        {processing && (
          <Card>
            <CardHeader>
              <CardTitle style={{ color: 'var(--mizzy-title)' }}>处理进度</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span style={{ color: 'var(--mizzy-content)' }}>已处理: 45/128</span>
                  <span style={{ color: 'var(--mizzy-content)' }}>35%</span>
                </div>
                <Progress value={progress} className="w-full" />
                <div className="flex justify-between text-sm">
                  <span style={{ color: 'var(--mizzy-icon)' }}>预计剩余时间: 2分钟</span>
                  <span style={{ color: 'var(--mizzy-icon)' }}>速度: 12/秒</span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 操作配置 */}
        <Card>
          <CardHeader>
            <CardTitle style={{ color: 'var(--mizzy-title)' }}>操作配置</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="space-y-2">
              <Label style={{ color: 'var(--mizzy-icon)' }}>目标路径</Label>
              <div className="flex gap-2">
                <Input placeholder="选择目标文件夹" />
                <Button variant="outline" size="sm">浏览</Button>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label style={{ color: 'var(--mizzy-icon)' }}>命名模式</Label>
              <Input placeholder="如: IMG_{序号}_{日期}" />
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox id="backup" />
              <Label htmlFor="backup" style={{ color: 'var(--mizzy-content)' }}>创建备份</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox id="log" />
              <Label htmlFor="log" style={{ color: 'var(--mizzy-content)' }}>记录操作日志</Label>
            </div>
          </CardContent>
        </Card>

        {/* 快捷操作 */}
        <Card>
          <CardHeader>
            <CardTitle style={{ color: 'var(--mizzy-title)' }}>快捷操作</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-2">
              <Button variant="outline" size="sm">
                <Tags className="h-4 w-4 mr-2" />
                快速标记
              </Button>
              <Button variant="outline" size="sm">
                <Archive className="h-4 w-4 mr-2" />
                批量归档
              </Button>
              <Button variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                刷新缩略图
              </Button>
              <Button variant="outline" size="sm">
                <Database className="h-4 w-4 mr-2" />
                重建索引
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </ScrollArea>
  );
};

// 高级搜索面板
const AdvancedSearch: React.FC = () => {
  const [searchHistory, setSearchHistory] = useState([
    'tag:portrait AND color:blue',
    'size:>1920x1080 AND quality:>8',
    'date:2024-01-01..2024-12-31',
  ]);

  return (
    <ScrollArea className="h-full p-4">
      <h3 className="mb-4" style={{ color: 'var(--mizzy-title)' }}>高级搜索</h3>
      
      <div className="space-y-4">
        {/* 搜索构建器 */}
        <Card>
          <CardHeader>
            <CardTitle style={{ color: 'var(--mizzy-title)' }}>搜索构建器</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="space-y-2">
              <Label style={{ color: 'var(--mizzy-icon)' }}>搜索类型</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="选择搜索类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="text">文本搜索</SelectItem>
                  <SelectItem value="visual">视觉搜索</SelectItem>
                  <SelectItem value="similarity">相似度搜索</SelectItem>
                  <SelectItem value="metadata">元数据搜索</SelectItem>
                  <SelectItem value="advanced">高级查询</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label style={{ color: 'var(--mizzy-icon)' }}>查询语句</Label>
              <Textarea 
                placeholder="输入搜索条件，如: tag:portrait AND color:blue OR size:>1920x1080"
                className="min-h-[100px]"
              />
            </div>
            
            <div className="flex gap-2">
              <Button style={{ background: 'var(--mizzy-highlight)', color: 'white' }}>
                <Search className="h-4 w-4 mr-2" />
                搜索
              </Button>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                过滤器
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 搜索过滤器 */}
        <Card>
          <CardHeader>
            <CardTitle style={{ color: 'var(--mizzy-title)' }}>过滤器</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="space-y-2">
              <Label style={{ color: 'var(--mizzy-icon)' }}>文件类型</Label>
              <div className="flex flex-wrap gap-2">
                {['JPG', 'PNG', 'RAW', 'TIFF', 'PSD'].map(type => (
                  <Badge key={type} variant="outline" className="cursor-pointer">
                    {type}
                  </Badge>
                ))}
              </div>
            </div>
            
            <div className="space-y-2">
              <Label style={{ color: 'var(--mizzy-icon)' }}>尺寸范围</Label>
              <div className="grid grid-cols-2 gap-2">
                <Input placeholder="最小宽度" />
                <Input placeholder="最大宽度" />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label style={{ color: 'var(--mizzy-icon)' }}>质量分数</Label>
              <Slider defaultValue={[5]} max={10} step={0.1} />
            </div>
            
            <div className="space-y-2">
              <Label style={{ color: 'var(--mizzy-icon)' }}>创建时间</Label>
              <div className="grid grid-cols-2 gap-2">
                <Input type="date" />
                <Input type="date" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 搜索历史 */}
        <Card>
          <CardHeader>
            <CardTitle style={{ color: 'var(--mizzy-title)' }}>搜索历史</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {searchHistory.map((query, index) => (
                <div key={index} className="flex items-center justify-between p-2 border rounded"
                     style={{ borderColor: 'var(--mizzy-border)' }}>
                  <span className="font-mono text-sm" style={{ color: 'var(--mizzy-content)' }}>
                    {query}
                  </span>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="sm">
                      <History className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </ScrollArea>
  );
};

// 导出导入面板
const ExportImport: React.FC = () => {
  const [exportProgress, setExportProgress] = useState(0);
  const [isExporting, setIsExporting] = useState(false);

  return (
    <ScrollArea className="h-full p-4">
      <h3 className="mb-4" style={{ color: 'var(--mizzy-title)' }}>导出导入</h3>
      
      <div className="space-y-4">
        {/* 导出设置 */}
        <Card>
          <CardHeader>
            <CardTitle style={{ color: 'var(--mizzy-title)' }}>导出设置</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="space-y-2">
              <Label style={{ color: 'var(--mizzy-icon)' }}>导出类型</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="选择导出类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="images">图像文件</SelectItem>
                  <SelectItem value="metadata">元数据</SelectItem>
                  <SelectItem value="database">数据库</SelectItem>
                  <SelectItem value="report">分析报告</SelectItem>
                  <SelectItem value="catalog">目录清单</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label style={{ color: 'var(--mizzy-icon)' }}>导出格式</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="选择导出格式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="zip">ZIP压缩包</SelectItem>
                  <SelectItem value="json">JSON格式</SelectItem>
                  <SelectItem value="csv">CSV表格</SelectItem>
                  <SelectItem value="pdf">PDF报告</SelectItem>
                  <SelectItem value="excel">Excel表格</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label style={{ color: 'var(--mizzy-icon)' }}>压缩质量</Label>
              <Slider defaultValue={[85]} max={100} step={1} />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox id="include-metadata" />
                <Label htmlFor="include-metadata" style={{ color: 'var(--mizzy-content)' }}>包含元数据</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="include-tags" />
                <Label htmlFor="include-tags" style={{ color: 'var(--mizzy-content)' }}>包含标签</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="include-thumbnails" />
                <Label htmlFor="include-thumbnails" style={{ color: 'var(--mizzy-content)' }}>包含缩略图</Label>
              </div>
            </div>
            
            <Button 
              className="w-full"
              style={{ background: 'var(--mizzy-highlight)', color: 'white' }}
              onClick={() => setIsExporting(true)}
            >
              <Download className="h-4 w-4 mr-2" />
              开始导出
            </Button>
          </CardContent>
        </Card>

        {/* 导出进度 */}
        {isExporting && (
          <Card>
            <CardHeader>
              <CardTitle style={{ color: 'var(--mizzy-title)' }}>导出进度</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span style={{ color: 'var(--mizzy-content)' }}>正在导出...</span>
                  <span style={{ color: 'var(--mizzy-content)' }}>65%</span>
                </div>
                <Progress value={65} className="w-full" />
                <div className="text-sm" style={{ color: 'var(--mizzy-icon)' }}>
                  已处理 650/1000 个文件
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 导入设置 */}
        <Card>
          <CardHeader>
            <CardTitle style={{ color: 'var(--mizzy-title)' }}>导入设置</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="border-2 border-dashed rounded-lg p-8 text-center"
                 style={{ borderColor: 'var(--mizzy-border)' }}>
              <Upload className="h-12 w-12 mx-auto mb-4" style={{ color: 'var(--mizzy-icon)' }} />
              <p style={{ color: 'var(--mizzy-content)' }}>
                拖拽文件到此处或点击选择文件
              </p>
              <p className="text-sm mt-2" style={{ color: 'var(--mizzy-icon)' }}>
                支持图像文件、JSON、CSV、ZIP等格式
              </p>
            </div>
            
            <div className="space-y-2">
              <Label style={{ color: 'var(--mizzy-icon)' }}>导入选项</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox id="auto-tag" />
                  <Label htmlFor="auto-tag" style={{ color: 'var(--mizzy-content)' }}>自动标记</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="duplicate-check" />
                  <Label htmlFor="duplicate-check" style={{ color: 'var(--mizzy-content)' }}>检查重复</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="thumbnail-gen" />
                  <Label htmlFor="thumbnail-gen" style={{ color: 'var(--mizzy-content)' }}>生成缩略图</Label>
                </div>
              </div>
            </div>
            
            <Button variant="outline" className="w-full">
              <Upload className="h-4 w-4 mr-2" />
              选择文件
            </Button>
          </CardContent>
        </Card>
      </div>
    </ScrollArea>
  );
};

// 图像处理面板
const ImageProcessing: React.FC = () => {
  const [activeProcessor, setActiveProcessor] = useState('basic');

  return (
    <ScrollArea className="h-full p-4">
      <h3 className="mb-4" style={{ color: 'var(--mizzy-title)' }}>图像处理</h3>
      
      <div className="space-y-4">
        {/* 处理器选择 */}
        <Card>
          <CardHeader>
            <CardTitle style={{ color: 'var(--mizzy-title)' }}>处理器</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-2">
              <Button 
                variant={activeProcessor === 'basic' ? 'default' : 'outline'}
                onClick={() => setActiveProcessor('basic')}
              >
                基础调整
              </Button>
              <Button 
                variant={activeProcessor === 'advanced' ? 'default' : 'outline'}
                onClick={() => setActiveProcessor('advanced')}
              >
                高级处理
              </Button>
              <Button 
                variant={activeProcessor === 'filter' ? 'default' : 'outline'}
                onClick={() => setActiveProcessor('filter')}
              >
                滤镜效果
              </Button>
              <Button 
                variant={activeProcessor === 'batch' ? 'default' : 'outline'}
                onClick={() => setActiveProcessor('batch')}
              >
                批量处理
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 基础调整 */}
        {activeProcessor === 'basic' && (
          <Card>
            <CardHeader>
              <CardTitle style={{ color: 'var(--mizzy-title)' }}>基础调整</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2">
                <Label style={{ color: 'var(--mizzy-icon)' }}>亮度</Label>
                <Slider defaultValue={[50]} max={100} step={1} />
              </div>
              <div className="space-y-2">
                <Label style={{ color: 'var(--mizzy-icon)' }}>对比度</Label>
                <Slider defaultValue={[50]} max={100} step={1} />
              </div>
              <div className="space-y-2">
                <Label style={{ color: 'var(--mizzy-icon)' }}>饱和度</Label>
                <Slider defaultValue={[50]} max={100} step={1} />
              </div>
              <div className="space-y-2">
                <Label style={{ color: 'var(--mizzy-icon)' }}>锐化</Label>
                <Slider defaultValue={[0]} max={100} step={1} />
              </div>
              <div className="grid grid-cols-2 gap-2">
                <Button variant="outline">
                  <RotateCcw className="h-4 w-4 mr-2" />
                  重置
                </Button>
                <Button style={{ background: 'var(--mizzy-highlight)', color: 'white' }}>
                  应用
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 高级处理 */}
        {activeProcessor === 'advanced' && (
          <Card>
            <CardHeader>
              <CardTitle style={{ color: 'var(--mizzy-title)' }}>高级处理</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-2">
                <Button variant="outline">
                  <Crop className="h-4 w-4 mr-2" />
                  裁剪
                </Button>
                <Button variant="outline">
                  <RotateCcw className="h-4 w-4 mr-2" />
                  旋转
                </Button>
                <Button variant="outline">
                  <Palette className="h-4 w-4 mr-2" />
                  色彩校正
                </Button>
                <Button variant="outline">
                  <Zap className="h-4 w-4 mr-2" />
                  降噪
                </Button>
              </div>
              
              <Separator />
              
              <div className="space-y-2">
                <Label style={{ color: 'var(--mizzy-icon)' }}>输出尺寸</Label>
                <div className="grid grid-cols-2 gap-2">
                  <Input placeholder="宽度" />
                  <Input placeholder="高度" />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label style={{ color: 'var(--mizzy-icon)' }}>输出格式</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="选择格式" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="jpg">JPEG</SelectItem>
                    <SelectItem value="png">PNG</SelectItem>
                    <SelectItem value="webp">WebP</SelectItem>
                    <SelectItem value="tiff">TIFF</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 预览区域 */}
        <Card>
          <CardHeader>
            <CardTitle style={{ color: 'var(--mizzy-title)' }}>预览</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="aspect-video border rounded flex items-center justify-center"
                 style={{ borderColor: 'var(--mizzy-border)' }}>
              <div className="text-center">
                <Image className="h-12 w-12 mx-auto mb-2" style={{ color: 'var(--mizzy-icon)' }} />
                <p style={{ color: 'var(--mizzy-icon)' }}>
                  选择图像进行预览
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </ScrollArea>
  );
};

// 工作流管理面板
const WorkflowManager: React.FC = () => {
  const [workflows, setWorkflows] = useState([
    { id: 1, name: '标准导入流程', status: '活跃', steps: 5 },
    { id: 2, name: '质量检查流程', status: '暂停', steps: 3 },
    { id: 3, name: '批量处理流程', status: '运行中', steps: 7 },
  ]);

  return (
    <ScrollArea className="h-full p-4">
      <h3 className="mb-4" style={{ color: 'var(--mizzy-title)' }}>工作流管理</h3>
      
      <div className="space-y-4">
        {/* 工作流列表 */}
        <Card>
          <CardHeader>
            <CardTitle style={{ color: 'var(--mizzy-title)' }}>工作流列表</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {workflows.map((workflow) => (
                <div key={workflow.id} className="flex items-center justify-between p-3 border rounded"
                     style={{ borderColor: 'var(--mizzy-border)' }}>
                  <div>
                    <div className="flex items-center gap-2">
                      <h4 style={{ color: 'var(--mizzy-content)' }}>{workflow.name}</h4>
                      <Badge variant={workflow.status === '运行中' ? 'default' : 'secondary'}>
                        {workflow.status}
                      </Badge>
                    </div>
                    <p className="text-sm" style={{ color: 'var(--mizzy-icon)' }}>
                      {workflow.steps} 个步骤
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="sm">
                      <Play className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Settings className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 工作流构建器 */}
        <Card>
          <CardHeader>
            <CardTitle style={{ color: 'var(--mizzy-title)' }}>工作流构建器</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="space-y-2">
              <Label style={{ color: 'var(--mizzy-icon)' }}>工作流名称</Label>
              <Input placeholder="输入工作流名称" />
            </div>
            
            <div className="space-y-2">
              <Label style={{ color: 'var(--mizzy-icon)' }}>触发条件</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="选择触发条件" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="manual">手动触发</SelectItem>
                  <SelectItem value="import">导入文件时</SelectItem>
                  <SelectItem value="schedule">定时触发</SelectItem>
                  <SelectItem value="tag">标签变更时</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label style={{ color: 'var(--mizzy-icon)' }}>操作步骤</Label>
              <div className="space-y-2">
                {['自动标记', '质量分析', '生成缩略图', '元数据提取'].map((step, index) => (
                  <div key={index} className="flex items-center justify-between p-2 border rounded"
                       style={{ borderColor: 'var(--mizzy-border)' }}>
                    <div className="flex items-center gap-2">
                      <span className="text-sm" style={{ color: 'var(--mizzy-content)' }}>
                        {index + 1}. {step}
                      </span>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="ghost" size="sm">
                        <Settings className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="flex gap-2">
              <Button style={{ background: 'var(--mizzy-highlight)', color: 'white' }}>
                保存工作流
              </Button>
              <Button variant="outline">
                测试运行
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </ScrollArea>
  );
};

// 数据分析面板
const DataAnalytics: React.FC = () => {
  return (
    <ScrollArea className="h-full p-4">
      <h3 className="mb-4" style={{ color: 'var(--mizzy-title)' }}>数据分析</h3>
      
      <div className="space-y-4">
        {/* 统计概览 */}
        <div className="grid grid-cols-2 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-2xl font-bold" style={{ color: 'var(--mizzy-highlight)' }}>
                  12,845
                </div>
                <div className="text-sm" style={{ color: 'var(--mizzy-icon)' }}>
                  总图像数
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-2xl font-bold" style={{ color: 'var(--mizzy-highlight)' }}>
                  8.5 GB
                </div>
                <div className="text-sm" style={{ color: 'var(--mizzy-icon)' }}>
                  总存储空间
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 质量分布 */}
        <Card>
          <CardHeader>
            <CardTitle style={{ color: 'var(--mizzy-title)' }}>质量分布</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span style={{ color: 'var(--mizzy-content)' }}>优秀 (9-10)</span>
                <span style={{ color: 'var(--mizzy-content)' }}>25%</span>
              </div>
              <Progress value={25} className="w-full" />
              
              <div className="flex justify-between">
                <span style={{ color: 'var(--mizzy-content)' }}>良好 (7-8)</span>
                <span style={{ color: 'var(--mizzy-content)' }}>45%</span>
              </div>
              <Progress value={45} className="w-full" />
              
              <div className="flex justify-between">
                <span style={{ color: 'var(--mizzy-content)' }}>一般 (5-6)</span>
                <span style={{ color: 'var(--mizzy-content)' }}>20%</span>
              </div>
              <Progress value={20} className="w-full" />
              
              <div className="flex justify-between">
                <span style={{ color: 'var(--mizzy-content)' }}>较差 (&lt;5)</span>
                <span style={{ color: 'var(--mizzy-content)' }}>10%</span>
              </div>
              <Progress value={10} className="w-full" />
            </div>
          </CardContent>
        </Card>

        {/* 标签统计 */}
        <Card>
          <CardHeader>
            <CardTitle style={{ color: 'var(--mizzy-title)' }}>热门标签</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {[
                { tag: 'portrait', count: 2845 },
                { tag: 'nature', count: 1923 },
                { tag: 'urban', count: 1567 },
                { tag: 'color', count: 1234 },
                { tag: 'black-white', count: 987 },
                { tag: 'vintage', count: 654 },
              ].map((item) => (
                <Badge key={item.tag} variant="outline" className="text-xs">
                  {item.tag} ({item.count})
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 存储分析 */}
        <Card>
          <CardHeader>
            <CardTitle style={{ color: 'var(--mizzy-title)' }}>存储分析</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span style={{ color: 'var(--mizzy-content)' }}>JPG文件</span>
                <span style={{ color: 'var(--mizzy-content)' }}>4.2 GB (49%)</span>
              </div>
              <div className="flex justify-between items-center">
                <span style={{ color: 'var(--mizzy-content)' }}>RAW文件</span>
                <span style={{ color: 'var(--mizzy-content)' }}>3.8 GB (45%)</span>
              </div>
              <div className="flex justify-between items-center">
                <span style={{ color: 'var(--mizzy-content)' }}>PNG文件</span>
                <span style={{ color: 'var(--mizzy-content)' }}>0.5 GB (6%)</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </ScrollArea>
  );
};

export const Workspace: React.FC<WorkspaceProps> = ({
  onToggleWorkspace,
  onSwapGalleryWorkspace,
  galleryWorkspaceSwapped,
  selectedImages,
  selectedTags,
}) => {
  const [activeTab, setActiveTab] = useState<string>('rules');
  const [openTabs, setOpenTabs] = useState<WorkspaceTab[]>([
    { id: 'rules', title: '规则配置', icon: Code, component: RuleConfiguration },
    { id: 'quality', title: '质量分析', icon: BarChart3, component: QualityAnalysis },
    { id: 'ai', title: 'AI分析', icon: Brain, component: AIAnalysis },
    { id: 'clipboard', title: '剪贴簿', icon: Clipboard, component: Clipboard },
    { id: 'clustering', title: '图像簇整理', icon: Layers, component: ImageClustering }
  ]);

  const handleTabClose = (tabId: string) => {
    setOpenTabs(openTabs.filter(tab => tab.id !== tabId));
    if (activeTab === tabId) {
      const remainingTabs = openTabs.filter(tab => tab.id !== tabId);
      if (remainingTabs.length > 0) {
        setActiveTab(remainingTabs[0].id);
      }
    }
  };

  const handleTabSwitch = (tabId: string) => {
    setActiveTab(tabId);
  };

  const getActiveTabComponent = () => {
    const activeTabData = openTabs.find(tab => tab.id === activeTab);
    return activeTabData ? activeTabData.component : null;
  };

  return (
    <div className="h-full flex flex-col" style={{ background: 'var(--mizzy-workspace)' }}>
      {/* 工作台视图切换器 C1 */}
      <div className="flex items-center justify-between p-2 border-b" style={{ borderColor: 'var(--mizzy-border)' }}>
        <div className="flex items-center gap-2">
          {/* 工作台视图切换器 */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onSwapGalleryWorkspace}
            className="hover:bg-opacity-10"
            style={{ color: 'var(--mizzy-icon)' }}
          >
            {galleryWorkspaceSwapped ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
          </Button>

          {/* 工作台全局设置 C2 */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                size="sm"
                className="hover:bg-opacity-10"
                style={{ color: 'var(--mizzy-icon)' }}
              >
                <Settings className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setActiveTab('rules')}>
                规则配置
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setActiveTab('quality')}>
                质量分析
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setActiveTab('ai')}>
                AI分析
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setActiveTab('clipboard')}>
                剪贴簿
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setActiveTab('clustering')}>
                图像簇整理
              </DropdownMenuItem>
              <DropdownMenuItem>
                设置智能规则
              </DropdownMenuItem>
              <DropdownMenuItem>
                保存当前工作区
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* 最小化工作台按钮 C5 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleWorkspace}
          className="hover:bg-opacity-10"
          style={{ color: 'var(--mizzy-icon)' }}
        >
          <Minimize2 className="h-4 w-4" />
        </Button>
      </div>

      {/* 标签页管理 C3-C4 */}
      {openTabs.length > 0 && (
        <div className="border-b" style={{ borderColor: 'var(--mizzy-border)' }}>
          <div className="flex items-center">
            {openTabs.map((tab) => {
              const TabIcon = tab.icon;
              const isActive = activeTab === tab.id;
              
              return (
                <div
                  key={tab.id}
                  className={`flex items-center gap-2 px-3 py-2 cursor-pointer border-r transition-all ${
                    isActive ? 'bg-opacity-20' : 'hover:bg-opacity-10'
                  }`}
                  style={{
                    background: isActive ? 'var(--mizzy-highlight)' : 'transparent',
                    borderColor: 'var(--mizzy-border)',
                    color: isActive ? 'var(--mizzy-content)' : 'var(--mizzy-icon)'
                  }}
                  onClick={() => handleTabSwitch(tab.id)}
                >
                  <TabIcon className="h-4 w-4" />
                  <span className="text-sm">{tab.title}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleTabClose(tab.id);
                    }}
                    className="h-4 w-4 p-0 hover:bg-opacity-10 ml-1"
                    style={{ color: 'var(--mizzy-icon)' }}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* 工作台内容区域 */}
      <div className="flex-1 overflow-hidden">
        {(() => {
          const ActiveComponent = getActiveTabComponent();
          return ActiveComponent ? <ActiveComponent /> : (
            <div className="h-full flex items-center justify-center" style={{ color: 'var(--mizzy-icon)' }}>
              <div className="text-center">
                <Settings className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>选择工作台功能</p>
              </div>
            </div>
          );
        })()}
      </div>
    </div>
  );
};