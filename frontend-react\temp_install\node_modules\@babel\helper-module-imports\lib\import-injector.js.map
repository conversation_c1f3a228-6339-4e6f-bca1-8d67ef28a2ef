{"version": 3, "names": ["_assert", "require", "_t", "_importBuilder", "_isModule", "identifier", "importSpecifier", "numericLiteral", "sequenceExpression", "isImportDeclaration", "ImportInjector", "constructor", "path", "importedSource", "opts", "_defaultOpts", "importedType", "importedInterop", "importingInterop", "ensureLiveReference", "ensureNoContext", "importPosition", "programPath", "find", "p", "isProgram", "_programPath", "_programScope", "scope", "_hub", "hub", "_applyDefaults", "addDefault", "importedSourceIn", "addNamed", "importName", "assert", "_generateImport", "addNamespace", "addSideEffect", "isInit", "newOpts", "Object", "assign", "nameHint", "undefined", "blockHoist", "isDefault", "isNamed", "isNamespace", "name", "isMod", "isModule", "isModuleForNode", "isModuleForBabel", "Error", "builder", "ImportBuilder", "import", "namespace", "named", "es6Default", "default", "var", "wildcardInterop", "defaultInterop", "read", "prop", "statements", "resultName", "done", "_insertStatements", "type", "_insertStatementsAfter", "_insertStatementsBefore", "unshiftContainer", "length", "isValueImport", "firstImportDecl", "get", "node", "source", "value", "maybeAppendImportSpecifiers", "for<PERSON>ach", "_blockHoist", "targetPath", "val", "Number", "isFinite", "insertBefore", "statementsSet", "Set", "importDeclarations", "Map", "statement", "has", "set", "push", "lastImportPath", "bodyStmt", "newImports", "decl", "delete", "size", "insertAfter", "Array", "from", "exports", "importKind", "hasNamespaceImport", "specifiers", "hasDefaultImport", "target", "local", "unshift", "shift"], "sources": ["../src/import-injector.ts"], "sourcesContent": ["import assert from \"node:assert\";\nimport {\n  identifier,\n  importSpecifier,\n  numericLiteral,\n  sequenceExpression,\n  isImportDeclaration,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport type { <PERSON>de<PERSON><PERSON>, <PERSON>ope, HubInterface } from \"@babel/traverse\";\n\nimport ImportBuilder from \"./import-builder.ts\";\nimport isModule from \"./is-module.ts\";\n\nexport type ImportOptions = {\n  /**\n   * The module being referenced.\n   */\n  importedSource: string | null;\n  /**\n   * The type of module being imported:\n   *\n   *  * 'es6'      - An ES6 module.\n   *  * 'commonjs' - A CommonJS module. (Default)\n   */\n  importedType: \"es6\" | \"commonjs\";\n  /**\n   * The type of interop behavior for namespace/default/named when loading\n   * CommonJS modules.\n   *\n   * ## 'babel' (Default)\n   *\n   * Load using Babel's interop.\n   *\n   * If '.__esModule' is true, treat as 'compiled', else:\n   *\n   * * Namespace: A copy of the module.exports with .default\n   *     populated by the module.exports object.\n   * * Default: The module.exports value.\n   * * Named: The .named property of module.exports.\n   *\n   * The 'ensureLiveReference' has no effect on the liveness of these.\n   *\n   * ## 'compiled'\n   *\n   * Assume the module is ES6 compiled to CommonJS. Useful to avoid injecting\n   * interop logic if you are confident that the module is a certain format.\n   *\n   * * Namespace: The root module.exports object.\n   * * Default: The .default property of the namespace.\n   * * Named: The .named property of the namespace.\n   *\n   * Will return erroneous results if the imported module is _not_ compiled\n   * from ES6 with Babel.\n   *\n   * ## 'uncompiled'\n   *\n   * Assume the module is _not_ ES6 compiled to CommonJS. Used a simplified\n   * access pattern that doesn't require additional function calls.\n   *\n   * Will return erroneous results if the imported module _is_ compiled\n   * from ES6 with Babel.\n   *\n   * * Namespace: The module.exports object.\n   * * Default: The module.exports object.\n   * * Named: The .named property of module.exports.\n   */\n  importedInterop: \"babel\" | \"node\" | \"compiled\" | \"uncompiled\";\n  /**\n   * The type of CommonJS interop included in the environment that will be\n   * loading the output code.\n   *\n   *  * 'babel' - CommonJS modules load with Babel's interop. (Default)\n   *  * 'node'  - CommonJS modules load with Node's interop.\n   *\n   * See descriptions in 'importedInterop' for more details.\n   */\n  importingInterop: \"babel\" | \"node\";\n  /**\n   * Define whether we explicitly care that the import be a live reference.\n   * Only applies when importing default and named imports, not the namespace.\n   *\n   *  * true  - Force imported values to be live references.\n   *  * false - No particular requirements. Keeps the code simplest. (Default)\n   */\n  ensureLiveReference: boolean;\n  /**\n   * Define if we explicitly care that the result not be a property reference.\n   *\n   *  * true  - Force calls to exclude context. Useful if the value is going to\n   *            be used as function callee.\n   *  * false - No particular requirements for context of the access. (Default)\n   */\n  ensureNoContext: boolean;\n  /**\n   * Define whether the import should be loaded before or after the existing imports.\n   * \"after\" is only allowed inside ECMAScript modules, since it's not possible to\n   * reliably pick the location _after_ require() calls but _before_ other code in CJS.\n   */\n  importPosition: \"before\" | \"after\";\n\n  nameHint?: string;\n  blockHoist?: number;\n};\n\n/**\n * A general helper classes add imports via transforms. See README for usage.\n */\nexport default class ImportInjector {\n  /**\n   * The path used for manipulation.\n   */\n  declare _programPath: NodePath<t.Program>;\n\n  /**\n   * The scope used to generate unique variable names.\n   */\n  declare _programScope: Scope;\n\n  /**\n   * The file used to inject helpers and resolve paths.\n   */\n  declare _hub: HubInterface;\n\n  /**\n   * The default options to use with this instance when imports are added.\n   */\n  _defaultOpts: ImportOptions = {\n    importedSource: null,\n    importedType: \"commonjs\",\n    importedInterop: \"babel\",\n    importingInterop: \"babel\",\n    ensureLiveReference: false,\n    ensureNoContext: false,\n    importPosition: \"before\",\n  };\n\n  constructor(\n    path: NodePath,\n    importedSource?: string,\n    opts?: Partial<ImportOptions>,\n  ) {\n    const programPath = path.find(p => p.isProgram()) as NodePath<t.Program>;\n\n    this._programPath = programPath;\n    this._programScope = programPath.scope;\n    this._hub = programPath.hub;\n\n    this._defaultOpts = this._applyDefaults(importedSource, opts, true);\n  }\n\n  addDefault(importedSourceIn: string, opts: Partial<ImportOptions>) {\n    return this.addNamed(\"default\", importedSourceIn, opts);\n  }\n\n  addNamed(\n    importName: string,\n    importedSourceIn: string,\n    opts: Partial<ImportOptions>,\n  ) {\n    assert(typeof importName === \"string\");\n\n    return this._generateImport(\n      this._applyDefaults(importedSourceIn, opts),\n      importName,\n    );\n  }\n\n  addNamespace(importedSourceIn: string, opts: Partial<ImportOptions>) {\n    return this._generateImport(\n      this._applyDefaults(importedSourceIn, opts),\n      null,\n    );\n  }\n\n  addSideEffect(importedSourceIn: string, opts: Partial<ImportOptions>) {\n    return this._generateImport(\n      this._applyDefaults(importedSourceIn, opts),\n      void 0,\n    );\n  }\n\n  _applyDefaults(\n    importedSource: string | Partial<ImportOptions>,\n    opts: Partial<ImportOptions> | undefined,\n    isInit = false,\n  ) {\n    let newOpts: ImportOptions;\n    if (typeof importedSource === \"string\") {\n      newOpts = { ...this._defaultOpts, importedSource, ...opts };\n    } else {\n      assert(!opts, \"Unexpected secondary arguments.\");\n      newOpts = { ...this._defaultOpts, ...importedSource };\n    }\n\n    if (!isInit && opts) {\n      if (opts.nameHint !== undefined) newOpts.nameHint = opts.nameHint;\n      if (opts.blockHoist !== undefined) newOpts.blockHoist = opts.blockHoist;\n    }\n    return newOpts;\n  }\n\n  _generateImport(\n    opts: Partial<ImportOptions>,\n    importName: string | null | undefined,\n  ) {\n    const isDefault = importName === \"default\";\n    const isNamed = !!importName && !isDefault;\n    const isNamespace = importName === null;\n\n    const {\n      importedSource,\n      importedType,\n      importedInterop,\n      importingInterop,\n      ensureLiveReference,\n      ensureNoContext,\n      nameHint,\n      importPosition,\n\n      // Not meant for public usage. Allows code that absolutely must control\n      // ordering to set a specific hoist value on the import nodes.\n      // This is ignored when \"importPosition\" is \"after\".\n      blockHoist,\n    } = opts;\n\n    // Provide a hint for generateUidIdentifier for the local variable name\n    // to use for the import, if the code will generate a simple assignment\n    // to a variable.\n    let name = nameHint || importName;\n\n    const isMod = isModule(this._programPath);\n    const isModuleForNode = isMod && importingInterop === \"node\";\n    const isModuleForBabel = isMod && importingInterop === \"babel\";\n\n    if (importPosition === \"after\" && !isMod) {\n      throw new Error(`\"importPosition\": \"after\" is only supported in modules`);\n    }\n\n    const builder = new ImportBuilder(\n      importedSource,\n      this._programScope,\n      this._hub,\n    );\n\n    if (importedType === \"es6\") {\n      if (!isModuleForNode && !isModuleForBabel) {\n        throw new Error(\"Cannot import an ES6 module from CommonJS\");\n      }\n\n      // import * as namespace from ''; namespace\n      // import def from ''; def\n      // import { named } from ''; named\n      builder.import();\n      if (isNamespace) {\n        builder.namespace(nameHint || importedSource);\n      } else if (isDefault || isNamed) {\n        builder.named(name, importName);\n      }\n    } else if (importedType !== \"commonjs\") {\n      throw new Error(`Unexpected interopType \"${importedType}\"`);\n    } else if (importedInterop === \"babel\") {\n      if (isModuleForNode) {\n        // import _tmp from ''; var namespace = interopRequireWildcard(_tmp); namespace\n        // import _tmp from ''; var def = interopRequireDefault(_tmp).default; def\n        // import _tmp from ''; _tmp.named\n        name = name !== \"default\" ? name : importedSource;\n        const es6Default = `${importedSource}$es6Default`;\n\n        builder.import();\n        if (isNamespace) {\n          builder\n            .default(es6Default)\n            .var(name || importedSource)\n            .wildcardInterop();\n        } else if (isDefault) {\n          if (ensureLiveReference) {\n            builder\n              .default(es6Default)\n              .var(name || importedSource)\n              .defaultInterop()\n              .read(\"default\");\n          } else {\n            builder\n              .default(es6Default)\n              .var(name)\n              .defaultInterop()\n              .prop(importName);\n          }\n        } else if (isNamed) {\n          builder.default(es6Default).read(importName);\n        }\n      } else if (isModuleForBabel) {\n        // import * as namespace from ''; namespace\n        // import def from ''; def\n        // import { named } from ''; named\n        builder.import();\n        if (isNamespace) {\n          builder.namespace(name || importedSource);\n        } else if (isDefault || isNamed) {\n          builder.named(name, importName);\n        }\n      } else {\n        // var namespace = interopRequireWildcard(require(''));\n        // var def = interopRequireDefault(require('')).default; def\n        // var named = require('').named; named\n        builder.require();\n        if (isNamespace) {\n          builder.var(name || importedSource).wildcardInterop();\n        } else if ((isDefault || isNamed) && ensureLiveReference) {\n          if (isDefault) {\n            name = name !== \"default\" ? name : importedSource;\n            builder.var(name).read(importName);\n            builder.defaultInterop();\n          } else {\n            builder.var(importedSource).read(importName);\n          }\n        } else if (isDefault) {\n          builder.var(name).defaultInterop().prop(importName);\n        } else if (isNamed) {\n          builder.var(name).prop(importName);\n        }\n      }\n    } else if (importedInterop === \"compiled\") {\n      if (isModuleForNode) {\n        // import namespace from ''; namespace\n        // import namespace from ''; namespace.default\n        // import namespace from ''; namespace.named\n\n        builder.import();\n        if (isNamespace) {\n          builder.default(name || importedSource);\n        } else if (isDefault || isNamed) {\n          builder.default(importedSource).read(name);\n        }\n      } else if (isModuleForBabel) {\n        // import * as namespace from ''; namespace\n        // import def from ''; def\n        // import { named } from ''; named\n        // Note: These lookups will break if the module has no __esModule set,\n        // hence the warning that 'compiled' will not work on standard CommonJS.\n\n        builder.import();\n        if (isNamespace) {\n          builder.namespace(name || importedSource);\n        } else if (isDefault || isNamed) {\n          builder.named(name, importName);\n        }\n      } else {\n        // var namespace = require(''); namespace\n        // var namespace = require(''); namespace.default\n        // var namespace = require(''); namespace.named\n        // var named = require('').named;\n        builder.require();\n        if (isNamespace) {\n          builder.var(name || importedSource);\n        } else if (isDefault || isNamed) {\n          if (ensureLiveReference) {\n            builder.var(importedSource).read(name);\n          } else {\n            builder.prop(importName).var(name);\n          }\n        }\n      }\n    } else if (importedInterop === \"uncompiled\") {\n      if (isDefault && ensureLiveReference) {\n        throw new Error(\"No live reference for commonjs default\");\n      }\n\n      if (isModuleForNode) {\n        // import namespace from ''; namespace\n        // import def from ''; def;\n        // import namespace from ''; namespace.named\n        builder.import();\n        if (isNamespace) {\n          builder.default(name || importedSource);\n        } else if (isDefault) {\n          builder.default(name);\n        } else if (isNamed) {\n          builder.default(importedSource).read(name);\n        }\n      } else if (isModuleForBabel) {\n        // import namespace from '';\n        // import def from '';\n        // import { named } from ''; named;\n        // Note: These lookups will break if the module has __esModule set,\n        // hence the warning that 'uncompiled' will not work on ES6 transpiled\n        // to CommonJS.\n\n        builder.import();\n        if (isNamespace) {\n          builder.default(name || importedSource);\n        } else if (isDefault) {\n          builder.default(name);\n        } else if (isNamed) {\n          builder.named(name, importName);\n        }\n      } else {\n        // var namespace = require(''); namespace\n        // var def = require(''); def\n        // var namespace = require(''); namespace.named\n        // var named = require('').named;\n        builder.require();\n        if (isNamespace) {\n          builder.var(name || importedSource);\n        } else if (isDefault) {\n          builder.var(name);\n        } else if (isNamed) {\n          if (ensureLiveReference) {\n            builder.var(importedSource).read(name);\n          } else {\n            builder.var(name).prop(importName);\n          }\n        }\n      }\n    } else {\n      throw new Error(`Unknown importedInterop \"${importedInterop}\".`);\n    }\n\n    const { statements, resultName } = builder.done();\n\n    this._insertStatements(statements, importPosition, blockHoist);\n\n    if (\n      (isDefault || isNamed) &&\n      ensureNoContext &&\n      resultName.type !== \"Identifier\"\n    ) {\n      return sequenceExpression([numericLiteral(0), resultName]);\n    }\n    return resultName;\n  }\n\n  _insertStatements(\n    statements: t.Statement[],\n    importPosition = \"before\",\n    blockHoist = 3,\n  ) {\n    if (importPosition === \"after\") {\n      if (this._insertStatementsAfter(statements)) return;\n    } else {\n      if (this._insertStatementsBefore(statements, blockHoist)) return;\n    }\n\n    this._programPath.unshiftContainer(\"body\", statements);\n  }\n\n  _insertStatementsBefore(statements: t.Statement[], blockHoist: number) {\n    if (\n      statements.length === 1 &&\n      isImportDeclaration(statements[0]) &&\n      isValueImport(statements[0])\n    ) {\n      const firstImportDecl = this._programPath\n        .get(\"body\")\n        .find((p): p is NodePath<t.ImportDeclaration> => {\n          return p.isImportDeclaration() && isValueImport(p.node);\n        });\n\n      if (\n        firstImportDecl?.node.source.value === statements[0].source.value &&\n        maybeAppendImportSpecifiers(firstImportDecl.node, statements[0])\n      ) {\n        return true;\n      }\n    }\n\n    statements.forEach(node => {\n      // @ts-expect-error handle _blockHoist\n      node._blockHoist = blockHoist;\n    });\n\n    const targetPath = this._programPath.get(\"body\").find(p => {\n      // @ts-expect-error todo(flow->ts): avoid mutations\n      const val = p.node._blockHoist;\n      return Number.isFinite(val) && val < 4;\n    });\n\n    if (targetPath) {\n      targetPath.insertBefore(statements);\n      return true;\n    }\n\n    return false;\n  }\n\n  _insertStatementsAfter(statements: t.Statement[]): boolean {\n    const statementsSet = new Set(statements);\n    const importDeclarations: Map<string, t.ImportDeclaration[]> = new Map();\n\n    for (const statement of statements) {\n      if (isImportDeclaration(statement) && isValueImport(statement)) {\n        const source = statement.source.value;\n        if (!importDeclarations.has(source)) importDeclarations.set(source, []);\n        importDeclarations.get(source).push(statement);\n      }\n    }\n\n    let lastImportPath = null;\n    for (const bodyStmt of this._programPath.get(\"body\")) {\n      if (bodyStmt.isImportDeclaration() && isValueImport(bodyStmt.node)) {\n        lastImportPath = bodyStmt;\n\n        const source = bodyStmt.node.source.value;\n        const newImports = importDeclarations.get(source);\n        if (!newImports) continue;\n\n        for (const decl of newImports) {\n          if (!statementsSet.has(decl)) continue;\n          if (maybeAppendImportSpecifiers(bodyStmt.node, decl)) {\n            statementsSet.delete(decl);\n          }\n        }\n      }\n    }\n\n    if (statementsSet.size === 0) return true;\n\n    if (lastImportPath) lastImportPath.insertAfter(Array.from(statementsSet));\n\n    return !!lastImportPath;\n  }\n}\n\nfunction isValueImport(node: t.ImportDeclaration) {\n  return node.importKind !== \"type\" && node.importKind !== \"typeof\";\n}\n\nfunction hasNamespaceImport(node: t.ImportDeclaration) {\n  return (\n    (node.specifiers.length === 1 &&\n      node.specifiers[0].type === \"ImportNamespaceSpecifier\") ||\n    (node.specifiers.length === 2 &&\n      node.specifiers[1].type === \"ImportNamespaceSpecifier\")\n  );\n}\n\nfunction hasDefaultImport(node: t.ImportDeclaration) {\n  return (\n    node.specifiers.length > 0 &&\n    node.specifiers[0].type === \"ImportDefaultSpecifier\"\n  );\n}\n\nfunction maybeAppendImportSpecifiers(\n  target: t.ImportDeclaration,\n  source: t.ImportDeclaration,\n): boolean {\n  if (!target.specifiers.length) {\n    target.specifiers = source.specifiers;\n    return true;\n  }\n  if (!source.specifiers.length) return true;\n\n  if (hasNamespaceImport(target) || hasNamespaceImport(source)) return false;\n\n  if (hasDefaultImport(source)) {\n    if (hasDefaultImport(target)) {\n      source.specifiers[0] = importSpecifier(\n        source.specifiers[0].local,\n        identifier(\"default\"),\n      );\n    } else {\n      target.specifiers.unshift(source.specifiers.shift());\n    }\n  }\n\n  target.specifiers.push(...source.specifiers);\n\n  return true;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,EAAA,GAAAD,OAAA;AAUA,IAAAE,cAAA,GAAAF,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AAAsC;EAVpCI,UAAU;EACVC,eAAe;EACfC,cAAc;EACdC,kBAAkB;EAClBC;AAAmB,IAAAP,EAAA;AAsGN,MAAMQ,cAAc,CAAC;EA6BlCC,WAAWA,CACTC,IAAc,EACdC,cAAuB,EACvBC,IAA6B,EAC7B;IAAA,KAdFC,YAAY,GAAkB;MAC5BF,cAAc,EAAE,IAAI;MACpBG,YAAY,EAAE,UAAU;MACxBC,eAAe,EAAE,OAAO;MACxBC,gBAAgB,EAAE,OAAO;MACzBC,mBAAmB,EAAE,KAAK;MAC1BC,eAAe,EAAE,KAAK;MACtBC,cAAc,EAAE;IAClB,CAAC;IAOC,MAAMC,WAAW,GAAGV,IAAI,CAACW,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,CAAC,CAAC,CAAwB;IAExE,IAAI,CAACC,YAAY,GAAGJ,WAAW;IAC/B,IAAI,CAACK,aAAa,GAAGL,WAAW,CAACM,KAAK;IACtC,IAAI,CAACC,IAAI,GAAGP,WAAW,CAACQ,GAAG;IAE3B,IAAI,CAACf,YAAY,GAAG,IAAI,CAACgB,cAAc,CAAClB,cAAc,EAAEC,IAAI,EAAE,IAAI,CAAC;EACrE;EAEAkB,UAAUA,CAACC,gBAAwB,EAAEnB,IAA4B,EAAE;IACjE,OAAO,IAAI,CAACoB,QAAQ,CAAC,SAAS,EAAED,gBAAgB,EAAEnB,IAAI,CAAC;EACzD;EAEAoB,QAAQA,CACNC,UAAkB,EAClBF,gBAAwB,EACxBnB,IAA4B,EAC5B;IACAsB,OAAM,CAAC,OAAOD,UAAU,KAAK,QAAQ,CAAC;IAEtC,OAAO,IAAI,CAACE,eAAe,CACzB,IAAI,CAACN,cAAc,CAACE,gBAAgB,EAAEnB,IAAI,CAAC,EAC3CqB,UACF,CAAC;EACH;EAEAG,YAAYA,CAACL,gBAAwB,EAAEnB,IAA4B,EAAE;IACnE,OAAO,IAAI,CAACuB,eAAe,CACzB,IAAI,CAACN,cAAc,CAACE,gBAAgB,EAAEnB,IAAI,CAAC,EAC3C,IACF,CAAC;EACH;EAEAyB,aAAaA,CAACN,gBAAwB,EAAEnB,IAA4B,EAAE;IACpE,OAAO,IAAI,CAACuB,eAAe,CACzB,IAAI,CAACN,cAAc,CAACE,gBAAgB,EAAEnB,IAAI,CAAC,EAC3C,KAAK,CACP,CAAC;EACH;EAEAiB,cAAcA,CACZlB,cAA+C,EAC/CC,IAAwC,EACxC0B,MAAM,GAAG,KAAK,EACd;IACA,IAAIC,OAAsB;IAC1B,IAAI,OAAO5B,cAAc,KAAK,QAAQ,EAAE;MACtC4B,OAAO,GAAAC,MAAA,CAAAC,MAAA,KAAQ,IAAI,CAAC5B,YAAY;QAAEF;MAAc,GAAKC,IAAI,CAAE;IAC7D,CAAC,MAAM;MACLsB,OAAM,CAAC,CAACtB,IAAI,EAAE,iCAAiC,CAAC;MAChD2B,OAAO,GAAAC,MAAA,CAAAC,MAAA,KAAQ,IAAI,CAAC5B,YAAY,EAAKF,cAAc,CAAE;IACvD;IAEA,IAAI,CAAC2B,MAAM,IAAI1B,IAAI,EAAE;MACnB,IAAIA,IAAI,CAAC8B,QAAQ,KAAKC,SAAS,EAAEJ,OAAO,CAACG,QAAQ,GAAG9B,IAAI,CAAC8B,QAAQ;MACjE,IAAI9B,IAAI,CAACgC,UAAU,KAAKD,SAAS,EAAEJ,OAAO,CAACK,UAAU,GAAGhC,IAAI,CAACgC,UAAU;IACzE;IACA,OAAOL,OAAO;EAChB;EAEAJ,eAAeA,CACbvB,IAA4B,EAC5BqB,UAAqC,EACrC;IACA,MAAMY,SAAS,GAAGZ,UAAU,KAAK,SAAS;IAC1C,MAAMa,OAAO,GAAG,CAAC,CAACb,UAAU,IAAI,CAACY,SAAS;IAC1C,MAAME,WAAW,GAAGd,UAAU,KAAK,IAAI;IAEvC,MAAM;MACJtB,cAAc;MACdG,YAAY;MACZC,eAAe;MACfC,gBAAgB;MAChBC,mBAAmB;MACnBC,eAAe;MACfwB,QAAQ;MACRvB,cAAc;MAKdyB;IACF,CAAC,GAAGhC,IAAI;IAKR,IAAIoC,IAAI,GAAGN,QAAQ,IAAIT,UAAU;IAEjC,MAAMgB,KAAK,GAAG,IAAAC,iBAAQ,EAAC,IAAI,CAAC1B,YAAY,CAAC;IACzC,MAAM2B,eAAe,GAAGF,KAAK,IAAIjC,gBAAgB,KAAK,MAAM;IAC5D,MAAMoC,gBAAgB,GAAGH,KAAK,IAAIjC,gBAAgB,KAAK,OAAO;IAE9D,IAAIG,cAAc,KAAK,OAAO,IAAI,CAAC8B,KAAK,EAAE;MACxC,MAAM,IAAII,KAAK,CAAC,wDAAwD,CAAC;IAC3E;IAEA,MAAMC,OAAO,GAAG,IAAIC,sBAAa,CAC/B5C,cAAc,EACd,IAAI,CAACc,aAAa,EAClB,IAAI,CAACE,IACP,CAAC;IAED,IAAIb,YAAY,KAAK,KAAK,EAAE;MAC1B,IAAI,CAACqC,eAAe,IAAI,CAACC,gBAAgB,EAAE;QACzC,MAAM,IAAIC,KAAK,CAAC,2CAA2C,CAAC;MAC9D;MAKAC,OAAO,CAACE,MAAM,CAAC,CAAC;MAChB,IAAIT,WAAW,EAAE;QACfO,OAAO,CAACG,SAAS,CAACf,QAAQ,IAAI/B,cAAc,CAAC;MAC/C,CAAC,MAAM,IAAIkC,SAAS,IAAIC,OAAO,EAAE;QAC/BQ,OAAO,CAACI,KAAK,CAACV,IAAI,EAAEf,UAAU,CAAC;MACjC;IACF,CAAC,MAAM,IAAInB,YAAY,KAAK,UAAU,EAAE;MACtC,MAAM,IAAIuC,KAAK,CAAC,2BAA2BvC,YAAY,GAAG,CAAC;IAC7D,CAAC,MAAM,IAAIC,eAAe,KAAK,OAAO,EAAE;MACtC,IAAIoC,eAAe,EAAE;QAInBH,IAAI,GAAGA,IAAI,KAAK,SAAS,GAAGA,IAAI,GAAGrC,cAAc;QACjD,MAAMgD,UAAU,GAAG,GAAGhD,cAAc,aAAa;QAEjD2C,OAAO,CAACE,MAAM,CAAC,CAAC;QAChB,IAAIT,WAAW,EAAE;UACfO,OAAO,CACJM,OAAO,CAACD,UAAU,CAAC,CACnBE,GAAG,CAACb,IAAI,IAAIrC,cAAc,CAAC,CAC3BmD,eAAe,CAAC,CAAC;QACtB,CAAC,MAAM,IAAIjB,SAAS,EAAE;UACpB,IAAI5B,mBAAmB,EAAE;YACvBqC,OAAO,CACJM,OAAO,CAACD,UAAU,CAAC,CACnBE,GAAG,CAACb,IAAI,IAAIrC,cAAc,CAAC,CAC3BoD,cAAc,CAAC,CAAC,CAChBC,IAAI,CAAC,SAAS,CAAC;UACpB,CAAC,MAAM;YACLV,OAAO,CACJM,OAAO,CAACD,UAAU,CAAC,CACnBE,GAAG,CAACb,IAAI,CAAC,CACTe,cAAc,CAAC,CAAC,CAChBE,IAAI,CAAChC,UAAU,CAAC;UACrB;QACF,CAAC,MAAM,IAAIa,OAAO,EAAE;UAClBQ,OAAO,CAACM,OAAO,CAACD,UAAU,CAAC,CAACK,IAAI,CAAC/B,UAAU,CAAC;QAC9C;MACF,CAAC,MAAM,IAAImB,gBAAgB,EAAE;QAI3BE,OAAO,CAACE,MAAM,CAAC,CAAC;QAChB,IAAIT,WAAW,EAAE;UACfO,OAAO,CAACG,SAAS,CAACT,IAAI,IAAIrC,cAAc,CAAC;QAC3C,CAAC,MAAM,IAAIkC,SAAS,IAAIC,OAAO,EAAE;UAC/BQ,OAAO,CAACI,KAAK,CAACV,IAAI,EAAEf,UAAU,CAAC;QACjC;MACF,CAAC,MAAM;QAILqB,OAAO,CAACvD,OAAO,CAAC,CAAC;QACjB,IAAIgD,WAAW,EAAE;UACfO,OAAO,CAACO,GAAG,CAACb,IAAI,IAAIrC,cAAc,CAAC,CAACmD,eAAe,CAAC,CAAC;QACvD,CAAC,MAAM,IAAI,CAACjB,SAAS,IAAIC,OAAO,KAAK7B,mBAAmB,EAAE;UACxD,IAAI4B,SAAS,EAAE;YACbG,IAAI,GAAGA,IAAI,KAAK,SAAS,GAAGA,IAAI,GAAGrC,cAAc;YACjD2C,OAAO,CAACO,GAAG,CAACb,IAAI,CAAC,CAACgB,IAAI,CAAC/B,UAAU,CAAC;YAClCqB,OAAO,CAACS,cAAc,CAAC,CAAC;UAC1B,CAAC,MAAM;YACLT,OAAO,CAACO,GAAG,CAAClD,cAAc,CAAC,CAACqD,IAAI,CAAC/B,UAAU,CAAC;UAC9C;QACF,CAAC,MAAM,IAAIY,SAAS,EAAE;UACpBS,OAAO,CAACO,GAAG,CAACb,IAAI,CAAC,CAACe,cAAc,CAAC,CAAC,CAACE,IAAI,CAAChC,UAAU,CAAC;QACrD,CAAC,MAAM,IAAIa,OAAO,EAAE;UAClBQ,OAAO,CAACO,GAAG,CAACb,IAAI,CAAC,CAACiB,IAAI,CAAChC,UAAU,CAAC;QACpC;MACF;IACF,CAAC,MAAM,IAAIlB,eAAe,KAAK,UAAU,EAAE;MACzC,IAAIoC,eAAe,EAAE;QAKnBG,OAAO,CAACE,MAAM,CAAC,CAAC;QAChB,IAAIT,WAAW,EAAE;UACfO,OAAO,CAACM,OAAO,CAACZ,IAAI,IAAIrC,cAAc,CAAC;QACzC,CAAC,MAAM,IAAIkC,SAAS,IAAIC,OAAO,EAAE;UAC/BQ,OAAO,CAACM,OAAO,CAACjD,cAAc,CAAC,CAACqD,IAAI,CAAChB,IAAI,CAAC;QAC5C;MACF,CAAC,MAAM,IAAII,gBAAgB,EAAE;QAO3BE,OAAO,CAACE,MAAM,CAAC,CAAC;QAChB,IAAIT,WAAW,EAAE;UACfO,OAAO,CAACG,SAAS,CAACT,IAAI,IAAIrC,cAAc,CAAC;QAC3C,CAAC,MAAM,IAAIkC,SAAS,IAAIC,OAAO,EAAE;UAC/BQ,OAAO,CAACI,KAAK,CAACV,IAAI,EAAEf,UAAU,CAAC;QACjC;MACF,CAAC,MAAM;QAKLqB,OAAO,CAACvD,OAAO,CAAC,CAAC;QACjB,IAAIgD,WAAW,EAAE;UACfO,OAAO,CAACO,GAAG,CAACb,IAAI,IAAIrC,cAAc,CAAC;QACrC,CAAC,MAAM,IAAIkC,SAAS,IAAIC,OAAO,EAAE;UAC/B,IAAI7B,mBAAmB,EAAE;YACvBqC,OAAO,CAACO,GAAG,CAAClD,cAAc,CAAC,CAACqD,IAAI,CAAChB,IAAI,CAAC;UACxC,CAAC,MAAM;YACLM,OAAO,CAACW,IAAI,CAAChC,UAAU,CAAC,CAAC4B,GAAG,CAACb,IAAI,CAAC;UACpC;QACF;MACF;IACF,CAAC,MAAM,IAAIjC,eAAe,KAAK,YAAY,EAAE;MAC3C,IAAI8B,SAAS,IAAI5B,mBAAmB,EAAE;QACpC,MAAM,IAAIoC,KAAK,CAAC,wCAAwC,CAAC;MAC3D;MAEA,IAAIF,eAAe,EAAE;QAInBG,OAAO,CAACE,MAAM,CAAC,CAAC;QAChB,IAAIT,WAAW,EAAE;UACfO,OAAO,CAACM,OAAO,CAACZ,IAAI,IAAIrC,cAAc,CAAC;QACzC,CAAC,MAAM,IAAIkC,SAAS,EAAE;UACpBS,OAAO,CAACM,OAAO,CAACZ,IAAI,CAAC;QACvB,CAAC,MAAM,IAAIF,OAAO,EAAE;UAClBQ,OAAO,CAACM,OAAO,CAACjD,cAAc,CAAC,CAACqD,IAAI,CAAChB,IAAI,CAAC;QAC5C;MACF,CAAC,MAAM,IAAII,gBAAgB,EAAE;QAQ3BE,OAAO,CAACE,MAAM,CAAC,CAAC;QAChB,IAAIT,WAAW,EAAE;UACfO,OAAO,CAACM,OAAO,CAACZ,IAAI,IAAIrC,cAAc,CAAC;QACzC,CAAC,MAAM,IAAIkC,SAAS,EAAE;UACpBS,OAAO,CAACM,OAAO,CAACZ,IAAI,CAAC;QACvB,CAAC,MAAM,IAAIF,OAAO,EAAE;UAClBQ,OAAO,CAACI,KAAK,CAACV,IAAI,EAAEf,UAAU,CAAC;QACjC;MACF,CAAC,MAAM;QAKLqB,OAAO,CAACvD,OAAO,CAAC,CAAC;QACjB,IAAIgD,WAAW,EAAE;UACfO,OAAO,CAACO,GAAG,CAACb,IAAI,IAAIrC,cAAc,CAAC;QACrC,CAAC,MAAM,IAAIkC,SAAS,EAAE;UACpBS,OAAO,CAACO,GAAG,CAACb,IAAI,CAAC;QACnB,CAAC,MAAM,IAAIF,OAAO,EAAE;UAClB,IAAI7B,mBAAmB,EAAE;YACvBqC,OAAO,CAACO,GAAG,CAAClD,cAAc,CAAC,CAACqD,IAAI,CAAChB,IAAI,CAAC;UACxC,CAAC,MAAM;YACLM,OAAO,CAACO,GAAG,CAACb,IAAI,CAAC,CAACiB,IAAI,CAAChC,UAAU,CAAC;UACpC;QACF;MACF;IACF,CAAC,MAAM;MACL,MAAM,IAAIoB,KAAK,CAAC,4BAA4BtC,eAAe,IAAI,CAAC;IAClE;IAEA,MAAM;MAAEmD,UAAU;MAAEC;IAAW,CAAC,GAAGb,OAAO,CAACc,IAAI,CAAC,CAAC;IAEjD,IAAI,CAACC,iBAAiB,CAACH,UAAU,EAAE/C,cAAc,EAAEyB,UAAU,CAAC;IAE9D,IACE,CAACC,SAAS,IAAIC,OAAO,KACrB5B,eAAe,IACfiD,UAAU,CAACG,IAAI,KAAK,YAAY,EAChC;MACA,OAAOhE,kBAAkB,CAAC,CAACD,cAAc,CAAC,CAAC,CAAC,EAAE8D,UAAU,CAAC,CAAC;IAC5D;IACA,OAAOA,UAAU;EACnB;EAEAE,iBAAiBA,CACfH,UAAyB,EACzB/C,cAAc,GAAG,QAAQ,EACzByB,UAAU,GAAG,CAAC,EACd;IACA,IAAIzB,cAAc,KAAK,OAAO,EAAE;MAC9B,IAAI,IAAI,CAACoD,sBAAsB,CAACL,UAAU,CAAC,EAAE;IAC/C,CAAC,MAAM;MACL,IAAI,IAAI,CAACM,uBAAuB,CAACN,UAAU,EAAEtB,UAAU,CAAC,EAAE;IAC5D;IAEA,IAAI,CAACpB,YAAY,CAACiD,gBAAgB,CAAC,MAAM,EAAEP,UAAU,CAAC;EACxD;EAEAM,uBAAuBA,CAACN,UAAyB,EAAEtB,UAAkB,EAAE;IACrE,IACEsB,UAAU,CAACQ,MAAM,KAAK,CAAC,IACvBnE,mBAAmB,CAAC2D,UAAU,CAAC,CAAC,CAAC,CAAC,IAClCS,aAAa,CAACT,UAAU,CAAC,CAAC,CAAC,CAAC,EAC5B;MACA,MAAMU,eAAe,GAAG,IAAI,CAACpD,YAAY,CACtCqD,GAAG,CAAC,MAAM,CAAC,CACXxD,IAAI,CAAEC,CAAC,IAAyC;QAC/C,OAAOA,CAAC,CAACf,mBAAmB,CAAC,CAAC,IAAIoE,aAAa,CAACrD,CAAC,CAACwD,IAAI,CAAC;MACzD,CAAC,CAAC;MAEJ,IACE,CAAAF,eAAe,oBAAfA,eAAe,CAAEE,IAAI,CAACC,MAAM,CAACC,KAAK,MAAKd,UAAU,CAAC,CAAC,CAAC,CAACa,MAAM,CAACC,KAAK,IACjEC,2BAA2B,CAACL,eAAe,CAACE,IAAI,EAAEZ,UAAU,CAAC,CAAC,CAAC,CAAC,EAChE;QACA,OAAO,IAAI;MACb;IACF;IAEAA,UAAU,CAACgB,OAAO,CAACJ,IAAI,IAAI;MAEzBA,IAAI,CAACK,WAAW,GAAGvC,UAAU;IAC/B,CAAC,CAAC;IAEF,MAAMwC,UAAU,GAAG,IAAI,CAAC5D,YAAY,CAACqD,GAAG,CAAC,MAAM,CAAC,CAACxD,IAAI,CAACC,CAAC,IAAI;MAEzD,MAAM+D,GAAG,GAAG/D,CAAC,CAACwD,IAAI,CAACK,WAAW;MAC9B,OAAOG,MAAM,CAACC,QAAQ,CAACF,GAAG,CAAC,IAAIA,GAAG,GAAG,CAAC;IACxC,CAAC,CAAC;IAEF,IAAID,UAAU,EAAE;MACdA,UAAU,CAACI,YAAY,CAACtB,UAAU,CAAC;MACnC,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd;EAEAK,sBAAsBA,CAACL,UAAyB,EAAW;IACzD,MAAMuB,aAAa,GAAG,IAAIC,GAAG,CAACxB,UAAU,CAAC;IACzC,MAAMyB,kBAAsD,GAAG,IAAIC,GAAG,CAAC,CAAC;IAExE,KAAK,MAAMC,SAAS,IAAI3B,UAAU,EAAE;MAClC,IAAI3D,mBAAmB,CAACsF,SAAS,CAAC,IAAIlB,aAAa,CAACkB,SAAS,CAAC,EAAE;QAC9D,MAAMd,MAAM,GAAGc,SAAS,CAACd,MAAM,CAACC,KAAK;QACrC,IAAI,CAACW,kBAAkB,CAACG,GAAG,CAACf,MAAM,CAAC,EAAEY,kBAAkB,CAACI,GAAG,CAAChB,MAAM,EAAE,EAAE,CAAC;QACvEY,kBAAkB,CAACd,GAAG,CAACE,MAAM,CAAC,CAACiB,IAAI,CAACH,SAAS,CAAC;MAChD;IACF;IAEA,IAAII,cAAc,GAAG,IAAI;IACzB,KAAK,MAAMC,QAAQ,IAAI,IAAI,CAAC1E,YAAY,CAACqD,GAAG,CAAC,MAAM,CAAC,EAAE;MACpD,IAAIqB,QAAQ,CAAC3F,mBAAmB,CAAC,CAAC,IAAIoE,aAAa,CAACuB,QAAQ,CAACpB,IAAI,CAAC,EAAE;QAClEmB,cAAc,GAAGC,QAAQ;QAEzB,MAAMnB,MAAM,GAAGmB,QAAQ,CAACpB,IAAI,CAACC,MAAM,CAACC,KAAK;QACzC,MAAMmB,UAAU,GAAGR,kBAAkB,CAACd,GAAG,CAACE,MAAM,CAAC;QACjD,IAAI,CAACoB,UAAU,EAAE;QAEjB,KAAK,MAAMC,IAAI,IAAID,UAAU,EAAE;UAC7B,IAAI,CAACV,aAAa,CAACK,GAAG,CAACM,IAAI,CAAC,EAAE;UAC9B,IAAInB,2BAA2B,CAACiB,QAAQ,CAACpB,IAAI,EAAEsB,IAAI,CAAC,EAAE;YACpDX,aAAa,CAACY,MAAM,CAACD,IAAI,CAAC;UAC5B;QACF;MACF;IACF;IAEA,IAAIX,aAAa,CAACa,IAAI,KAAK,CAAC,EAAE,OAAO,IAAI;IAEzC,IAAIL,cAAc,EAAEA,cAAc,CAACM,WAAW,CAACC,KAAK,CAACC,IAAI,CAAChB,aAAa,CAAC,CAAC;IAEzE,OAAO,CAAC,CAACQ,cAAc;EACzB;AACF;AAACS,OAAA,CAAA9C,OAAA,GAAApD,cAAA;AAED,SAASmE,aAAaA,CAACG,IAAyB,EAAE;EAChD,OAAOA,IAAI,CAAC6B,UAAU,KAAK,MAAM,IAAI7B,IAAI,CAAC6B,UAAU,KAAK,QAAQ;AACnE;AAEA,SAASC,kBAAkBA,CAAC9B,IAAyB,EAAE;EACrD,OACGA,IAAI,CAAC+B,UAAU,CAACnC,MAAM,KAAK,CAAC,IAC3BI,IAAI,CAAC+B,UAAU,CAAC,CAAC,CAAC,CAACvC,IAAI,KAAK,0BAA0B,IACvDQ,IAAI,CAAC+B,UAAU,CAACnC,MAAM,KAAK,CAAC,IAC3BI,IAAI,CAAC+B,UAAU,CAAC,CAAC,CAAC,CAACvC,IAAI,KAAK,0BAA2B;AAE7D;AAEA,SAASwC,gBAAgBA,CAAChC,IAAyB,EAAE;EACnD,OACEA,IAAI,CAAC+B,UAAU,CAACnC,MAAM,GAAG,CAAC,IAC1BI,IAAI,CAAC+B,UAAU,CAAC,CAAC,CAAC,CAACvC,IAAI,KAAK,wBAAwB;AAExD;AAEA,SAASW,2BAA2BA,CAClC8B,MAA2B,EAC3BhC,MAA2B,EAClB;EACT,IAAI,CAACgC,MAAM,CAACF,UAAU,CAACnC,MAAM,EAAE;IAC7BqC,MAAM,CAACF,UAAU,GAAG9B,MAAM,CAAC8B,UAAU;IACrC,OAAO,IAAI;EACb;EACA,IAAI,CAAC9B,MAAM,CAAC8B,UAAU,CAACnC,MAAM,EAAE,OAAO,IAAI;EAE1C,IAAIkC,kBAAkB,CAACG,MAAM,CAAC,IAAIH,kBAAkB,CAAC7B,MAAM,CAAC,EAAE,OAAO,KAAK;EAE1E,IAAI+B,gBAAgB,CAAC/B,MAAM,CAAC,EAAE;IAC5B,IAAI+B,gBAAgB,CAACC,MAAM,CAAC,EAAE;MAC5BhC,MAAM,CAAC8B,UAAU,CAAC,CAAC,CAAC,GAAGzG,eAAe,CACpC2E,MAAM,CAAC8B,UAAU,CAAC,CAAC,CAAC,CAACG,KAAK,EAC1B7G,UAAU,CAAC,SAAS,CACtB,CAAC;IACH,CAAC,MAAM;MACL4G,MAAM,CAACF,UAAU,CAACI,OAAO,CAAClC,MAAM,CAAC8B,UAAU,CAACK,KAAK,CAAC,CAAC,CAAC;IACtD;EACF;EAEAH,MAAM,CAACF,UAAU,CAACb,IAAI,CAAC,GAAGjB,MAAM,CAAC8B,UAAU,CAAC;EAE5C,OAAO,IAAI;AACb", "ignoreList": []}