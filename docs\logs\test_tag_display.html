<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .tag-category {
            margin-bottom: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            overflow: hidden;
        }
        .tag-category-header {
            background: #f8f9fa;
            padding: 12px 16px;
            font-weight: bold;
            border-bottom: 1px solid #e0e0e0;
        }
        .tag-list {
            padding: 16px;
        }
        .tag-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            margin-bottom: 4px;
            background: #f8f9fa;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .tag-item:hover {
            background: #e9ecef;
        }
        .tag-name {
            font-weight: 500;
        }
        .tag-count {
            background: #007bff;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        .empty-message {
            text-align: center;
            color: #6c757d;
            padding: 20px;
            font-style: italic;
        }
        .test-info {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>标签显示测试页面</h1>
        
        <div class="test-info">
            <strong>测试目的：</strong>验证前端是否能正确显示后端返回的元数据标签
        </div>

        <div id="tag-display-area">
            <p>正在加载标签数据...</p>
        </div>
    </div>

    <script>
        // 模拟后端返回的标签树数据
        const mockTagTree = {
            "tags": {
                "metadata": {
                    "camera: SONY ILCE-7RM4": 6,
                    "iso: ISO-100": 6,
                    "color_standard: sRGB": 6,
                    "shooting_date: 2023/8/21": 6
                },
                "cv": {},
                "user": {},
                "ai": {}
            },
            "properties": {
                "fileType: jpg": 6,
                "dimensions: 3508x2339": 6,
                "resolution: 300 DPI": 6,
                "color_depth: 24-bit": 6
            },
            "custom": []
        };

        // 渲染标签的函数
        function renderTags() {
            const container = document.getElementById('tag-display-area');
            const categories = [
                { key: 'properties', name: '属性标签', data: mockTagTree.properties },
                { key: 'metadata', name: '元数据标签', data: mockTagTree.tags.metadata },
                { key: 'cv', name: 'CV标签', data: mockTagTree.tags.cv },
                { key: 'user', name: '用户标签', data: mockTagTree.tags.user },
                { key: 'ai', name: 'AI标签', data: mockTagTree.tags.ai }
            ];

            let html = '';
            
            categories.forEach(category => {
                const tagCount = Object.keys(category.data).length;
                
                html += `
                    <div class="tag-category">
                        <div class="tag-category-header">
                            ${category.name} (${tagCount})
                        </div>
                        <div class="tag-list">
                `;
                
                if (tagCount === 0) {
                    html += '<div class="empty-message">暂无标签</div>';
                } else {
                    Object.entries(category.data).forEach(([tagName, tagCount]) => {
                        html += `
                            <div class="tag-item">
                                <span class="tag-name">${tagName}</span>
                                <span class="tag-count">${tagCount}</span>
                            </div>
                        `;
                    });
                }
                
                html += `
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        // 页面加载完成后渲染标签
        document.addEventListener('DOMContentLoaded', renderTags);
    </script>
</body>
</html>
