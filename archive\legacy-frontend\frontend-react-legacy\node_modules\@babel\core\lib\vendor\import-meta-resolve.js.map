{"version": 3, "names": ["_assert", "data", "require", "_fs", "_interopRequireWildcard", "_process", "_url", "_path", "_module", "_v", "_util", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "own$1", "classRegExp", "kTypes", "Set", "codes", "formatList", "array", "type", "length", "join", "slice", "messages", "Map", "nodeInternalPrefix", "userStackTraceLimit", "ERR_INVALID_ARG_TYPE", "createError", "name", "expected", "actual", "assert", "Array", "isArray", "message", "endsWith", "includes", "types", "instances", "other", "value", "push", "toLowerCase", "exec", "pos", "indexOf", "determineSpecificType", "TypeError", "ERR_INVALID_MODULE_SPECIFIER", "request", "reason", "base", "undefined", "ERR_INVALID_PACKAGE_CONFIG", "path", "Error", "ERR_INVALID_PACKAGE_TARGET", "packagePath", "key", "target", "isImport", "relatedError", "startsWith", "JSON", "stringify", "ERR_MODULE_NOT_FOUND", "exactUrl", "ERR_NETWORK_IMPORT_DISALLOWED", "ERR_PACKAGE_IMPORT_NOT_DEFINED", "specifier", "ERR_PACKAGE_PATH_NOT_EXPORTED", "subpath", "ERR_UNSUPPORTED_DIR_IMPORT", "ERR_UNSUPPORTED_RESOLVE_REQUEST", "ERR_UNKNOWN_FILE_EXTENSION", "extension", "ERR_INVALID_ARG_VALUE", "inspected", "inspect", "sym", "constructor", "makeNodeErrorWithCode", "Base", "NodeError", "parameters", "limit", "stackTraceLimit", "isErrorStackTraceLimitWritable", "error", "getMessage", "defineProperties", "enumerable", "writable", "configurable", "toString", "captureLargerStackTrace", "code", "v8", "startupSnapshot", "isBuildingSnapshot", "_unused", "desc", "isExtensible", "hideStackFrames", "wrappedFunction", "hidden", "stackTraceLimitIsWritable", "Number", "POSITIVE_INFINITY", "captureStackTrace", "self", "Reflect", "apply", "regex", "<PERSON><PERSON><PERSON><PERSON>", "unshift", "format", "String", "depth", "colors", "hasOwnProperty$1", "ERR_INVALID_PACKAGE_CONFIG$1", "cache", "read", "jsonPath", "existing", "string", "fs", "readFileSync", "toNamespacedPath", "exception", "result", "exists", "p<PERSON><PERSON><PERSON><PERSON>", "main", "exports", "imports", "parsed", "parse", "error_", "cause", "fileURLToPath", "getPackageScopeConfig", "resolved", "packageJSONUrl", "URL", "packageJSONPath", "pathname", "packageConfig", "lastPackageJSONUrl", "getPackageType", "url", "extensionFormatMap", "mimeToFormat", "mime", "test", "protocolHandlers", "getDataProtocolModuleFormat", "getFileProtocolModuleFormat", "getHttpProtocolModuleFormat", "node:", "extname", "index", "codePointAt", "_context", "ignoreErrors", "packageType", "filepath", "defaultGetFormatWithoutErrors", "context", "protocol", "DEFAULT_CONDITIONS", "freeze", "DEFAULT_CONDITIONS_SET", "getDefaultConditions", "getDefaultConditionsSet", "getConditionsSet", "conditions", "RegExpPrototypeSymbolReplace", "RegExp", "prototype", "Symbol", "replace", "own", "invalidSegmentRegEx", "deprecatedInvalidSegmentRegEx", "invalidPackageNameRegEx", "patternRegEx", "encodedSeparatorRegEx", "emittedPackageWarnings", "doubleSlashRegEx", "emitInvalidSegmentDeprecation", "match", "packageJsonUrl", "internal", "<PERSON><PERSON><PERSON><PERSON>", "process", "noDeprecation", "double", "emitWarning", "emitLegacyIndexDeprecation", "parentURL", "href", "url<PERSON><PERSON>", "URL$1", "basePath", "resolve", "tryStatSync", "statSync", "_unused2", "fileExists", "stats", "throwIfNoEntry", "isFile", "legacyMainResolve", "guess", "tries", "finalizeResolution", "preserveSymlinks", "filePath", "isDirectory", "real", "realpathSync", "search", "hash", "pathToFileURL", "sep", "importNotDefined", "exportsNotFound", "throwInvalidSubpath", "invalidPackageTarget", "resolvePackageTargetString", "pattern", "isPathMap", "isURL", "_unused3", "exportTarget", "packageResolve", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "isArrayIndex", "keyNumber", "resolvePackageTarget", "packageSubpath", "targetList", "lastException", "targetItem", "resolveResult", "keys", "getOwnPropertyNames", "<PERSON><PERSON><PERSON><PERSON>", "isConditionalExportsMainSugar", "isConditionalSugar", "keyIndex", "currentIsConditionalSugar", "emitTrailingSlashPatternDeprecation", "pjsonUrl", "add", "packageExportsResolve", "bestMatch", "bestMatchSubpath", "patternIndex", "patternTrailer", "patternKeyCompare", "lastIndexOf", "a", "b", "aPatternIndex", "bPatternIndex", "baseLengthA", "baseLengthB", "packageImportsResolve", "parsePackageName", "separatorIndex", "validPackageName", "isScoped", "packageName", "builtinModules", "packageJsonPath", "last<PERSON><PERSON>", "stat", "isRelativeSpecifier", "shouldBeTreatedAsRelativeOrAbsolutePath", "moduleResolve", "isData", "isRemote", "checkIfDisallowedImport", "parsedParentURL", "parentProtocol", "parsedProtocol", "Boolean", "throwIfInvalidParentURL", "defaultResolve", "_unused4", "_unused5", "maybeReturn", "parent"], "sources": ["../../src/vendor/import-meta-resolve.js"], "sourcesContent": ["\n/****************************************************************************\\\n *                         NOTE FROM BABEL AUTHORS                          *\n * This file is inlined from https://github.com/wooorm/import-meta-resolve, *\n * because we need to compile it to CommonJS.                               *\n\\****************************************************************************/\n\n/*\n(The MIT License)\n\nCopyright (c) 2021 Titus Wormer <mailto:<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n---\n\nThis is a derivative work based on:\n<https://github.com/nodejs/node>.\nWhich is licensed:\n\n\"\"\"\nCopyright Node.js contributors. All rights reserved.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to\ndeal in the Software without restriction, including without limitation the\nrights to use, copy, modify, merge, publish, distribute, sublicense, and/or\nsell copies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\nIN THE SOFTWARE.\n\"\"\"\n\nThis license applies to parts of Node.js originating from the\nhttps://github.com/joyent/node repository:\n\n\"\"\"\nCopyright Joyent, Inc. and other Node contributors. All rights reserved.\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to\ndeal in the Software without restriction, including without limitation the\nrights to use, copy, modify, merge, publish, distribute, sublicense, and/or\nsell copies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\nIN THE SOFTWARE.\n\"\"\"\n*/\n\nimport assert from 'assert';\nimport fs, { realpathSync, statSync } from 'fs';\nimport process from 'process';\nimport { fileURLToPath, URL as URL$1, pathToFileURL } from 'url';\nimport path from 'path';\nimport { builtinModules } from 'module';\nimport v8 from 'v8';\nimport { format, inspect } from 'util';\n\n/**\n * @typedef ErrnoExceptionFields\n * @property {number | undefined} [errnode]\n * @property {string | undefined} [code]\n * @property {string | undefined} [path]\n * @property {string | undefined} [syscall]\n * @property {string | undefined} [url]\n *\n * @typedef {Error & ErrnoExceptionFields} ErrnoException\n */\n\n\nconst own$1 = {}.hasOwnProperty;\n\nconst classRegExp = /^([A-Z][a-z\\d]*)+$/;\n// Sorted by a rough estimate on most frequently used entries.\nconst kTypes = new Set([\n  'string',\n  'function',\n  'number',\n  'object',\n  // Accept 'Function' and 'Object' as alternative to the lower cased version.\n  'Function',\n  'Object',\n  'boolean',\n  'bigint',\n  'symbol'\n]);\n\nconst codes = {};\n\n/**\n * Create a list string in the form like 'A and B' or 'A, B, ..., and Z'.\n * We cannot use Intl.ListFormat because it's not available in\n * --without-intl builds.\n *\n * @param {Array<string>} array\n *   An array of strings.\n * @param {string} [type]\n *   The list type to be inserted before the last element.\n * @returns {string}\n */\nfunction formatList(array, type = 'and') {\n  return array.length < 3\n    ? array.join(` ${type} `)\n    : `${array.slice(0, -1).join(', ')}, ${type} ${array[array.length - 1]}`\n}\n\n/** @type {Map<string, MessageFunction | string>} */\nconst messages = new Map();\nconst nodeInternalPrefix = '__node_internal_';\n/** @type {number} */\nlet userStackTraceLimit;\n\ncodes.ERR_INVALID_ARG_TYPE = createError(\n  'ERR_INVALID_ARG_TYPE',\n  /**\n   * @param {string} name\n   * @param {Array<string> | string} expected\n   * @param {unknown} actual\n   */\n  (name, expected, actual) => {\n    assert(typeof name === 'string', \"'name' must be a string\");\n    if (!Array.isArray(expected)) {\n      expected = [expected];\n    }\n\n    let message = 'The ';\n    if (name.endsWith(' argument')) {\n      // For cases like 'first argument'\n      message += `${name} `;\n    } else {\n      const type = name.includes('.') ? 'property' : 'argument';\n      message += `\"${name}\" ${type} `;\n    }\n\n    message += 'must be ';\n\n    /** @type {Array<string>} */\n    const types = [];\n    /** @type {Array<string>} */\n    const instances = [];\n    /** @type {Array<string>} */\n    const other = [];\n\n    for (const value of expected) {\n      assert(\n        typeof value === 'string',\n        'All expected entries have to be of type string'\n      );\n\n      if (kTypes.has(value)) {\n        types.push(value.toLowerCase());\n      } else if (classRegExp.exec(value) === null) {\n        assert(\n          value !== 'object',\n          'The value \"object\" should be written as \"Object\"'\n        );\n        other.push(value);\n      } else {\n        instances.push(value);\n      }\n    }\n\n    // Special handle `object` in case other instances are allowed to outline\n    // the differences between each other.\n    if (instances.length > 0) {\n      const pos = types.indexOf('object');\n      if (pos !== -1) {\n        types.slice(pos, 1);\n        instances.push('Object');\n      }\n    }\n\n    if (types.length > 0) {\n      message += `${types.length > 1 ? 'one of type' : 'of type'} ${formatList(\n        types,\n        'or'\n      )}`;\n      if (instances.length > 0 || other.length > 0) message += ' or ';\n    }\n\n    if (instances.length > 0) {\n      message += `an instance of ${formatList(instances, 'or')}`;\n      if (other.length > 0) message += ' or ';\n    }\n\n    if (other.length > 0) {\n      if (other.length > 1) {\n        message += `one of ${formatList(other, 'or')}`;\n      } else {\n        if (other[0].toLowerCase() !== other[0]) message += 'an ';\n        message += `${other[0]}`;\n      }\n    }\n\n    message += `. Received ${determineSpecificType(actual)}`;\n\n    return message\n  },\n  TypeError\n);\n\ncodes.ERR_INVALID_MODULE_SPECIFIER = createError(\n  'ERR_INVALID_MODULE_SPECIFIER',\n  /**\n   * @param {string} request\n   * @param {string} reason\n   * @param {string} [base]\n   */\n  (request, reason, base = undefined) => {\n    return `Invalid module \"${request}\" ${reason}${\n      base ? ` imported from ${base}` : ''\n    }`\n  },\n  TypeError\n);\n\ncodes.ERR_INVALID_PACKAGE_CONFIG = createError(\n  'ERR_INVALID_PACKAGE_CONFIG',\n  /**\n   * @param {string} path\n   * @param {string} [base]\n   * @param {string} [message]\n   */\n  (path, base, message) => {\n    return `Invalid package config ${path}${\n      base ? ` while importing ${base}` : ''\n    }${message ? `. ${message}` : ''}`\n  },\n  Error\n);\n\ncodes.ERR_INVALID_PACKAGE_TARGET = createError(\n  'ERR_INVALID_PACKAGE_TARGET',\n  /**\n   * @param {string} packagePath\n   * @param {string} key\n   * @param {unknown} target\n   * @param {boolean} [isImport=false]\n   * @param {string} [base]\n   */\n  (packagePath, key, target, isImport = false, base = undefined) => {\n    const relatedError =\n      typeof target === 'string' &&\n      !isImport &&\n      target.length > 0 &&\n      !target.startsWith('./');\n    if (key === '.') {\n      assert(isImport === false);\n      return (\n        `Invalid \"exports\" main target ${JSON.stringify(target)} defined ` +\n        `in the package config ${packagePath}package.json${\n          base ? ` imported from ${base}` : ''\n        }${relatedError ? '; targets must start with \"./\"' : ''}`\n      )\n    }\n\n    return `Invalid \"${\n      isImport ? 'imports' : 'exports'\n    }\" target ${JSON.stringify(\n      target\n    )} defined for '${key}' in the package config ${packagePath}package.json${\n      base ? ` imported from ${base}` : ''\n    }${relatedError ? '; targets must start with \"./\"' : ''}`\n  },\n  Error\n);\n\ncodes.ERR_MODULE_NOT_FOUND = createError(\n  'ERR_MODULE_NOT_FOUND',\n  /**\n   * @param {string} path\n   * @param {string} base\n   * @param {boolean} [exactUrl]\n   */\n  (path, base, exactUrl = false) => {\n    return `Cannot find ${\n      exactUrl ? 'module' : 'package'\n    } '${path}' imported from ${base}`\n  },\n  Error\n);\n\ncodes.ERR_NETWORK_IMPORT_DISALLOWED = createError(\n  'ERR_NETWORK_IMPORT_DISALLOWED',\n  \"import of '%s' by %s is not supported: %s\",\n  Error\n);\n\ncodes.ERR_PACKAGE_IMPORT_NOT_DEFINED = createError(\n  'ERR_PACKAGE_IMPORT_NOT_DEFINED',\n  /**\n   * @param {string} specifier\n   * @param {string} packagePath\n   * @param {string} base\n   */\n  (specifier, packagePath, base) => {\n    return `Package import specifier \"${specifier}\" is not defined${\n      packagePath ? ` in package ${packagePath}package.json` : ''\n    } imported from ${base}`\n  },\n  TypeError\n);\n\ncodes.ERR_PACKAGE_PATH_NOT_EXPORTED = createError(\n  'ERR_PACKAGE_PATH_NOT_EXPORTED',\n  /**\n   * @param {string} packagePath\n   * @param {string} subpath\n   * @param {string} [base]\n   */\n  (packagePath, subpath, base = undefined) => {\n    if (subpath === '.')\n      return `No \"exports\" main defined in ${packagePath}package.json${\n        base ? ` imported from ${base}` : ''\n      }`\n    return `Package subpath '${subpath}' is not defined by \"exports\" in ${packagePath}package.json${\n      base ? ` imported from ${base}` : ''\n    }`\n  },\n  Error\n);\n\ncodes.ERR_UNSUPPORTED_DIR_IMPORT = createError(\n  'ERR_UNSUPPORTED_DIR_IMPORT',\n  \"Directory import '%s' is not supported \" +\n    'resolving ES modules imported from %s',\n  Error\n);\n\ncodes.ERR_UNSUPPORTED_RESOLVE_REQUEST = createError(\n  'ERR_UNSUPPORTED_RESOLVE_REQUEST',\n  'Failed to resolve module specifier \"%s\" from \"%s\": Invalid relative URL or base scheme is not hierarchical.',\n  TypeError\n);\n\ncodes.ERR_UNKNOWN_FILE_EXTENSION = createError(\n  'ERR_UNKNOWN_FILE_EXTENSION',\n  /**\n   * @param {string} extension\n   * @param {string} path\n   */\n  (extension, path) => {\n    return `Unknown file extension \"${extension}\" for ${path}`\n  },\n  TypeError\n);\n\ncodes.ERR_INVALID_ARG_VALUE = createError(\n  'ERR_INVALID_ARG_VALUE',\n  /**\n   * @param {string} name\n   * @param {unknown} value\n   * @param {string} [reason='is invalid']\n   */\n  (name, value, reason = 'is invalid') => {\n    let inspected = inspect(value);\n\n    if (inspected.length > 128) {\n      inspected = `${inspected.slice(0, 128)}...`;\n    }\n\n    const type = name.includes('.') ? 'property' : 'argument';\n\n    return `The ${type} '${name}' ${reason}. Received ${inspected}`\n  },\n  TypeError\n  // Note: extra classes have been shaken out.\n  // , RangeError\n);\n\n/**\n * Utility function for registering the error codes. Only used here. Exported\n * *only* to allow for testing.\n * @param {string} sym\n * @param {MessageFunction | string} value\n * @param {ErrorConstructor} constructor\n * @returns {new (...parameters: Array<any>) => Error}\n */\nfunction createError(sym, value, constructor) {\n  // Special case for SystemError that formats the error message differently\n  // The SystemErrors only have SystemError as their base classes.\n  messages.set(sym, value);\n\n  return makeNodeErrorWithCode(constructor, sym)\n}\n\n/**\n * @param {ErrorConstructor} Base\n * @param {string} key\n * @returns {ErrorConstructor}\n */\nfunction makeNodeErrorWithCode(Base, key) {\n  // @ts-expect-error It’s a Node error.\n  return NodeError\n  /**\n   * @param {Array<unknown>} parameters\n   */\n  function NodeError(...parameters) {\n    const limit = Error.stackTraceLimit;\n    if (isErrorStackTraceLimitWritable()) Error.stackTraceLimit = 0;\n    const error = new Base();\n    // Reset the limit and setting the name property.\n    if (isErrorStackTraceLimitWritable()) Error.stackTraceLimit = limit;\n    const message = getMessage(key, parameters, error);\n    Object.defineProperties(error, {\n      // Note: no need to implement `kIsNodeError` symbol, would be hard,\n      // probably.\n      message: {\n        value: message,\n        enumerable: false,\n        writable: true,\n        configurable: true\n      },\n      toString: {\n        /** @this {Error} */\n        value() {\n          return `${this.name} [${key}]: ${this.message}`\n        },\n        enumerable: false,\n        writable: true,\n        configurable: true\n      }\n    });\n\n    captureLargerStackTrace(error);\n    // @ts-expect-error It’s a Node error.\n    error.code = key;\n    return error\n  }\n}\n\n/**\n * @returns {boolean}\n */\nfunction isErrorStackTraceLimitWritable() {\n  // Do no touch Error.stackTraceLimit as V8 would attempt to install\n  // it again during deserialization.\n  try {\n    if (v8.startupSnapshot.isBuildingSnapshot()) {\n      return false\n    }\n  } catch {}\n\n  const desc = Object.getOwnPropertyDescriptor(Error, 'stackTraceLimit');\n  if (desc === undefined) {\n    return Object.isExtensible(Error)\n  }\n\n  return own$1.call(desc, 'writable') && desc.writable !== undefined\n    ? desc.writable\n    : desc.set !== undefined\n}\n\n/**\n * This function removes unnecessary frames from Node.js core errors.\n * @template {(...parameters: unknown[]) => unknown} T\n * @param {T} wrappedFunction\n * @returns {T}\n */\nfunction hideStackFrames(wrappedFunction) {\n  // We rename the functions that will be hidden to cut off the stacktrace\n  // at the outermost one\n  const hidden = nodeInternalPrefix + wrappedFunction.name;\n  Object.defineProperty(wrappedFunction, 'name', {value: hidden});\n  return wrappedFunction\n}\n\nconst captureLargerStackTrace = hideStackFrames(\n  /**\n   * @param {Error} error\n   * @returns {Error}\n   */\n  // @ts-expect-error: fine\n  function (error) {\n    const stackTraceLimitIsWritable = isErrorStackTraceLimitWritable();\n    if (stackTraceLimitIsWritable) {\n      userStackTraceLimit = Error.stackTraceLimit;\n      Error.stackTraceLimit = Number.POSITIVE_INFINITY;\n    }\n\n    Error.captureStackTrace(error);\n\n    // Reset the limit\n    if (stackTraceLimitIsWritable) Error.stackTraceLimit = userStackTraceLimit;\n\n    return error\n  }\n);\n\n/**\n * @param {string} key\n * @param {Array<unknown>} parameters\n * @param {Error} self\n * @returns {string}\n */\nfunction getMessage(key, parameters, self) {\n  const message = messages.get(key);\n  assert(message !== undefined, 'expected `message` to be found');\n\n  if (typeof message === 'function') {\n    assert(\n      message.length <= parameters.length, // Default options do not count.\n      `Code: ${key}; The provided arguments length (${parameters.length}) does not ` +\n        `match the required ones (${message.length}).`\n    );\n    return Reflect.apply(message, self, parameters)\n  }\n\n  const regex = /%[dfijoOs]/g;\n  let expectedLength = 0;\n  while (regex.exec(message) !== null) expectedLength++;\n  assert(\n    expectedLength === parameters.length,\n    `Code: ${key}; The provided arguments length (${parameters.length}) does not ` +\n      `match the required ones (${expectedLength}).`\n  );\n  if (parameters.length === 0) return message\n\n  parameters.unshift(message);\n  return Reflect.apply(format, null, parameters)\n}\n\n/**\n * Determine the specific type of a value for type-mismatch errors.\n * @param {unknown} value\n * @returns {string}\n */\nfunction determineSpecificType(value) {\n  if (value === null || value === undefined) {\n    return String(value)\n  }\n\n  if (typeof value === 'function' && value.name) {\n    return `function ${value.name}`\n  }\n\n  if (typeof value === 'object') {\n    if (value.constructor && value.constructor.name) {\n      return `an instance of ${value.constructor.name}`\n    }\n\n    return `${inspect(value, {depth: -1})}`\n  }\n\n  let inspected = inspect(value, {colors: false});\n\n  if (inspected.length > 28) {\n    inspected = `${inspected.slice(0, 25)}...`;\n  }\n\n  return `type ${typeof value} (${inspected})`\n}\n\n// Manually “tree shaken” from:\n// <https://github.com/nodejs/node/blob/7c3dce0/lib/internal/modules/package_json_reader.js>\n// Last checked on: Apr 29, 2023.\n// Removed the native dependency.\n// Also: no need to cache, we do that in resolve already.\n\n\nconst hasOwnProperty$1 = {}.hasOwnProperty;\n\nconst {ERR_INVALID_PACKAGE_CONFIG: ERR_INVALID_PACKAGE_CONFIG$1} = codes;\n\n/** @type {Map<string, PackageConfig>} */\nconst cache = new Map();\n\n/**\n * @param {string} jsonPath\n * @param {{specifier: URL | string, base?: URL}} options\n * @returns {PackageConfig}\n */\nfunction read(jsonPath, {base, specifier}) {\n  const existing = cache.get(jsonPath);\n\n  if (existing) {\n    return existing\n  }\n\n  /** @type {string | undefined} */\n  let string;\n\n  try {\n    string = fs.readFileSync(path.toNamespacedPath(jsonPath), 'utf8');\n  } catch (error) {\n    const exception = /** @type {ErrnoException} */ (error);\n\n    if (exception.code !== 'ENOENT') {\n      throw exception\n    }\n  }\n\n  /** @type {PackageConfig} */\n  const result = {\n    exists: false,\n    pjsonPath: jsonPath,\n    main: undefined,\n    name: undefined,\n    type: 'none', // Ignore unknown types for forwards compatibility\n    exports: undefined,\n    imports: undefined\n  };\n\n  if (string !== undefined) {\n    /** @type {Record<string, unknown>} */\n    let parsed;\n\n    try {\n      parsed = JSON.parse(string);\n    } catch (error_) {\n      const cause = /** @type {ErrnoException} */ (error_);\n      const error = new ERR_INVALID_PACKAGE_CONFIG$1(\n        jsonPath,\n        (base ? `\"${specifier}\" from ` : '') + fileURLToPath(base || specifier),\n        cause.message\n      );\n      error.cause = cause;\n      throw error\n    }\n\n    result.exists = true;\n\n    if (\n      hasOwnProperty$1.call(parsed, 'name') &&\n      typeof parsed.name === 'string'\n    ) {\n      result.name = parsed.name;\n    }\n\n    if (\n      hasOwnProperty$1.call(parsed, 'main') &&\n      typeof parsed.main === 'string'\n    ) {\n      result.main = parsed.main;\n    }\n\n    if (hasOwnProperty$1.call(parsed, 'exports')) {\n      // @ts-expect-error: assume valid.\n      result.exports = parsed.exports;\n    }\n\n    if (hasOwnProperty$1.call(parsed, 'imports')) {\n      // @ts-expect-error: assume valid.\n      result.imports = parsed.imports;\n    }\n\n    // Ignore unknown types for forwards compatibility\n    if (\n      hasOwnProperty$1.call(parsed, 'type') &&\n      (parsed.type === 'commonjs' || parsed.type === 'module')\n    ) {\n      result.type = parsed.type;\n    }\n  }\n\n  cache.set(jsonPath, result);\n\n  return result\n}\n\n/**\n * @param {URL | string} resolved\n * @returns {PackageConfig}\n */\nfunction getPackageScopeConfig(resolved) {\n  // Note: in Node, this is now a native module.\n  let packageJSONUrl = new URL('package.json', resolved);\n\n  while (true) {\n    const packageJSONPath = packageJSONUrl.pathname;\n    if (packageJSONPath.endsWith('node_modules/package.json')) {\n      break\n    }\n\n    const packageConfig = read(fileURLToPath(packageJSONUrl), {\n      specifier: resolved\n    });\n\n    if (packageConfig.exists) {\n      return packageConfig\n    }\n\n    const lastPackageJSONUrl = packageJSONUrl;\n    packageJSONUrl = new URL('../package.json', packageJSONUrl);\n\n    // Terminates at root where ../package.json equals ../../package.json\n    // (can't just check \"/package.json\" for Windows support).\n    if (packageJSONUrl.pathname === lastPackageJSONUrl.pathname) {\n      break\n    }\n  }\n\n  const packageJSONPath = fileURLToPath(packageJSONUrl);\n  // ^^ Note: in Node, this is now a native module.\n\n  return {\n    pjsonPath: packageJSONPath,\n    exists: false,\n    type: 'none'\n  }\n}\n\n/**\n * Returns the package type for a given URL.\n * @param {URL} url - The URL to get the package type for.\n * @returns {PackageType}\n */\nfunction getPackageType(url) {\n  // To do @anonrig: Write a C++ function that returns only \"type\".\n  return getPackageScopeConfig(url).type\n}\n\n// Manually “tree shaken” from:\n// <https://github.com/nodejs/node/blob/7c3dce0/lib/internal/modules/esm/get_format.js>\n// Last checked on: Apr 29, 2023.\n\n\nconst {ERR_UNKNOWN_FILE_EXTENSION} = codes;\n\nconst hasOwnProperty = {}.hasOwnProperty;\n\n/** @type {Record<string, string>} */\nconst extensionFormatMap = {\n  // @ts-expect-error: hush.\n  __proto__: null,\n  '.cjs': 'commonjs',\n  '.js': 'module',\n  '.json': 'json',\n  '.mjs': 'module'\n};\n\n/**\n * @param {string | null} mime\n * @returns {string | null}\n */\nfunction mimeToFormat(mime) {\n  if (\n    mime &&\n    /\\s*(text|application)\\/javascript\\s*(;\\s*charset=utf-?8\\s*)?/i.test(mime)\n  )\n    return 'module'\n  if (mime === 'application/json') return 'json'\n  return null\n}\n\n/**\n * @callback ProtocolHandler\n * @param {URL} parsed\n * @param {{parentURL: string, source?: Buffer}} context\n * @param {boolean} ignoreErrors\n * @returns {string | null | void}\n */\n\n/**\n * @type {Record<string, ProtocolHandler>}\n */\nconst protocolHandlers = {\n  // @ts-expect-error: hush.\n  __proto__: null,\n  'data:': getDataProtocolModuleFormat,\n  'file:': getFileProtocolModuleFormat,\n  'http:': getHttpProtocolModuleFormat,\n  'https:': getHttpProtocolModuleFormat,\n  'node:'() {\n    return 'builtin'\n  }\n};\n\n/**\n * @param {URL} parsed\n */\nfunction getDataProtocolModuleFormat(parsed) {\n  const {1: mime} = /^([^/]+\\/[^;,]+)[^,]*?(;base64)?,/.exec(\n    parsed.pathname\n  ) || [null, null, null];\n  return mimeToFormat(mime)\n}\n\n/**\n * Returns the file extension from a URL.\n *\n * Should give similar result to\n * `require('node:path').extname(require('node:url').fileURLToPath(url))`\n * when used with a `file:` URL.\n *\n * @param {URL} url\n * @returns {string}\n */\nfunction extname(url) {\n  const pathname = url.pathname;\n  let index = pathname.length;\n\n  while (index--) {\n    const code = pathname.codePointAt(index);\n\n    if (code === 47 /* `/` */) {\n      return ''\n    }\n\n    if (code === 46 /* `.` */) {\n      return pathname.codePointAt(index - 1) === 47 /* `/` */\n        ? ''\n        : pathname.slice(index)\n    }\n  }\n\n  return ''\n}\n\n/**\n * @type {ProtocolHandler}\n */\nfunction getFileProtocolModuleFormat(url, _context, ignoreErrors) {\n  const value = extname(url);\n\n  if (value === '.js') {\n    const packageType = getPackageType(url);\n\n    if (packageType !== 'none') {\n      return packageType\n    }\n\n    return 'commonjs'\n  }\n\n  if (value === '') {\n    const packageType = getPackageType(url);\n\n    // Legacy behavior\n    if (packageType === 'none' || packageType === 'commonjs') {\n      return 'commonjs'\n    }\n\n    // Note: we don’t implement WASM, so we don’t need\n    // `getFormatOfExtensionlessFile` from `formats`.\n    return 'module'\n  }\n\n  const format = extensionFormatMap[value];\n  if (format) return format\n\n  // Explicit undefined return indicates load hook should rerun format check\n  if (ignoreErrors) {\n    return undefined\n  }\n\n  const filepath = fileURLToPath(url);\n  throw new ERR_UNKNOWN_FILE_EXTENSION(value, filepath)\n}\n\nfunction getHttpProtocolModuleFormat() {\n  // To do: HTTPS imports.\n}\n\n/**\n * @param {URL} url\n * @param {{parentURL: string}} context\n * @returns {string | null}\n */\nfunction defaultGetFormatWithoutErrors(url, context) {\n  const protocol = url.protocol;\n\n  if (!hasOwnProperty.call(protocolHandlers, protocol)) {\n    return null\n  }\n\n  return protocolHandlers[protocol](url, context, true) || null\n}\n\n// Manually “tree shaken” from:\n// <https://github.com/nodejs/node/blob/81a9a97/lib/internal/modules/esm/utils.js>\n// Last checked on: Apr 29, 2023.\n\n\nconst {ERR_INVALID_ARG_VALUE} = codes;\n\n// In Node itself these values are populated from CLI arguments, before any\n// user code runs.\n// Here we just define the defaults.\nconst DEFAULT_CONDITIONS = Object.freeze(['node', 'import']);\nconst DEFAULT_CONDITIONS_SET = new Set(DEFAULT_CONDITIONS);\n\n/**\n * Returns the default conditions for ES module loading.\n */\nfunction getDefaultConditions() {\n  return DEFAULT_CONDITIONS\n}\n\n/**\n * Returns the default conditions for ES module loading, as a Set.\n */\nfunction getDefaultConditionsSet() {\n  return DEFAULT_CONDITIONS_SET\n}\n\n/**\n * @param {Array<string>} [conditions]\n * @returns {Set<string>}\n */\nfunction getConditionsSet(conditions) {\n  if (conditions !== undefined && conditions !== getDefaultConditions()) {\n    if (!Array.isArray(conditions)) {\n      throw new ERR_INVALID_ARG_VALUE(\n        'conditions',\n        conditions,\n        'expected an array'\n      )\n    }\n\n    return new Set(conditions)\n  }\n\n  return getDefaultConditionsSet()\n}\n\n// Manually “tree shaken” from:\n// <https://github.com/nodejs/node/blob/81a9a97/lib/internal/modules/esm/resolve.js>\n// Last checked on: Apr 29, 2023.\n\n\nconst RegExpPrototypeSymbolReplace = RegExp.prototype[Symbol.replace];\n\nconst {\n  ERR_NETWORK_IMPORT_DISALLOWED,\n  ERR_INVALID_MODULE_SPECIFIER,\n  ERR_INVALID_PACKAGE_CONFIG,\n  ERR_INVALID_PACKAGE_TARGET,\n  ERR_MODULE_NOT_FOUND,\n  ERR_PACKAGE_IMPORT_NOT_DEFINED,\n  ERR_PACKAGE_PATH_NOT_EXPORTED,\n  ERR_UNSUPPORTED_DIR_IMPORT,\n  ERR_UNSUPPORTED_RESOLVE_REQUEST\n} = codes;\n\nconst own = {}.hasOwnProperty;\n\nconst invalidSegmentRegEx =\n  /(^|\\\\|\\/)((\\.|%2e)(\\.|%2e)?|(n|%6e|%4e)(o|%6f|%4f)(d|%64|%44)(e|%65|%45)(_|%5f)(m|%6d|%4d)(o|%6f|%4f)(d|%64|%44)(u|%75|%55)(l|%6c|%4c)(e|%65|%45)(s|%73|%53))?(\\\\|\\/|$)/i;\nconst deprecatedInvalidSegmentRegEx =\n  /(^|\\\\|\\/)((\\.|%2e)(\\.|%2e)?|(n|%6e|%4e)(o|%6f|%4f)(d|%64|%44)(e|%65|%45)(_|%5f)(m|%6d|%4d)(o|%6f|%4f)(d|%64|%44)(u|%75|%55)(l|%6c|%4c)(e|%65|%45)(s|%73|%53))(\\\\|\\/|$)/i;\nconst invalidPackageNameRegEx = /^\\.|%|\\\\/;\nconst patternRegEx = /\\*/g;\nconst encodedSeparatorRegEx = /%2f|%5c/i;\n/** @type {Set<string>} */\nconst emittedPackageWarnings = new Set();\n\nconst doubleSlashRegEx = /[/\\\\]{2}/;\n\n/**\n *\n * @param {string} target\n * @param {string} request\n * @param {string} match\n * @param {URL} packageJsonUrl\n * @param {boolean} internal\n * @param {URL} base\n * @param {boolean} isTarget\n */\nfunction emitInvalidSegmentDeprecation(\n  target,\n  request,\n  match,\n  packageJsonUrl,\n  internal,\n  base,\n  isTarget\n) {\n  // @ts-expect-error: apparently it does exist, TS.\n  if (process.noDeprecation) {\n    return\n  }\n\n  const pjsonPath = fileURLToPath(packageJsonUrl);\n  const double = doubleSlashRegEx.exec(isTarget ? target : request) !== null;\n  process.emitWarning(\n    `Use of deprecated ${\n      double ? 'double slash' : 'leading or trailing slash matching'\n    } resolving \"${target}\" for module ` +\n      `request \"${request}\" ${\n        request === match ? '' : `matched to \"${match}\" `\n      }in the \"${\n        internal ? 'imports' : 'exports'\n      }\" field module resolution of the package at ${pjsonPath}${\n        base ? ` imported from ${fileURLToPath(base)}` : ''\n      }.`,\n    'DeprecationWarning',\n    'DEP0166'\n  );\n}\n\n/**\n * @param {URL} url\n * @param {URL} packageJsonUrl\n * @param {URL} base\n * @param {string} [main]\n * @returns {void}\n */\nfunction emitLegacyIndexDeprecation(url, packageJsonUrl, base, main) {\n  // @ts-expect-error: apparently it does exist, TS.\n  if (process.noDeprecation) {\n    return\n  }\n\n  const format = defaultGetFormatWithoutErrors(url, {parentURL: base.href});\n  if (format !== 'module') return\n  const urlPath = fileURLToPath(url.href);\n  const packagePath = fileURLToPath(new URL$1('.', packageJsonUrl));\n  const basePath = fileURLToPath(base);\n  if (!main) {\n    process.emitWarning(\n      `No \"main\" or \"exports\" field defined in the package.json for ${packagePath} resolving the main entry point \"${urlPath.slice(\n        packagePath.length\n      )}\", imported from ${basePath}.\\nDefault \"index\" lookups for the main are deprecated for ES modules.`,\n      'DeprecationWarning',\n      'DEP0151'\n    );\n  } else if (path.resolve(packagePath, main) !== urlPath) {\n    process.emitWarning(\n      `Package ${packagePath} has a \"main\" field set to \"${main}\", ` +\n        `excluding the full filename and extension to the resolved file at \"${urlPath.slice(\n          packagePath.length\n        )}\", imported from ${basePath}.\\n Automatic extension resolution of the \"main\" field is ` +\n        'deprecated for ES modules.',\n      'DeprecationWarning',\n      'DEP0151'\n    );\n  }\n}\n\n/**\n * @param {string} path\n * @returns {Stats | undefined}\n */\nfunction tryStatSync(path) {\n  // Note: from Node 15 onwards we can use `throwIfNoEntry: false` instead.\n  try {\n    return statSync(path)\n  } catch {\n    // Note: in Node code this returns `new Stats`,\n    // but in Node 22 that’s marked as a deprecated internal API.\n    // Which, well, we kinda are, but still to prevent that warning,\n    // just yield `undefined`.\n  }\n}\n\n/**\n * Legacy CommonJS main resolution:\n * 1. let M = pkg_url + (json main field)\n * 2. TRY(M, M.js, M.json, M.node)\n * 3. TRY(M/index.js, M/index.json, M/index.node)\n * 4. TRY(pkg_url/index.js, pkg_url/index.json, pkg_url/index.node)\n * 5. NOT_FOUND\n *\n * @param {URL} url\n * @returns {boolean}\n */\nfunction fileExists(url) {\n  const stats = statSync(url, {throwIfNoEntry: false});\n  const isFile = stats ? stats.isFile() : undefined;\n  return isFile === null || isFile === undefined ? false : isFile\n}\n\n/**\n * @param {URL} packageJsonUrl\n * @param {PackageConfig} packageConfig\n * @param {URL} base\n * @returns {URL}\n */\nfunction legacyMainResolve(packageJsonUrl, packageConfig, base) {\n  /** @type {URL | undefined} */\n  let guess;\n  if (packageConfig.main !== undefined) {\n    guess = new URL$1(packageConfig.main, packageJsonUrl);\n    // Note: fs check redundances will be handled by Descriptor cache here.\n    if (fileExists(guess)) return guess\n\n    const tries = [\n      `./${packageConfig.main}.js`,\n      `./${packageConfig.main}.json`,\n      `./${packageConfig.main}.node`,\n      `./${packageConfig.main}/index.js`,\n      `./${packageConfig.main}/index.json`,\n      `./${packageConfig.main}/index.node`\n    ];\n    let i = -1;\n\n    while (++i < tries.length) {\n      guess = new URL$1(tries[i], packageJsonUrl);\n      if (fileExists(guess)) break\n      guess = undefined;\n    }\n\n    if (guess) {\n      emitLegacyIndexDeprecation(\n        guess,\n        packageJsonUrl,\n        base,\n        packageConfig.main\n      );\n      return guess\n    }\n    // Fallthrough.\n  }\n\n  const tries = ['./index.js', './index.json', './index.node'];\n  let i = -1;\n\n  while (++i < tries.length) {\n    guess = new URL$1(tries[i], packageJsonUrl);\n    if (fileExists(guess)) break\n    guess = undefined;\n  }\n\n  if (guess) {\n    emitLegacyIndexDeprecation(guess, packageJsonUrl, base, packageConfig.main);\n    return guess\n  }\n\n  // Not found.\n  throw new ERR_MODULE_NOT_FOUND(\n    fileURLToPath(new URL$1('.', packageJsonUrl)),\n    fileURLToPath(base)\n  )\n}\n\n/**\n * @param {URL} resolved\n * @param {URL} base\n * @param {boolean} [preserveSymlinks]\n * @returns {URL}\n */\nfunction finalizeResolution(resolved, base, preserveSymlinks) {\n  if (encodedSeparatorRegEx.exec(resolved.pathname) !== null) {\n    throw new ERR_INVALID_MODULE_SPECIFIER(\n      resolved.pathname,\n      'must not include encoded \"/\" or \"\\\\\" characters',\n      fileURLToPath(base)\n    )\n  }\n\n  /** @type {string} */\n  let filePath;\n\n  try {\n    filePath = fileURLToPath(resolved);\n  } catch (error) {\n    const cause = /** @type {ErrnoException} */ (error);\n    Object.defineProperty(cause, 'input', {value: String(resolved)});\n    Object.defineProperty(cause, 'module', {value: String(base)});\n    throw cause\n  }\n\n  const stats = tryStatSync(\n    filePath.endsWith('/') ? filePath.slice(-1) : filePath\n  );\n\n  if (stats && stats.isDirectory()) {\n    const error = new ERR_UNSUPPORTED_DIR_IMPORT(filePath, fileURLToPath(base));\n    // @ts-expect-error Add this for `import.meta.resolve`.\n    error.url = String(resolved);\n    throw error\n  }\n\n  if (!stats || !stats.isFile()) {\n    const error = new ERR_MODULE_NOT_FOUND(\n      filePath || resolved.pathname,\n      base && fileURLToPath(base),\n      true\n    );\n    // @ts-expect-error Add this for `import.meta.resolve`.\n    error.url = String(resolved);\n    throw error\n  }\n\n  if (!preserveSymlinks) {\n    const real = realpathSync(filePath);\n    const {search, hash} = resolved;\n    resolved = pathToFileURL(real + (filePath.endsWith(path.sep) ? '/' : ''));\n    resolved.search = search;\n    resolved.hash = hash;\n  }\n\n  return resolved\n}\n\n/**\n * @param {string} specifier\n * @param {URL | undefined} packageJsonUrl\n * @param {URL} base\n * @returns {Error}\n */\nfunction importNotDefined(specifier, packageJsonUrl, base) {\n  return new ERR_PACKAGE_IMPORT_NOT_DEFINED(\n    specifier,\n    packageJsonUrl && fileURLToPath(new URL$1('.', packageJsonUrl)),\n    fileURLToPath(base)\n  )\n}\n\n/**\n * @param {string} subpath\n * @param {URL} packageJsonUrl\n * @param {URL} base\n * @returns {Error}\n */\nfunction exportsNotFound(subpath, packageJsonUrl, base) {\n  return new ERR_PACKAGE_PATH_NOT_EXPORTED(\n    fileURLToPath(new URL$1('.', packageJsonUrl)),\n    subpath,\n    base && fileURLToPath(base)\n  )\n}\n\n/**\n * @param {string} request\n * @param {string} match\n * @param {URL} packageJsonUrl\n * @param {boolean} internal\n * @param {URL} [base]\n * @returns {never}\n */\nfunction throwInvalidSubpath(request, match, packageJsonUrl, internal, base) {\n  const reason = `request is not a valid match in pattern \"${match}\" for the \"${\n    internal ? 'imports' : 'exports'\n  }\" resolution of ${fileURLToPath(packageJsonUrl)}`;\n  throw new ERR_INVALID_MODULE_SPECIFIER(\n    request,\n    reason,\n    base && fileURLToPath(base)\n  )\n}\n\n/**\n * @param {string} subpath\n * @param {unknown} target\n * @param {URL} packageJsonUrl\n * @param {boolean} internal\n * @param {URL} [base]\n * @returns {Error}\n */\nfunction invalidPackageTarget(subpath, target, packageJsonUrl, internal, base) {\n  target =\n    typeof target === 'object' && target !== null\n      ? JSON.stringify(target, null, '')\n      : `${target}`;\n\n  return new ERR_INVALID_PACKAGE_TARGET(\n    fileURLToPath(new URL$1('.', packageJsonUrl)),\n    subpath,\n    target,\n    internal,\n    base && fileURLToPath(base)\n  )\n}\n\n/**\n * @param {string} target\n * @param {string} subpath\n * @param {string} match\n * @param {URL} packageJsonUrl\n * @param {URL} base\n * @param {boolean} pattern\n * @param {boolean} internal\n * @param {boolean} isPathMap\n * @param {Set<string> | undefined} conditions\n * @returns {URL}\n */\nfunction resolvePackageTargetString(\n  target,\n  subpath,\n  match,\n  packageJsonUrl,\n  base,\n  pattern,\n  internal,\n  isPathMap,\n  conditions\n) {\n  if (subpath !== '' && !pattern && target[target.length - 1] !== '/')\n    throw invalidPackageTarget(match, target, packageJsonUrl, internal, base)\n\n  if (!target.startsWith('./')) {\n    if (internal && !target.startsWith('../') && !target.startsWith('/')) {\n      let isURL = false;\n\n      try {\n        new URL$1(target);\n        isURL = true;\n      } catch {\n        // Continue regardless of error.\n      }\n\n      if (!isURL) {\n        const exportTarget = pattern\n          ? RegExpPrototypeSymbolReplace.call(\n              patternRegEx,\n              target,\n              () => subpath\n            )\n          : target + subpath;\n\n        return packageResolve(exportTarget, packageJsonUrl, conditions)\n      }\n    }\n\n    throw invalidPackageTarget(match, target, packageJsonUrl, internal, base)\n  }\n\n  if (invalidSegmentRegEx.exec(target.slice(2)) !== null) {\n    if (deprecatedInvalidSegmentRegEx.exec(target.slice(2)) === null) {\n      if (!isPathMap) {\n        const request = pattern\n          ? match.replace('*', () => subpath)\n          : match + subpath;\n        const resolvedTarget = pattern\n          ? RegExpPrototypeSymbolReplace.call(\n              patternRegEx,\n              target,\n              () => subpath\n            )\n          : target;\n        emitInvalidSegmentDeprecation(\n          resolvedTarget,\n          request,\n          match,\n          packageJsonUrl,\n          internal,\n          base,\n          true\n        );\n      }\n    } else {\n      throw invalidPackageTarget(match, target, packageJsonUrl, internal, base)\n    }\n  }\n\n  const resolved = new URL$1(target, packageJsonUrl);\n  const resolvedPath = resolved.pathname;\n  const packagePath = new URL$1('.', packageJsonUrl).pathname;\n\n  if (!resolvedPath.startsWith(packagePath))\n    throw invalidPackageTarget(match, target, packageJsonUrl, internal, base)\n\n  if (subpath === '') return resolved\n\n  if (invalidSegmentRegEx.exec(subpath) !== null) {\n    const request = pattern\n      ? match.replace('*', () => subpath)\n      : match + subpath;\n    if (deprecatedInvalidSegmentRegEx.exec(subpath) === null) {\n      if (!isPathMap) {\n        const resolvedTarget = pattern\n          ? RegExpPrototypeSymbolReplace.call(\n              patternRegEx,\n              target,\n              () => subpath\n            )\n          : target;\n        emitInvalidSegmentDeprecation(\n          resolvedTarget,\n          request,\n          match,\n          packageJsonUrl,\n          internal,\n          base,\n          false\n        );\n      }\n    } else {\n      throwInvalidSubpath(request, match, packageJsonUrl, internal, base);\n    }\n  }\n\n  if (pattern) {\n    return new URL$1(\n      RegExpPrototypeSymbolReplace.call(\n        patternRegEx,\n        resolved.href,\n        () => subpath\n      )\n    )\n  }\n\n  return new URL$1(subpath, resolved)\n}\n\n/**\n * @param {string} key\n * @returns {boolean}\n */\nfunction isArrayIndex(key) {\n  const keyNumber = Number(key);\n  if (`${keyNumber}` !== key) return false\n  return keyNumber >= 0 && keyNumber < 0xff_ff_ff_ff\n}\n\n/**\n * @param {URL} packageJsonUrl\n * @param {unknown} target\n * @param {string} subpath\n * @param {string} packageSubpath\n * @param {URL} base\n * @param {boolean} pattern\n * @param {boolean} internal\n * @param {boolean} isPathMap\n * @param {Set<string> | undefined} conditions\n * @returns {URL | null}\n */\nfunction resolvePackageTarget(\n  packageJsonUrl,\n  target,\n  subpath,\n  packageSubpath,\n  base,\n  pattern,\n  internal,\n  isPathMap,\n  conditions\n) {\n  if (typeof target === 'string') {\n    return resolvePackageTargetString(\n      target,\n      subpath,\n      packageSubpath,\n      packageJsonUrl,\n      base,\n      pattern,\n      internal,\n      isPathMap,\n      conditions\n    )\n  }\n\n  if (Array.isArray(target)) {\n    /** @type {Array<unknown>} */\n    const targetList = target;\n    if (targetList.length === 0) return null\n\n    /** @type {ErrnoException | null | undefined} */\n    let lastException;\n    let i = -1;\n\n    while (++i < targetList.length) {\n      const targetItem = targetList[i];\n      /** @type {URL | null} */\n      let resolveResult;\n      try {\n        resolveResult = resolvePackageTarget(\n          packageJsonUrl,\n          targetItem,\n          subpath,\n          packageSubpath,\n          base,\n          pattern,\n          internal,\n          isPathMap,\n          conditions\n        );\n      } catch (error) {\n        const exception = /** @type {ErrnoException} */ (error);\n        lastException = exception;\n        if (exception.code === 'ERR_INVALID_PACKAGE_TARGET') continue\n        throw error\n      }\n\n      if (resolveResult === undefined) continue\n\n      if (resolveResult === null) {\n        lastException = null;\n        continue\n      }\n\n      return resolveResult\n    }\n\n    if (lastException === undefined || lastException === null) {\n      return null\n    }\n\n    throw lastException\n  }\n\n  if (typeof target === 'object' && target !== null) {\n    const keys = Object.getOwnPropertyNames(target);\n    let i = -1;\n\n    while (++i < keys.length) {\n      const key = keys[i];\n      if (isArrayIndex(key)) {\n        throw new ERR_INVALID_PACKAGE_CONFIG(\n          fileURLToPath(packageJsonUrl),\n          base,\n          '\"exports\" cannot contain numeric property keys.'\n        )\n      }\n    }\n\n    i = -1;\n\n    while (++i < keys.length) {\n      const key = keys[i];\n      if (key === 'default' || (conditions && conditions.has(key))) {\n        // @ts-expect-error: indexable.\n        const conditionalTarget = /** @type {unknown} */ (target[key]);\n        const resolveResult = resolvePackageTarget(\n          packageJsonUrl,\n          conditionalTarget,\n          subpath,\n          packageSubpath,\n          base,\n          pattern,\n          internal,\n          isPathMap,\n          conditions\n        );\n        if (resolveResult === undefined) continue\n        return resolveResult\n      }\n    }\n\n    return null\n  }\n\n  if (target === null) {\n    return null\n  }\n\n  throw invalidPackageTarget(\n    packageSubpath,\n    target,\n    packageJsonUrl,\n    internal,\n    base\n  )\n}\n\n/**\n * @param {unknown} exports\n * @param {URL} packageJsonUrl\n * @param {URL} base\n * @returns {boolean}\n */\nfunction isConditionalExportsMainSugar(exports, packageJsonUrl, base) {\n  if (typeof exports === 'string' || Array.isArray(exports)) return true\n  if (typeof exports !== 'object' || exports === null) return false\n\n  const keys = Object.getOwnPropertyNames(exports);\n  let isConditionalSugar = false;\n  let i = 0;\n  let keyIndex = -1;\n  while (++keyIndex < keys.length) {\n    const key = keys[keyIndex];\n    const currentIsConditionalSugar = key === '' || key[0] !== '.';\n    if (i++ === 0) {\n      isConditionalSugar = currentIsConditionalSugar;\n    } else if (isConditionalSugar !== currentIsConditionalSugar) {\n      throw new ERR_INVALID_PACKAGE_CONFIG(\n        fileURLToPath(packageJsonUrl),\n        base,\n        '\"exports\" cannot contain some keys starting with \\'.\\' and some not.' +\n          ' The exports object must either be an object of package subpath keys' +\n          ' or an object of main entry condition name keys only.'\n      )\n    }\n  }\n\n  return isConditionalSugar\n}\n\n/**\n * @param {string} match\n * @param {URL} pjsonUrl\n * @param {URL} base\n */\nfunction emitTrailingSlashPatternDeprecation(match, pjsonUrl, base) {\n  // @ts-expect-error: apparently it does exist, TS.\n  if (process.noDeprecation) {\n    return\n  }\n\n  const pjsonPath = fileURLToPath(pjsonUrl);\n  if (emittedPackageWarnings.has(pjsonPath + '|' + match)) return\n  emittedPackageWarnings.add(pjsonPath + '|' + match);\n  process.emitWarning(\n    `Use of deprecated trailing slash pattern mapping \"${match}\" in the ` +\n      `\"exports\" field module resolution of the package at ${pjsonPath}${\n        base ? ` imported from ${fileURLToPath(base)}` : ''\n      }. Mapping specifiers ending in \"/\" is no longer supported.`,\n    'DeprecationWarning',\n    'DEP0155'\n  );\n}\n\n/**\n * @param {URL} packageJsonUrl\n * @param {string} packageSubpath\n * @param {Record<string, unknown>} packageConfig\n * @param {URL} base\n * @param {Set<string> | undefined} conditions\n * @returns {URL}\n */\nfunction packageExportsResolve(\n  packageJsonUrl,\n  packageSubpath,\n  packageConfig,\n  base,\n  conditions\n) {\n  let exports = packageConfig.exports;\n\n  if (isConditionalExportsMainSugar(exports, packageJsonUrl, base)) {\n    exports = {'.': exports};\n  }\n\n  if (\n    own.call(exports, packageSubpath) &&\n    !packageSubpath.includes('*') &&\n    !packageSubpath.endsWith('/')\n  ) {\n    // @ts-expect-error: indexable.\n    const target = exports[packageSubpath];\n    const resolveResult = resolvePackageTarget(\n      packageJsonUrl,\n      target,\n      '',\n      packageSubpath,\n      base,\n      false,\n      false,\n      false,\n      conditions\n    );\n    if (resolveResult === null || resolveResult === undefined) {\n      throw exportsNotFound(packageSubpath, packageJsonUrl, base)\n    }\n\n    return resolveResult\n  }\n\n  let bestMatch = '';\n  let bestMatchSubpath = '';\n  const keys = Object.getOwnPropertyNames(exports);\n  let i = -1;\n\n  while (++i < keys.length) {\n    const key = keys[i];\n    const patternIndex = key.indexOf('*');\n\n    if (\n      patternIndex !== -1 &&\n      packageSubpath.startsWith(key.slice(0, patternIndex))\n    ) {\n      // When this reaches EOL, this can throw at the top of the whole function:\n      //\n      // if (StringPrototypeEndsWith(packageSubpath, '/'))\n      //   throwInvalidSubpath(packageSubpath)\n      //\n      // To match \"imports\" and the spec.\n      if (packageSubpath.endsWith('/')) {\n        emitTrailingSlashPatternDeprecation(\n          packageSubpath,\n          packageJsonUrl,\n          base\n        );\n      }\n\n      const patternTrailer = key.slice(patternIndex + 1);\n\n      if (\n        packageSubpath.length >= key.length &&\n        packageSubpath.endsWith(patternTrailer) &&\n        patternKeyCompare(bestMatch, key) === 1 &&\n        key.lastIndexOf('*') === patternIndex\n      ) {\n        bestMatch = key;\n        bestMatchSubpath = packageSubpath.slice(\n          patternIndex,\n          packageSubpath.length - patternTrailer.length\n        );\n      }\n    }\n  }\n\n  if (bestMatch) {\n    // @ts-expect-error: indexable.\n    const target = /** @type {unknown} */ (exports[bestMatch]);\n    const resolveResult = resolvePackageTarget(\n      packageJsonUrl,\n      target,\n      bestMatchSubpath,\n      bestMatch,\n      base,\n      true,\n      false,\n      packageSubpath.endsWith('/'),\n      conditions\n    );\n\n    if (resolveResult === null || resolveResult === undefined) {\n      throw exportsNotFound(packageSubpath, packageJsonUrl, base)\n    }\n\n    return resolveResult\n  }\n\n  throw exportsNotFound(packageSubpath, packageJsonUrl, base)\n}\n\n/**\n * @param {string} a\n * @param {string} b\n */\nfunction patternKeyCompare(a, b) {\n  const aPatternIndex = a.indexOf('*');\n  const bPatternIndex = b.indexOf('*');\n  const baseLengthA = aPatternIndex === -1 ? a.length : aPatternIndex + 1;\n  const baseLengthB = bPatternIndex === -1 ? b.length : bPatternIndex + 1;\n  if (baseLengthA > baseLengthB) return -1\n  if (baseLengthB > baseLengthA) return 1\n  if (aPatternIndex === -1) return 1\n  if (bPatternIndex === -1) return -1\n  if (a.length > b.length) return -1\n  if (b.length > a.length) return 1\n  return 0\n}\n\n/**\n * @param {string} name\n * @param {URL} base\n * @param {Set<string>} [conditions]\n * @returns {URL}\n */\nfunction packageImportsResolve(name, base, conditions) {\n  if (name === '#' || name.startsWith('#/') || name.endsWith('/')) {\n    const reason = 'is not a valid internal imports specifier name';\n    throw new ERR_INVALID_MODULE_SPECIFIER(name, reason, fileURLToPath(base))\n  }\n\n  /** @type {URL | undefined} */\n  let packageJsonUrl;\n\n  const packageConfig = getPackageScopeConfig(base);\n\n  if (packageConfig.exists) {\n    packageJsonUrl = pathToFileURL(packageConfig.pjsonPath);\n    const imports = packageConfig.imports;\n    if (imports) {\n      if (own.call(imports, name) && !name.includes('*')) {\n        const resolveResult = resolvePackageTarget(\n          packageJsonUrl,\n          imports[name],\n          '',\n          name,\n          base,\n          false,\n          true,\n          false,\n          conditions\n        );\n        if (resolveResult !== null && resolveResult !== undefined) {\n          return resolveResult\n        }\n      } else {\n        let bestMatch = '';\n        let bestMatchSubpath = '';\n        const keys = Object.getOwnPropertyNames(imports);\n        let i = -1;\n\n        while (++i < keys.length) {\n          const key = keys[i];\n          const patternIndex = key.indexOf('*');\n\n          if (patternIndex !== -1 && name.startsWith(key.slice(0, -1))) {\n            const patternTrailer = key.slice(patternIndex + 1);\n            if (\n              name.length >= key.length &&\n              name.endsWith(patternTrailer) &&\n              patternKeyCompare(bestMatch, key) === 1 &&\n              key.lastIndexOf('*') === patternIndex\n            ) {\n              bestMatch = key;\n              bestMatchSubpath = name.slice(\n                patternIndex,\n                name.length - patternTrailer.length\n              );\n            }\n          }\n        }\n\n        if (bestMatch) {\n          const target = imports[bestMatch];\n          const resolveResult = resolvePackageTarget(\n            packageJsonUrl,\n            target,\n            bestMatchSubpath,\n            bestMatch,\n            base,\n            true,\n            true,\n            false,\n            conditions\n          );\n\n          if (resolveResult !== null && resolveResult !== undefined) {\n            return resolveResult\n          }\n        }\n      }\n    }\n  }\n\n  throw importNotDefined(name, packageJsonUrl, base)\n}\n\n/**\n * @param {string} specifier\n * @param {URL} base\n */\nfunction parsePackageName(specifier, base) {\n  let separatorIndex = specifier.indexOf('/');\n  let validPackageName = true;\n  let isScoped = false;\n  if (specifier[0] === '@') {\n    isScoped = true;\n    if (separatorIndex === -1 || specifier.length === 0) {\n      validPackageName = false;\n    } else {\n      separatorIndex = specifier.indexOf('/', separatorIndex + 1);\n    }\n  }\n\n  const packageName =\n    separatorIndex === -1 ? specifier : specifier.slice(0, separatorIndex);\n\n  // Package name cannot have leading . and cannot have percent-encoding or\n  // \\\\ separators.\n  if (invalidPackageNameRegEx.exec(packageName) !== null) {\n    validPackageName = false;\n  }\n\n  if (!validPackageName) {\n    throw new ERR_INVALID_MODULE_SPECIFIER(\n      specifier,\n      'is not a valid package name',\n      fileURLToPath(base)\n    )\n  }\n\n  const packageSubpath =\n    '.' + (separatorIndex === -1 ? '' : specifier.slice(separatorIndex));\n\n  return {packageName, packageSubpath, isScoped}\n}\n\n/**\n * @param {string} specifier\n * @param {URL} base\n * @param {Set<string> | undefined} conditions\n * @returns {URL}\n */\nfunction packageResolve(specifier, base, conditions) {\n  if (builtinModules.includes(specifier)) {\n    return new URL$1('node:' + specifier)\n  }\n\n  const {packageName, packageSubpath, isScoped} = parsePackageName(\n    specifier,\n    base\n  );\n\n  // ResolveSelf\n  const packageConfig = getPackageScopeConfig(base);\n\n  // Can’t test.\n  /* c8 ignore next 16 */\n  if (packageConfig.exists) {\n    const packageJsonUrl = pathToFileURL(packageConfig.pjsonPath);\n    if (\n      packageConfig.name === packageName &&\n      packageConfig.exports !== undefined &&\n      packageConfig.exports !== null\n    ) {\n      return packageExportsResolve(\n        packageJsonUrl,\n        packageSubpath,\n        packageConfig,\n        base,\n        conditions\n      )\n    }\n  }\n\n  let packageJsonUrl = new URL$1(\n    './node_modules/' + packageName + '/package.json',\n    base\n  );\n  let packageJsonPath = fileURLToPath(packageJsonUrl);\n  /** @type {string} */\n  let lastPath;\n  do {\n    const stat = tryStatSync(packageJsonPath.slice(0, -13));\n    if (!stat || !stat.isDirectory()) {\n      lastPath = packageJsonPath;\n      packageJsonUrl = new URL$1(\n        (isScoped ? '../../../../node_modules/' : '../../../node_modules/') +\n          packageName +\n          '/package.json',\n        packageJsonUrl\n      );\n      packageJsonPath = fileURLToPath(packageJsonUrl);\n      continue\n    }\n\n    // Package match.\n    const packageConfig = read(packageJsonPath, {base, specifier});\n    if (packageConfig.exports !== undefined && packageConfig.exports !== null) {\n      return packageExportsResolve(\n        packageJsonUrl,\n        packageSubpath,\n        packageConfig,\n        base,\n        conditions\n      )\n    }\n\n    if (packageSubpath === '.') {\n      return legacyMainResolve(packageJsonUrl, packageConfig, base)\n    }\n\n    return new URL$1(packageSubpath, packageJsonUrl)\n    // Cross-platform root check.\n  } while (packageJsonPath.length !== lastPath.length)\n\n  throw new ERR_MODULE_NOT_FOUND(packageName, fileURLToPath(base), false)\n}\n\n/**\n * @param {string} specifier\n * @returns {boolean}\n */\nfunction isRelativeSpecifier(specifier) {\n  if (specifier[0] === '.') {\n    if (specifier.length === 1 || specifier[1] === '/') return true\n    if (\n      specifier[1] === '.' &&\n      (specifier.length === 2 || specifier[2] === '/')\n    ) {\n      return true\n    }\n  }\n\n  return false\n}\n\n/**\n * @param {string} specifier\n * @returns {boolean}\n */\nfunction shouldBeTreatedAsRelativeOrAbsolutePath(specifier) {\n  if (specifier === '') return false\n  if (specifier[0] === '/') return true\n  return isRelativeSpecifier(specifier)\n}\n\n/**\n * The “Resolver Algorithm Specification” as detailed in the Node docs (which is\n * sync and slightly lower-level than `resolve`).\n *\n * @param {string} specifier\n *   `/example.js`, `./example.js`, `../example.js`, `some-package`, `fs`, etc.\n * @param {URL} base\n *   Full URL (to a file) that `specifier` is resolved relative from.\n * @param {Set<string>} [conditions]\n *   Conditions.\n * @param {boolean} [preserveSymlinks]\n *   Keep symlinks instead of resolving them.\n * @returns {URL}\n *   A URL object to the found thing.\n */\nfunction moduleResolve(specifier, base, conditions, preserveSymlinks) {\n  // Note: The Node code supports `base` as a string (in this internal API) too,\n  // we don’t.\n  const protocol = base.protocol;\n  const isData = protocol === 'data:';\n  const isRemote = isData || protocol === 'http:' || protocol === 'https:';\n  // Order swapped from spec for minor perf gain.\n  // Ok since relative URLs cannot parse as URLs.\n  /** @type {URL | undefined} */\n  let resolved;\n\n  if (shouldBeTreatedAsRelativeOrAbsolutePath(specifier)) {\n    try {\n      resolved = new URL$1(specifier, base);\n    } catch (error_) {\n      const error = new ERR_UNSUPPORTED_RESOLVE_REQUEST(specifier, base);\n      error.cause = error_;\n      throw error\n    }\n  } else if (protocol === 'file:' && specifier[0] === '#') {\n    resolved = packageImportsResolve(specifier, base, conditions);\n  } else {\n    try {\n      resolved = new URL$1(specifier);\n    } catch (error_) {\n      // Note: actual code uses `canBeRequiredWithoutScheme`.\n      if (isRemote && !builtinModules.includes(specifier)) {\n        const error = new ERR_UNSUPPORTED_RESOLVE_REQUEST(specifier, base);\n        error.cause = error_;\n        throw error\n      }\n\n      resolved = packageResolve(specifier, base, conditions);\n    }\n  }\n\n  assert(resolved !== undefined, 'expected to be defined');\n\n  if (resolved.protocol !== 'file:') {\n    return resolved\n  }\n\n  return finalizeResolution(resolved, base, preserveSymlinks)\n}\n\n/**\n * @param {string} specifier\n * @param {URL | undefined} parsed\n * @param {URL | undefined} parsedParentURL\n */\nfunction checkIfDisallowedImport(specifier, parsed, parsedParentURL) {\n  if (parsedParentURL) {\n    // Avoid accessing the `protocol` property due to the lazy getters.\n    const parentProtocol = parsedParentURL.protocol;\n\n    if (parentProtocol === 'http:' || parentProtocol === 'https:') {\n      if (shouldBeTreatedAsRelativeOrAbsolutePath(specifier)) {\n        // Avoid accessing the `protocol` property due to the lazy getters.\n        const parsedProtocol = parsed?.protocol;\n\n        // `data:` and `blob:` disallowed due to allowing file: access via\n        // indirection\n        if (\n          parsedProtocol &&\n          parsedProtocol !== 'https:' &&\n          parsedProtocol !== 'http:'\n        ) {\n          throw new ERR_NETWORK_IMPORT_DISALLOWED(\n            specifier,\n            parsedParentURL,\n            'remote imports cannot import from a local location.'\n          )\n        }\n\n        return {url: parsed?.href || ''}\n      }\n\n      if (builtinModules.includes(specifier)) {\n        throw new ERR_NETWORK_IMPORT_DISALLOWED(\n          specifier,\n          parsedParentURL,\n          'remote imports cannot import from a local location.'\n        )\n      }\n\n      throw new ERR_NETWORK_IMPORT_DISALLOWED(\n        specifier,\n        parsedParentURL,\n        'only relative and absolute specifiers are supported.'\n      )\n    }\n  }\n}\n\n// Note: this is from:\n// <https://github.com/nodejs/node/blob/3e74590/lib/internal/url.js#L687>\n/**\n * Checks if a value has the shape of a WHATWG URL object.\n *\n * Using a symbol or instanceof would not be able to recognize URL objects\n * coming from other implementations (e.g. in Electron), so instead we are\n * checking some well known properties for a lack of a better test.\n *\n * We use `href` and `protocol` as they are the only properties that are\n * easy to retrieve and calculate due to the lazy nature of the getters.\n *\n * @template {unknown} Value\n * @param {Value} self\n * @returns {Value is URL}\n */\nfunction isURL(self) {\n  return Boolean(\n    self &&\n      typeof self === 'object' &&\n      'href' in self &&\n      typeof self.href === 'string' &&\n      'protocol' in self &&\n      typeof self.protocol === 'string' &&\n      self.href &&\n      self.protocol\n  )\n}\n\n/**\n * Validate user-input in `context` supplied by a custom loader.\n *\n * @param {unknown} parentURL\n * @returns {asserts parentURL is URL | string | undefined}\n */\nfunction throwIfInvalidParentURL(parentURL) {\n  if (parentURL === undefined) {\n    return // Main entry point, so no parent\n  }\n\n  if (typeof parentURL !== 'string' && !isURL(parentURL)) {\n    throw new codes.ERR_INVALID_ARG_TYPE(\n      'parentURL',\n      ['string', 'URL'],\n      parentURL\n    )\n  }\n}\n\n/**\n * @param {string} specifier\n * @param {{parentURL?: string, conditions?: Array<string>}} context\n * @returns {{url: string, format?: string | null}}\n */\nfunction defaultResolve(specifier, context = {}) {\n  const {parentURL} = context;\n  assert(parentURL !== undefined, 'expected `parentURL` to be defined');\n  throwIfInvalidParentURL(parentURL);\n\n  /** @type {URL | undefined} */\n  let parsedParentURL;\n  if (parentURL) {\n    try {\n      parsedParentURL = new URL$1(parentURL);\n    } catch {\n      // Ignore exception\n    }\n  }\n\n  /** @type {URL | undefined} */\n  let parsed;\n  /** @type {string | undefined} */\n  let protocol;\n\n  try {\n    parsed = shouldBeTreatedAsRelativeOrAbsolutePath(specifier)\n      ? new URL$1(specifier, parsedParentURL)\n      : new URL$1(specifier);\n\n    // Avoid accessing the `protocol` property due to the lazy getters.\n    protocol = parsed.protocol;\n\n    if (protocol === 'data:') {\n      return {url: parsed.href, format: null}\n    }\n  } catch {\n    // Ignore exception\n  }\n\n  // There are multiple deep branches that can either throw or return; instead\n  // of duplicating that deeply nested logic for the possible returns, DRY and\n  // check for a return. This seems the least gnarly.\n  const maybeReturn = checkIfDisallowedImport(\n    specifier,\n    parsed,\n    parsedParentURL\n  );\n\n  if (maybeReturn) return maybeReturn\n\n  // This must come after checkIfDisallowedImport\n  if (protocol === undefined && parsed) {\n    protocol = parsed.protocol;\n  }\n\n  if (protocol === 'node:') {\n    return {url: specifier}\n  }\n\n  // This must come after checkIfDisallowedImport\n  if (parsed && parsed.protocol === 'node:') return {url: specifier}\n\n  const conditions = getConditionsSet(context.conditions);\n\n  const url = moduleResolve(specifier, new URL$1(parentURL), conditions, false);\n\n  return {\n    // Do NOT cast `url` to a string: that will work even when there are real\n    // problems, silencing them\n    url: url.href,\n    format: defaultGetFormatWithoutErrors(url, {parentURL})\n  }\n}\n\n/**\n * @typedef {import('./lib/errors.js').ErrnoException} ErrnoException\n */\n\n\n/**\n * Match `import.meta.resolve` except that `parent` is required (you can pass\n * `import.meta.url`).\n *\n * @param {string} specifier\n *   The module specifier to resolve relative to parent\n *   (`/example.js`, `./example.js`, `../example.js`, `some-package`, `fs`,\n *   etc).\n * @param {string} parent\n *   The absolute parent module URL to resolve from.\n *   You must pass `import.meta.url` or something else.\n * @returns {string}\n *   Returns a string to a full `file:`, `data:`, or `node:` URL\n *   to the found thing.\n */\nfunction resolve(specifier, parent) {\n  if (!parent) {\n    throw new Error(\n      'Please pass `parent`: `import-meta-resolve` cannot ponyfill that'\n    )\n  }\n\n  try {\n    return defaultResolve(specifier, {parentURL: parent}).url\n  } catch (error) {\n    // See: <https://github.com/nodejs/node/blob/45f5c9b/lib/internal/modules/esm/initialize_import_meta.js#L34>\n    const exception = /** @type {ErrnoException} */ (error);\n\n    if (\n      (exception.code === 'ERR_UNSUPPORTED_DIR_IMPORT' ||\n        exception.code === 'ERR_MODULE_NOT_FOUND') &&\n      typeof exception.url === 'string'\n    ) {\n      return exception.url\n    }\n\n    throw error\n  }\n}\n\nexport { moduleResolve, resolve };\n"], "mappings": ";;;;;;;AAoFA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,IAAA;EAAA,MAAAF,IAAA,GAAAG,uBAAA,CAAAF,OAAA;EAAAC,GAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,SAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,QAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,KAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,IAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,MAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,KAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,QAAA;EAAA,MAAAP,IAAA,GAAAC,OAAA;EAAAM,OAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,GAAA;EAAA,MAAAR,IAAA,GAAAC,OAAA;EAAAO,EAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAS,MAAA;EAAA,MAAAT,IAAA,GAAAC,OAAA;EAAAQ,KAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAuC,SAAAG,wBAAAO,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAT,uBAAA,YAAAA,CAAAO,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAcvC,MAAMkB,KAAK,GAAG,CAAC,CAAC,CAACL,cAAc;AAE/B,MAAMM,WAAW,GAAG,oBAAoB;AAExC,MAAMC,MAAM,GAAG,IAAIC,GAAG,CAAC,CACrB,QAAQ,EACR,UAAU,EACV,QAAQ,EACR,QAAQ,EAER,UAAU,EACV,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,QAAQ,CACT,CAAC;AAEF,MAAMC,KAAK,GAAG,CAAC,CAAC;AAahB,SAASC,UAAUA,CAACC,KAAK,EAAEC,IAAI,GAAG,KAAK,EAAE;EACvC,OAAOD,KAAK,CAACE,MAAM,GAAG,CAAC,GACnBF,KAAK,CAACG,IAAI,CAAC,IAAIF,IAAI,GAAG,CAAC,GACvB,GAAGD,KAAK,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACD,IAAI,CAAC,IAAI,CAAC,KAAKF,IAAI,IAAID,KAAK,CAACA,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC,EAAE;AAC5E;AAGA,MAAMG,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC1B,MAAMC,kBAAkB,GAAG,kBAAkB;AAE7C,IAAIC,mBAAmB;AAEvBV,KAAK,CAACW,oBAAoB,GAAGC,WAAW,CACtC,sBAAsB,EAMtB,CAACC,IAAI,EAAEC,QAAQ,EAAEC,MAAM,KAAK;EAC1BC,QAAKA,CAAC,CAAC,OAAOH,IAAI,KAAK,QAAQ,EAAE,yBAAyB,CAAC;EAC3D,IAAI,CAACI,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAAC,EAAE;IAC5BA,QAAQ,GAAG,CAACA,QAAQ,CAAC;EACvB;EAEA,IAAIK,OAAO,GAAG,MAAM;EACpB,IAAIN,IAAI,CAACO,QAAQ,CAAC,WAAW,CAAC,EAAE;IAE9BD,OAAO,IAAI,GAAGN,IAAI,GAAG;EACvB,CAAC,MAAM;IACL,MAAMV,IAAI,GAAGU,IAAI,CAACQ,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,GAAG,UAAU;IACzDF,OAAO,IAAI,IAAIN,IAAI,KAAKV,IAAI,GAAG;EACjC;EAEAgB,OAAO,IAAI,UAAU;EAGrB,MAAMG,KAAK,GAAG,EAAE;EAEhB,MAAMC,SAAS,GAAG,EAAE;EAEpB,MAAMC,KAAK,GAAG,EAAE;EAEhB,KAAK,MAAMC,KAAK,IAAIX,QAAQ,EAAE;IAC5BE,QAAKA,CAAC,CACJ,OAAOS,KAAK,KAAK,QAAQ,EACzB,gDACF,CAAC;IAED,IAAI3B,MAAM,CAACV,GAAG,CAACqC,KAAK,CAAC,EAAE;MACrBH,KAAK,CAACI,IAAI,CAACD,KAAK,CAACE,WAAW,CAAC,CAAC,CAAC;IACjC,CAAC,MAAM,IAAI9B,WAAW,CAAC+B,IAAI,CAACH,KAAK,CAAC,KAAK,IAAI,EAAE;MAC3CT,QAAKA,CAAC,CACJS,KAAK,KAAK,QAAQ,EAClB,kDACF,CAAC;MACDD,KAAK,CAACE,IAAI,CAACD,KAAK,CAAC;IACnB,CAAC,MAAM;MACLF,SAAS,CAACG,IAAI,CAACD,KAAK,CAAC;IACvB;EACF;EAIA,IAAIF,SAAS,CAACnB,MAAM,GAAG,CAAC,EAAE;IACxB,MAAMyB,GAAG,GAAGP,KAAK,CAACQ,OAAO,CAAC,QAAQ,CAAC;IACnC,IAAID,GAAG,KAAK,CAAC,CAAC,EAAE;MACdP,KAAK,CAAChB,KAAK,CAACuB,GAAG,EAAE,CAAC,CAAC;MACnBN,SAAS,CAACG,IAAI,CAAC,QAAQ,CAAC;IAC1B;EACF;EAEA,IAAIJ,KAAK,CAAClB,MAAM,GAAG,CAAC,EAAE;IACpBe,OAAO,IAAI,GAAGG,KAAK,CAAClB,MAAM,GAAG,CAAC,GAAG,aAAa,GAAG,SAAS,IAAIH,UAAU,CACtEqB,KAAK,EACL,IACF,CAAC,EAAE;IACH,IAAIC,SAAS,CAACnB,MAAM,GAAG,CAAC,IAAIoB,KAAK,CAACpB,MAAM,GAAG,CAAC,EAAEe,OAAO,IAAI,MAAM;EACjE;EAEA,IAAII,SAAS,CAACnB,MAAM,GAAG,CAAC,EAAE;IACxBe,OAAO,IAAI,kBAAkBlB,UAAU,CAACsB,SAAS,EAAE,IAAI,CAAC,EAAE;IAC1D,IAAIC,KAAK,CAACpB,MAAM,GAAG,CAAC,EAAEe,OAAO,IAAI,MAAM;EACzC;EAEA,IAAIK,KAAK,CAACpB,MAAM,GAAG,CAAC,EAAE;IACpB,IAAIoB,KAAK,CAACpB,MAAM,GAAG,CAAC,EAAE;MACpBe,OAAO,IAAI,UAAUlB,UAAU,CAACuB,KAAK,EAAE,IAAI,CAAC,EAAE;IAChD,CAAC,MAAM;MACL,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC,KAAKH,KAAK,CAAC,CAAC,CAAC,EAAEL,OAAO,IAAI,KAAK;MACzDA,OAAO,IAAI,GAAGK,KAAK,CAAC,CAAC,CAAC,EAAE;IAC1B;EACF;EAEAL,OAAO,IAAI,cAAcY,qBAAqB,CAAChB,MAAM,CAAC,EAAE;EAExD,OAAOI,OAAO;AAChB,CAAC,EACDa,SACF,CAAC;AAEDhC,KAAK,CAACiC,4BAA4B,GAAGrB,WAAW,CAC9C,8BAA8B,EAM9B,CAACsB,OAAO,EAAEC,MAAM,EAAEC,IAAI,GAAGC,SAAS,KAAK;EACrC,OAAO,mBAAmBH,OAAO,KAAKC,MAAM,GAC1CC,IAAI,GAAG,kBAAkBA,IAAI,EAAE,GAAG,EAAE,EACpC;AACJ,CAAC,EACDJ,SACF,CAAC;AAEDhC,KAAK,CAACsC,0BAA0B,GAAG1B,WAAW,CAC5C,4BAA4B,EAM5B,CAAC2B,IAAI,EAAEH,IAAI,EAAEjB,OAAO,KAAK;EACvB,OAAO,0BAA0BoB,IAAI,GACnCH,IAAI,GAAG,oBAAoBA,IAAI,EAAE,GAAG,EAAE,GACrCjB,OAAO,GAAG,KAAKA,OAAO,EAAE,GAAG,EAAE,EAAE;AACpC,CAAC,EACDqB,KACF,CAAC;AAEDxC,KAAK,CAACyC,0BAA0B,GAAG7B,WAAW,CAC5C,4BAA4B,EAQ5B,CAAC8B,WAAW,EAAEC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,GAAG,KAAK,EAAET,IAAI,GAAGC,SAAS,KAAK;EAChE,MAAMS,YAAY,GAChB,OAAOF,MAAM,KAAK,QAAQ,IAC1B,CAACC,QAAQ,IACTD,MAAM,CAACxC,MAAM,GAAG,CAAC,IACjB,CAACwC,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;EAC1B,IAAIJ,GAAG,KAAK,GAAG,EAAE;IACf3B,QAAKA,CAAC,CAAC6B,QAAQ,KAAK,KAAK,CAAC;IAC1B,OACE,iCAAiCG,IAAI,CAACC,SAAS,CAACL,MAAM,CAAC,WAAW,GAClE,yBAAyBF,WAAW,eAClCN,IAAI,GAAG,kBAAkBA,IAAI,EAAE,GAAG,EAAE,GACnCU,YAAY,GAAG,gCAAgC,GAAG,EAAE,EAAE;EAE7D;EAEA,OAAO,YACLD,QAAQ,GAAG,SAAS,GAAG,SAAS,YACtBG,IAAI,CAACC,SAAS,CACxBL,MACF,CAAC,iBAAiBD,GAAG,2BAA2BD,WAAW,eACzDN,IAAI,GAAG,kBAAkBA,IAAI,EAAE,GAAG,EAAE,GACnCU,YAAY,GAAG,gCAAgC,GAAG,EAAE,EAAE;AAC3D,CAAC,EACDN,KACF,CAAC;AAEDxC,KAAK,CAACkD,oBAAoB,GAAGtC,WAAW,CACtC,sBAAsB,EAMtB,CAAC2B,IAAI,EAAEH,IAAI,EAAEe,QAAQ,GAAG,KAAK,KAAK;EAChC,OAAO,eACLA,QAAQ,GAAG,QAAQ,GAAG,SAAS,KAC5BZ,IAAI,mBAAmBH,IAAI,EAAE;AACpC,CAAC,EACDI,KACF,CAAC;AAEDxC,KAAK,CAACoD,6BAA6B,GAAGxC,WAAW,CAC/C,+BAA+B,EAC/B,2CAA2C,EAC3C4B,KACF,CAAC;AAEDxC,KAAK,CAACqD,8BAA8B,GAAGzC,WAAW,CAChD,gCAAgC,EAMhC,CAAC0C,SAAS,EAAEZ,WAAW,EAAEN,IAAI,KAAK;EAChC,OAAO,6BAA6BkB,SAAS,mBAC3CZ,WAAW,GAAG,eAAeA,WAAW,cAAc,GAAG,EAAE,kBAC3CN,IAAI,EAAE;AAC1B,CAAC,EACDJ,SACF,CAAC;AAEDhC,KAAK,CAACuD,6BAA6B,GAAG3C,WAAW,CAC/C,+BAA+B,EAM/B,CAAC8B,WAAW,EAAEc,OAAO,EAAEpB,IAAI,GAAGC,SAAS,KAAK;EAC1C,IAAImB,OAAO,KAAK,GAAG,EACjB,OAAO,gCAAgCd,WAAW,eAChDN,IAAI,GAAG,kBAAkBA,IAAI,EAAE,GAAG,EAAE,EACpC;EACJ,OAAO,oBAAoBoB,OAAO,oCAAoCd,WAAW,eAC/EN,IAAI,GAAG,kBAAkBA,IAAI,EAAE,GAAG,EAAE,EACpC;AACJ,CAAC,EACDI,KACF,CAAC;AAEDxC,KAAK,CAACyD,0BAA0B,GAAG7C,WAAW,CAC5C,4BAA4B,EAC5B,yCAAyC,GACvC,uCAAuC,EACzC4B,KACF,CAAC;AAEDxC,KAAK,CAAC0D,+BAA+B,GAAG9C,WAAW,CACjD,iCAAiC,EACjC,6GAA6G,EAC7GoB,SACF,CAAC;AAEDhC,KAAK,CAAC2D,0BAA0B,GAAG/C,WAAW,CAC5C,4BAA4B,EAK5B,CAACgD,SAAS,EAAErB,IAAI,KAAK;EACnB,OAAO,2BAA2BqB,SAAS,SAASrB,IAAI,EAAE;AAC5D,CAAC,EACDP,SACF,CAAC;AAEDhC,KAAK,CAAC6D,qBAAqB,GAAGjD,WAAW,CACvC,uBAAuB,EAMvB,CAACC,IAAI,EAAEY,KAAK,EAAEU,MAAM,GAAG,YAAY,KAAK;EACtC,IAAI2B,SAAS,GAAG,IAAAC,eAAO,EAACtC,KAAK,CAAC;EAE9B,IAAIqC,SAAS,CAAC1D,MAAM,GAAG,GAAG,EAAE;IAC1B0D,SAAS,GAAG,GAAGA,SAAS,CAACxD,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK;EAC7C;EAEA,MAAMH,IAAI,GAAGU,IAAI,CAACQ,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,GAAG,UAAU;EAEzD,OAAO,OAAOlB,IAAI,KAAKU,IAAI,KAAKsB,MAAM,cAAc2B,SAAS,EAAE;AACjE,CAAC,EACD9B,SAGF,CAAC;AAUD,SAASpB,WAAWA,CAACoD,GAAG,EAAEvC,KAAK,EAAEwC,WAAW,EAAE;EAG5C1D,QAAQ,CAACjB,GAAG,CAAC0E,GAAG,EAAEvC,KAAK,CAAC;EAExB,OAAOyC,qBAAqB,CAACD,WAAW,EAAED,GAAG,CAAC;AAChD;AAOA,SAASE,qBAAqBA,CAACC,IAAI,EAAExB,GAAG,EAAE;EAExC,OAAOyB,SAAS;EAIhB,SAASA,SAASA,CAAC,GAAGC,UAAU,EAAE;IAChC,MAAMC,KAAK,GAAG9B,KAAK,CAAC+B,eAAe;IACnC,IAAIC,8BAA8B,CAAC,CAAC,EAAEhC,KAAK,CAAC+B,eAAe,GAAG,CAAC;IAC/D,MAAME,KAAK,GAAG,IAAIN,IAAI,CAAC,CAAC;IAExB,IAAIK,8BAA8B,CAAC,CAAC,EAAEhC,KAAK,CAAC+B,eAAe,GAAGD,KAAK;IACnE,MAAMnD,OAAO,GAAGuD,UAAU,CAAC/B,GAAG,EAAE0B,UAAU,EAAEI,KAAK,CAAC;IAClDhF,MAAM,CAACkF,gBAAgB,CAACF,KAAK,EAAE;MAG7BtD,OAAO,EAAE;QACPM,KAAK,EAAEN,OAAO;QACdyD,UAAU,EAAE,KAAK;QACjBC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE;MAChB,CAAC;MACDC,QAAQ,EAAE;QAERtD,KAAKA,CAAA,EAAG;UACN,OAAO,GAAG,IAAI,CAACZ,IAAI,KAAK8B,GAAG,MAAM,IAAI,CAACxB,OAAO,EAAE;QACjD,CAAC;QACDyD,UAAU,EAAE,KAAK;QACjBC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE;MAChB;IACF,CAAC,CAAC;IAEFE,uBAAuB,CAACP,KAAK,CAAC;IAE9BA,KAAK,CAACQ,IAAI,GAAGtC,GAAG;IAChB,OAAO8B,KAAK;EACd;AACF;AAKA,SAASD,8BAA8BA,CAAA,EAAG;EAGxC,IAAI;IACF,IAAIU,GAACA,CAAC,CAACC,eAAe,CAACC,kBAAkB,CAAC,CAAC,EAAE;MAC3C,OAAO,KAAK;IACd;EACF,CAAC,CAAC,OAAAC,OAAA,EAAM,CAAC;EAET,MAAMC,IAAI,GAAG7F,MAAM,CAACE,wBAAwB,CAAC6C,KAAK,EAAE,iBAAiB,CAAC;EACtE,IAAI8C,IAAI,KAAKjD,SAAS,EAAE;IACtB,OAAO5C,MAAM,CAAC8F,YAAY,CAAC/C,KAAK,CAAC;EACnC;EAEA,OAAO5C,KAAK,CAACJ,IAAI,CAAC8F,IAAI,EAAE,UAAU,CAAC,IAAIA,IAAI,CAACT,QAAQ,KAAKxC,SAAS,GAC9DiD,IAAI,CAACT,QAAQ,GACbS,IAAI,CAAChG,GAAG,KAAK+C,SAAS;AAC5B;AAQA,SAASmD,eAAeA,CAACC,eAAe,EAAE;EAGxC,MAAMC,MAAM,GAAGjF,kBAAkB,GAAGgF,eAAe,CAAC5E,IAAI;EACxDpB,MAAM,CAACC,cAAc,CAAC+F,eAAe,EAAE,MAAM,EAAE;IAAChE,KAAK,EAAEiE;EAAM,CAAC,CAAC;EAC/D,OAAOD,eAAe;AACxB;AAEA,MAAMT,uBAAuB,GAAGQ,eAAe,CAM7C,UAAUf,KAAK,EAAE;EACf,MAAMkB,yBAAyB,GAAGnB,8BAA8B,CAAC,CAAC;EAClE,IAAImB,yBAAyB,EAAE;IAC7BjF,mBAAmB,GAAG8B,KAAK,CAAC+B,eAAe;IAC3C/B,KAAK,CAAC+B,eAAe,GAAGqB,MAAM,CAACC,iBAAiB;EAClD;EAEArD,KAAK,CAACsD,iBAAiB,CAACrB,KAAK,CAAC;EAG9B,IAAIkB,yBAAyB,EAAEnD,KAAK,CAAC+B,eAAe,GAAG7D,mBAAmB;EAE1E,OAAO+D,KAAK;AACd,CACF,CAAC;AAQD,SAASC,UAAUA,CAAC/B,GAAG,EAAE0B,UAAU,EAAE0B,IAAI,EAAE;EACzC,MAAM5E,OAAO,GAAGZ,QAAQ,CAAClB,GAAG,CAACsD,GAAG,CAAC;EACjC3B,QAAKA,CAAC,CAACG,OAAO,KAAKkB,SAAS,EAAE,gCAAgC,CAAC;EAE/D,IAAI,OAAOlB,OAAO,KAAK,UAAU,EAAE;IACjCH,QAAKA,CAAC,CACJG,OAAO,CAACf,MAAM,IAAIiE,UAAU,CAACjE,MAAM,EACnC,SAASuC,GAAG,oCAAoC0B,UAAU,CAACjE,MAAM,aAAa,GAC5E,4BAA4Be,OAAO,CAACf,MAAM,IAC9C,CAAC;IACD,OAAO4F,OAAO,CAACC,KAAK,CAAC9E,OAAO,EAAE4E,IAAI,EAAE1B,UAAU,CAAC;EACjD;EAEA,MAAM6B,KAAK,GAAG,aAAa;EAC3B,IAAIC,cAAc,GAAG,CAAC;EACtB,OAAOD,KAAK,CAACtE,IAAI,CAACT,OAAO,CAAC,KAAK,IAAI,EAAEgF,cAAc,EAAE;EACrDnF,QAAKA,CAAC,CACJmF,cAAc,KAAK9B,UAAU,CAACjE,MAAM,EACpC,SAASuC,GAAG,oCAAoC0B,UAAU,CAACjE,MAAM,aAAa,GAC5E,4BAA4B+F,cAAc,IAC9C,CAAC;EACD,IAAI9B,UAAU,CAACjE,MAAM,KAAK,CAAC,EAAE,OAAOe,OAAO;EAE3CkD,UAAU,CAAC+B,OAAO,CAACjF,OAAO,CAAC;EAC3B,OAAO6E,OAAO,CAACC,KAAK,CAACI,cAAM,EAAE,IAAI,EAAEhC,UAAU,CAAC;AAChD;AAOA,SAAStC,qBAAqBA,CAACN,KAAK,EAAE;EACpC,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKY,SAAS,EAAE;IACzC,OAAOiE,MAAM,CAAC7E,KAAK,CAAC;EACtB;EAEA,IAAI,OAAOA,KAAK,KAAK,UAAU,IAAIA,KAAK,CAACZ,IAAI,EAAE;IAC7C,OAAO,YAAYY,KAAK,CAACZ,IAAI,EAAE;EACjC;EAEA,IAAI,OAAOY,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAIA,KAAK,CAACwC,WAAW,IAAIxC,KAAK,CAACwC,WAAW,CAACpD,IAAI,EAAE;MAC/C,OAAO,kBAAkBY,KAAK,CAACwC,WAAW,CAACpD,IAAI,EAAE;IACnD;IAEA,OAAO,GAAG,IAAAkD,eAAO,EAACtC,KAAK,EAAE;MAAC8E,KAAK,EAAE,CAAC;IAAC,CAAC,CAAC,EAAE;EACzC;EAEA,IAAIzC,SAAS,GAAG,IAAAC,eAAO,EAACtC,KAAK,EAAE;IAAC+E,MAAM,EAAE;EAAK,CAAC,CAAC;EAE/C,IAAI1C,SAAS,CAAC1D,MAAM,GAAG,EAAE,EAAE;IACzB0D,SAAS,GAAG,GAAGA,SAAS,CAACxD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK;EAC5C;EAEA,OAAO,QAAQ,OAAOmB,KAAK,KAAKqC,SAAS,GAAG;AAC9C;AASA,MAAM2C,gBAAgB,GAAG,CAAC,CAAC,CAAClH,cAAc;AAE1C,MAAM;EAAC+C,0BAA0B,EAAEoE;AAA4B,CAAC,GAAG1G,KAAK;AAGxE,MAAM2G,KAAK,GAAG,IAAInG,GAAG,CAAC,CAAC;AAOvB,SAASoG,IAAIA,CAACC,QAAQ,EAAE;EAACzE,IAAI;EAAEkB;AAAS,CAAC,EAAE;EACzC,MAAMwD,QAAQ,GAAGH,KAAK,CAACtH,GAAG,CAACwH,QAAQ,CAAC;EAEpC,IAAIC,QAAQ,EAAE;IACZ,OAAOA,QAAQ;EACjB;EAGA,IAAIC,MAAM;EAEV,IAAI;IACFA,MAAM,GAAGC,aAAE,CAACC,YAAY,CAAC1E,MAAGA,CAAC,CAAC2E,gBAAgB,CAACL,QAAQ,CAAC,EAAE,MAAM,CAAC;EACnE,CAAC,CAAC,OAAOpC,KAAK,EAAE;IACd,MAAM0C,SAAS,GAAkC1C,KAAM;IAEvD,IAAI0C,SAAS,CAAClC,IAAI,KAAK,QAAQ,EAAE;MAC/B,MAAMkC,SAAS;IACjB;EACF;EAGA,MAAMC,MAAM,GAAG;IACbC,MAAM,EAAE,KAAK;IACbC,SAAS,EAAET,QAAQ;IACnBU,IAAI,EAAElF,SAAS;IACfxB,IAAI,EAAEwB,SAAS;IACflC,IAAI,EAAE,MAAM;IACZqH,OAAO,EAAEnF,SAAS;IAClBoF,OAAO,EAAEpF;EACX,CAAC;EAED,IAAI0E,MAAM,KAAK1E,SAAS,EAAE;IAExB,IAAIqF,MAAM;IAEV,IAAI;MACFA,MAAM,GAAG1E,IAAI,CAAC2E,KAAK,CAACZ,MAAM,CAAC;IAC7B,CAAC,CAAC,OAAOa,MAAM,EAAE;MACf,MAAMC,KAAK,GAAkCD,MAAO;MACpD,MAAMnD,KAAK,GAAG,IAAIiC,4BAA4B,CAC5CG,QAAQ,EACR,CAACzE,IAAI,GAAG,IAAIkB,SAAS,SAAS,GAAG,EAAE,IAAI,IAAAwE,oBAAa,EAAC1F,IAAI,IAAIkB,SAAS,CAAC,EACvEuE,KAAK,CAAC1G,OACR,CAAC;MACDsD,KAAK,CAACoD,KAAK,GAAGA,KAAK;MACnB,MAAMpD,KAAK;IACb;IAEA2C,MAAM,CAACC,MAAM,GAAG,IAAI;IAEpB,IACEZ,gBAAgB,CAACjH,IAAI,CAACkI,MAAM,EAAE,MAAM,CAAC,IACrC,OAAOA,MAAM,CAAC7G,IAAI,KAAK,QAAQ,EAC/B;MACAuG,MAAM,CAACvG,IAAI,GAAG6G,MAAM,CAAC7G,IAAI;IAC3B;IAEA,IACE4F,gBAAgB,CAACjH,IAAI,CAACkI,MAAM,EAAE,MAAM,CAAC,IACrC,OAAOA,MAAM,CAACH,IAAI,KAAK,QAAQ,EAC/B;MACAH,MAAM,CAACG,IAAI,GAAGG,MAAM,CAACH,IAAI;IAC3B;IAEA,IAAId,gBAAgB,CAACjH,IAAI,CAACkI,MAAM,EAAE,SAAS,CAAC,EAAE;MAE5CN,MAAM,CAACI,OAAO,GAAGE,MAAM,CAACF,OAAO;IACjC;IAEA,IAAIf,gBAAgB,CAACjH,IAAI,CAACkI,MAAM,EAAE,SAAS,CAAC,EAAE;MAE5CN,MAAM,CAACK,OAAO,GAAGC,MAAM,CAACD,OAAO;IACjC;IAGA,IACEhB,gBAAgB,CAACjH,IAAI,CAACkI,MAAM,EAAE,MAAM,CAAC,KACpCA,MAAM,CAACvH,IAAI,KAAK,UAAU,IAAIuH,MAAM,CAACvH,IAAI,KAAK,QAAQ,CAAC,EACxD;MACAiH,MAAM,CAACjH,IAAI,GAAGuH,MAAM,CAACvH,IAAI;IAC3B;EACF;EAEAwG,KAAK,CAACrH,GAAG,CAACuH,QAAQ,EAAEO,MAAM,CAAC;EAE3B,OAAOA,MAAM;AACf;AAMA,SAASW,qBAAqBA,CAACC,QAAQ,EAAE;EAEvC,IAAIC,cAAc,GAAG,IAAIC,GAAG,CAAC,cAAc,EAAEF,QAAQ,CAAC;EAEtD,OAAO,IAAI,EAAE;IACX,MAAMG,eAAe,GAAGF,cAAc,CAACG,QAAQ;IAC/C,IAAID,eAAe,CAAC/G,QAAQ,CAAC,2BAA2B,CAAC,EAAE;MACzD;IACF;IAEA,MAAMiH,aAAa,GAAGzB,IAAI,CAAC,IAAAkB,oBAAa,EAACG,cAAc,CAAC,EAAE;MACxD3E,SAAS,EAAE0E;IACb,CAAC,CAAC;IAEF,IAAIK,aAAa,CAAChB,MAAM,EAAE;MACxB,OAAOgB,aAAa;IACtB;IAEA,MAAMC,kBAAkB,GAAGL,cAAc;IACzCA,cAAc,GAAG,IAAIC,GAAG,CAAC,iBAAiB,EAAED,cAAc,CAAC;IAI3D,IAAIA,cAAc,CAACG,QAAQ,KAAKE,kBAAkB,CAACF,QAAQ,EAAE;MAC3D;IACF;EACF;EAEA,MAAMD,eAAe,GAAG,IAAAL,oBAAa,EAACG,cAAc,CAAC;EAGrD,OAAO;IACLX,SAAS,EAAEa,eAAe;IAC1Bd,MAAM,EAAE,KAAK;IACblH,IAAI,EAAE;EACR,CAAC;AACH;AAOA,SAASoI,cAAcA,CAACC,GAAG,EAAE;EAE3B,OAAOT,qBAAqB,CAACS,GAAG,CAAC,CAACrI,IAAI;AACxC;AAOA,MAAM;EAACwD;AAA0B,CAAC,GAAG3D,KAAK;AAE1C,MAAMT,cAAc,GAAG,CAAC,CAAC,CAACA,cAAc;AAGxC,MAAMkJ,kBAAkB,GAAG;EAEzBvJ,SAAS,EAAE,IAAI;EACf,MAAM,EAAE,UAAU;EAClB,KAAK,EAAE,QAAQ;EACf,OAAO,EAAE,MAAM;EACf,MAAM,EAAE;AACV,CAAC;AAMD,SAASwJ,YAAYA,CAACC,IAAI,EAAE;EAC1B,IACEA,IAAI,IACJ,+DAA+D,CAACC,IAAI,CAACD,IAAI,CAAC,EAE1E,OAAO,QAAQ;EACjB,IAAIA,IAAI,KAAK,kBAAkB,EAAE,OAAO,MAAM;EAC9C,OAAO,IAAI;AACb;AAaA,MAAME,gBAAgB,GAAG;EAEvB3J,SAAS,EAAE,IAAI;EACf,OAAO,EAAE4J,2BAA2B;EACpC,OAAO,EAAEC,2BAA2B;EACpC,OAAO,EAAEC,2BAA2B;EACpC,QAAQ,EAAEA,2BAA2B;EACrC,OAAOC,CAAA,EAAG;IACR,OAAO,SAAS;EAClB;AACF,CAAC;AAKD,SAASH,2BAA2BA,CAACpB,MAAM,EAAE;EAC3C,MAAM;IAAC,CAAC,EAAEiB;EAAI,CAAC,GAAG,mCAAmC,CAAC/G,IAAI,CACxD8F,MAAM,CAACU,QACT,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACvB,OAAOM,YAAY,CAACC,IAAI,CAAC;AAC3B;AAYA,SAASO,OAAOA,CAACV,GAAG,EAAE;EACpB,MAAMJ,QAAQ,GAAGI,GAAG,CAACJ,QAAQ;EAC7B,IAAIe,KAAK,GAAGf,QAAQ,CAAChI,MAAM;EAE3B,OAAO+I,KAAK,EAAE,EAAE;IACd,MAAMlE,IAAI,GAAGmD,QAAQ,CAACgB,WAAW,CAACD,KAAK,CAAC;IAExC,IAAIlE,IAAI,KAAK,EAAE,EAAY;MACzB,OAAO,EAAE;IACX;IAEA,IAAIA,IAAI,KAAK,EAAE,EAAY;MACzB,OAAOmD,QAAQ,CAACgB,WAAW,CAACD,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE,GACzC,EAAE,GACFf,QAAQ,CAAC9H,KAAK,CAAC6I,KAAK,CAAC;IAC3B;EACF;EAEA,OAAO,EAAE;AACX;AAKA,SAASJ,2BAA2BA,CAACP,GAAG,EAAEa,QAAQ,EAAEC,YAAY,EAAE;EAChE,MAAM7H,KAAK,GAAGyH,OAAO,CAACV,GAAG,CAAC;EAE1B,IAAI/G,KAAK,KAAK,KAAK,EAAE;IACnB,MAAM8H,WAAW,GAAGhB,cAAc,CAACC,GAAG,CAAC;IAEvC,IAAIe,WAAW,KAAK,MAAM,EAAE;MAC1B,OAAOA,WAAW;IACpB;IAEA,OAAO,UAAU;EACnB;EAEA,IAAI9H,KAAK,KAAK,EAAE,EAAE;IAChB,MAAM8H,WAAW,GAAGhB,cAAc,CAACC,GAAG,CAAC;IAGvC,IAAIe,WAAW,KAAK,MAAM,IAAIA,WAAW,KAAK,UAAU,EAAE;MACxD,OAAO,UAAU;IACnB;IAIA,OAAO,QAAQ;EACjB;EAEA,MAAMlD,MAAM,GAAGoC,kBAAkB,CAAChH,KAAK,CAAC;EACxC,IAAI4E,MAAM,EAAE,OAAOA,MAAM;EAGzB,IAAIiD,YAAY,EAAE;IAChB,OAAOjH,SAAS;EAClB;EAEA,MAAMmH,QAAQ,GAAG,IAAA1B,oBAAa,EAACU,GAAG,CAAC;EACnC,MAAM,IAAI7E,0BAA0B,CAAClC,KAAK,EAAE+H,QAAQ,CAAC;AACvD;AAEA,SAASR,2BAA2BA,CAAA,EAAG,CAEvC;AAOA,SAASS,6BAA6BA,CAACjB,GAAG,EAAEkB,OAAO,EAAE;EACnD,MAAMC,QAAQ,GAAGnB,GAAG,CAACmB,QAAQ;EAE7B,IAAI,CAACpK,cAAc,CAACC,IAAI,CAACqJ,gBAAgB,EAAEc,QAAQ,CAAC,EAAE;IACpD,OAAO,IAAI;EACb;EAEA,OAAOd,gBAAgB,CAACc,QAAQ,CAAC,CAACnB,GAAG,EAAEkB,OAAO,EAAE,IAAI,CAAC,IAAI,IAAI;AAC/D;AAOA,MAAM;EAAC7F;AAAqB,CAAC,GAAG7D,KAAK;AAKrC,MAAM4J,kBAAkB,GAAGnK,MAAM,CAACoK,MAAM,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC5D,MAAMC,sBAAsB,GAAG,IAAI/J,GAAG,CAAC6J,kBAAkB,CAAC;AAK1D,SAASG,oBAAoBA,CAAA,EAAG;EAC9B,OAAOH,kBAAkB;AAC3B;AAKA,SAASI,uBAAuBA,CAAA,EAAG;EACjC,OAAOF,sBAAsB;AAC/B;AAMA,SAASG,gBAAgBA,CAACC,UAAU,EAAE;EACpC,IAAIA,UAAU,KAAK7H,SAAS,IAAI6H,UAAU,KAAKH,oBAAoB,CAAC,CAAC,EAAE;IACrE,IAAI,CAAC9I,KAAK,CAACC,OAAO,CAACgJ,UAAU,CAAC,EAAE;MAC9B,MAAM,IAAIrG,qBAAqB,CAC7B,YAAY,EACZqG,UAAU,EACV,mBACF,CAAC;IACH;IAEA,OAAO,IAAInK,GAAG,CAACmK,UAAU,CAAC;EAC5B;EAEA,OAAOF,uBAAuB,CAAC,CAAC;AAClC;AAOA,MAAMG,4BAA4B,GAAGC,MAAM,CAACC,SAAS,CAACC,MAAM,CAACC,OAAO,CAAC;AAErE,MAAM;EACJnH,6BAA6B;EAC7BnB,4BAA4B;EAC5BK,0BAA0B;EAC1BG,0BAA0B;EAC1BS,oBAAoB;EACpBG,8BAA8B;EAC9BE,6BAA6B;EAC7BE,0BAA0B;EAC1BC;AACF,CAAC,GAAG1D,KAAK;AAET,MAAMwK,GAAG,GAAG,CAAC,CAAC,CAACjL,cAAc;AAE7B,MAAMkL,mBAAmB,GACvB,0KAA0K;AAC5K,MAAMC,6BAA6B,GACjC,yKAAyK;AAC3K,MAAMC,uBAAuB,GAAG,UAAU;AAC1C,MAAMC,YAAY,GAAG,KAAK;AAC1B,MAAMC,qBAAqB,GAAG,UAAU;AAExC,MAAMC,sBAAsB,GAAG,IAAI/K,GAAG,CAAC,CAAC;AAExC,MAAMgL,gBAAgB,GAAG,UAAU;AAYnC,SAASC,6BAA6BA,CACpCpI,MAAM,EACNV,OAAO,EACP+I,KAAK,EACLC,cAAc,EACdC,QAAQ,EACR/I,IAAI,EACJgJ,QAAQ,EACR;EAEA,IAAIC,SAAMA,CAAC,CAACC,aAAa,EAAE;IACzB;EACF;EAEA,MAAMhE,SAAS,GAAG,IAAAQ,oBAAa,EAACoD,cAAc,CAAC;EAC/C,MAAMK,MAAM,GAAGR,gBAAgB,CAACnJ,IAAI,CAACwJ,QAAQ,GAAGxI,MAAM,GAAGV,OAAO,CAAC,KAAK,IAAI;EAC1EmJ,SAAMA,CAAC,CAACG,WAAW,CACjB,qBACED,MAAM,GAAG,cAAc,GAAG,oCAAoC,eACjD3I,MAAM,eAAe,GAClC,YAAYV,OAAO,KACjBA,OAAO,KAAK+I,KAAK,GAAG,EAAE,GAAG,eAAeA,KAAK,IAAI,WAEjDE,QAAQ,GAAG,SAAS,GAAG,SAAS,+CACa7D,SAAS,GACtDlF,IAAI,GAAG,kBAAkB,IAAA0F,oBAAa,EAAC1F,IAAI,CAAC,EAAE,GAAG,EAAE,GAClD,EACL,oBAAoB,EACpB,SACF,CAAC;AACH;AASA,SAASqJ,0BAA0BA,CAACjD,GAAG,EAAE0C,cAAc,EAAE9I,IAAI,EAAEmF,IAAI,EAAE;EAEnE,IAAI8D,SAAMA,CAAC,CAACC,aAAa,EAAE;IACzB;EACF;EAEA,MAAMjF,MAAM,GAAGoD,6BAA6B,CAACjB,GAAG,EAAE;IAACkD,SAAS,EAAEtJ,IAAI,CAACuJ;EAAI,CAAC,CAAC;EACzE,IAAItF,MAAM,KAAK,QAAQ,EAAE;EACzB,MAAMuF,OAAO,GAAG,IAAA9D,oBAAa,EAACU,GAAG,CAACmD,IAAI,CAAC;EACvC,MAAMjJ,WAAW,GAAG,IAAAoF,oBAAa,EAAC,KAAI+D,UAAK,EAAC,GAAG,EAAEX,cAAc,CAAC,CAAC;EACjE,MAAMY,QAAQ,GAAG,IAAAhE,oBAAa,EAAC1F,IAAI,CAAC;EACpC,IAAI,CAACmF,IAAI,EAAE;IACT8D,SAAMA,CAAC,CAACG,WAAW,CACjB,gEAAgE9I,WAAW,oCAAoCkJ,OAAO,CAACtL,KAAK,CAC1HoC,WAAW,CAACtC,MACd,CAAC,oBAAoB0L,QAAQ,wEAAwE,EACrG,oBAAoB,EACpB,SACF,CAAC;EACH,CAAC,MAAM,IAAIvJ,MAAGA,CAAC,CAACwJ,OAAO,CAACrJ,WAAW,EAAE6E,IAAI,CAAC,KAAKqE,OAAO,EAAE;IACtDP,SAAMA,CAAC,CAACG,WAAW,CACjB,WAAW9I,WAAW,+BAA+B6E,IAAI,KAAK,GAC5D,sEAAsEqE,OAAO,CAACtL,KAAK,CACjFoC,WAAW,CAACtC,MACd,CAAC,oBAAoB0L,QAAQ,4DAA4D,GACzF,4BAA4B,EAC9B,oBAAoB,EACpB,SACF,CAAC;EACH;AACF;AAMA,SAASE,WAAWA,CAACzJ,IAAI,EAAE;EAEzB,IAAI;IACF,OAAO,IAAA0J,cAAQ,EAAC1J,IAAI,CAAC;EACvB,CAAC,CAAC,OAAA2J,QAAA,EAAM,CAKR;AACF;AAaA,SAASC,UAAUA,CAAC3D,GAAG,EAAE;EACvB,MAAM4D,KAAK,GAAG,IAAAH,cAAQ,EAACzD,GAAG,EAAE;IAAC6D,cAAc,EAAE;EAAK,CAAC,CAAC;EACpD,MAAMC,MAAM,GAAGF,KAAK,GAAGA,KAAK,CAACE,MAAM,CAAC,CAAC,GAAGjK,SAAS;EACjD,OAAOiK,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAKjK,SAAS,GAAG,KAAK,GAAGiK,MAAM;AACjE;AAQA,SAASC,iBAAiBA,CAACrB,cAAc,EAAE7C,aAAa,EAAEjG,IAAI,EAAE;EAE9D,IAAIoK,KAAK;EACT,IAAInE,aAAa,CAACd,IAAI,KAAKlF,SAAS,EAAE;IACpCmK,KAAK,GAAG,KAAIX,UAAK,EAACxD,aAAa,CAACd,IAAI,EAAE2D,cAAc,CAAC;IAErD,IAAIiB,UAAU,CAACK,KAAK,CAAC,EAAE,OAAOA,KAAK;IAEnC,MAAMC,KAAK,GAAG,CACZ,KAAKpE,aAAa,CAACd,IAAI,KAAK,EAC5B,KAAKc,aAAa,CAACd,IAAI,OAAO,EAC9B,KAAKc,aAAa,CAACd,IAAI,OAAO,EAC9B,KAAKc,aAAa,CAACd,IAAI,WAAW,EAClC,KAAKc,aAAa,CAACd,IAAI,aAAa,EACpC,KAAKc,aAAa,CAACd,IAAI,aAAa,CACrC;IACD,IAAIvI,CAAC,GAAG,CAAC,CAAC;IAEV,OAAO,EAAEA,CAAC,GAAGyN,KAAK,CAACrM,MAAM,EAAE;MACzBoM,KAAK,GAAG,KAAIX,UAAK,EAACY,KAAK,CAACzN,CAAC,CAAC,EAAEkM,cAAc,CAAC;MAC3C,IAAIiB,UAAU,CAACK,KAAK,CAAC,EAAE;MACvBA,KAAK,GAAGnK,SAAS;IACnB;IAEA,IAAImK,KAAK,EAAE;MACTf,0BAA0B,CACxBe,KAAK,EACLtB,cAAc,EACd9I,IAAI,EACJiG,aAAa,CAACd,IAChB,CAAC;MACD,OAAOiF,KAAK;IACd;EAEF;EAEA,MAAMC,KAAK,GAAG,CAAC,YAAY,EAAE,cAAc,EAAE,cAAc,CAAC;EAC5D,IAAIzN,CAAC,GAAG,CAAC,CAAC;EAEV,OAAO,EAAEA,CAAC,GAAGyN,KAAK,CAACrM,MAAM,EAAE;IACzBoM,KAAK,GAAG,KAAIX,UAAK,EAACY,KAAK,CAACzN,CAAC,CAAC,EAAEkM,cAAc,CAAC;IAC3C,IAAIiB,UAAU,CAACK,KAAK,CAAC,EAAE;IACvBA,KAAK,GAAGnK,SAAS;EACnB;EAEA,IAAImK,KAAK,EAAE;IACTf,0BAA0B,CAACe,KAAK,EAAEtB,cAAc,EAAE9I,IAAI,EAAEiG,aAAa,CAACd,IAAI,CAAC;IAC3E,OAAOiF,KAAK;EACd;EAGA,MAAM,IAAItJ,oBAAoB,CAC5B,IAAA4E,oBAAa,EAAC,KAAI+D,UAAK,EAAC,GAAG,EAAEX,cAAc,CAAC,CAAC,EAC7C,IAAApD,oBAAa,EAAC1F,IAAI,CACpB,CAAC;AACH;AAQA,SAASsK,kBAAkBA,CAAC1E,QAAQ,EAAE5F,IAAI,EAAEuK,gBAAgB,EAAE;EAC5D,IAAI9B,qBAAqB,CAACjJ,IAAI,CAACoG,QAAQ,CAACI,QAAQ,CAAC,KAAK,IAAI,EAAE;IAC1D,MAAM,IAAInG,4BAA4B,CACpC+F,QAAQ,CAACI,QAAQ,EACjB,iDAAiD,EACjD,IAAAN,oBAAa,EAAC1F,IAAI,CACpB,CAAC;EACH;EAGA,IAAIwK,QAAQ;EAEZ,IAAI;IACFA,QAAQ,GAAG,IAAA9E,oBAAa,EAACE,QAAQ,CAAC;EACpC,CAAC,CAAC,OAAOvD,KAAK,EAAE;IACd,MAAMoD,KAAK,GAAkCpD,KAAM;IACnDhF,MAAM,CAACC,cAAc,CAACmI,KAAK,EAAE,OAAO,EAAE;MAACpG,KAAK,EAAE6E,MAAM,CAAC0B,QAAQ;IAAC,CAAC,CAAC;IAChEvI,MAAM,CAACC,cAAc,CAACmI,KAAK,EAAE,QAAQ,EAAE;MAACpG,KAAK,EAAE6E,MAAM,CAAClE,IAAI;IAAC,CAAC,CAAC;IAC7D,MAAMyF,KAAK;EACb;EAEA,MAAMuE,KAAK,GAAGJ,WAAW,CACvBY,QAAQ,CAACxL,QAAQ,CAAC,GAAG,CAAC,GAAGwL,QAAQ,CAACtM,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGsM,QAChD,CAAC;EAED,IAAIR,KAAK,IAAIA,KAAK,CAACS,WAAW,CAAC,CAAC,EAAE;IAChC,MAAMpI,KAAK,GAAG,IAAIhB,0BAA0B,CAACmJ,QAAQ,EAAE,IAAA9E,oBAAa,EAAC1F,IAAI,CAAC,CAAC;IAE3EqC,KAAK,CAAC+D,GAAG,GAAGlC,MAAM,CAAC0B,QAAQ,CAAC;IAC5B,MAAMvD,KAAK;EACb;EAEA,IAAI,CAAC2H,KAAK,IAAI,CAACA,KAAK,CAACE,MAAM,CAAC,CAAC,EAAE;IAC7B,MAAM7H,KAAK,GAAG,IAAIvB,oBAAoB,CACpC0J,QAAQ,IAAI5E,QAAQ,CAACI,QAAQ,EAC7BhG,IAAI,IAAI,IAAA0F,oBAAa,EAAC1F,IAAI,CAAC,EAC3B,IACF,CAAC;IAEDqC,KAAK,CAAC+D,GAAG,GAAGlC,MAAM,CAAC0B,QAAQ,CAAC;IAC5B,MAAMvD,KAAK;EACb;EAEA,IAAI,CAACkI,gBAAgB,EAAE;IACrB,MAAMG,IAAI,GAAG,IAAAC,kBAAY,EAACH,QAAQ,CAAC;IACnC,MAAM;MAACI,MAAM;MAAEC;IAAI,CAAC,GAAGjF,QAAQ;IAC/BA,QAAQ,GAAG,IAAAkF,oBAAa,EAACJ,IAAI,IAAIF,QAAQ,CAACxL,QAAQ,CAACmB,MAAGA,CAAC,CAAC4K,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;IACzEnF,QAAQ,CAACgF,MAAM,GAAGA,MAAM;IACxBhF,QAAQ,CAACiF,IAAI,GAAGA,IAAI;EACtB;EAEA,OAAOjF,QAAQ;AACjB;AAQA,SAASoF,gBAAgBA,CAAC9J,SAAS,EAAE4H,cAAc,EAAE9I,IAAI,EAAE;EACzD,OAAO,IAAIiB,8BAA8B,CACvCC,SAAS,EACT4H,cAAc,IAAI,IAAApD,oBAAa,EAAC,KAAI+D,UAAK,EAAC,GAAG,EAAEX,cAAc,CAAC,CAAC,EAC/D,IAAApD,oBAAa,EAAC1F,IAAI,CACpB,CAAC;AACH;AAQA,SAASiL,eAAeA,CAAC7J,OAAO,EAAE0H,cAAc,EAAE9I,IAAI,EAAE;EACtD,OAAO,IAAImB,6BAA6B,CACtC,IAAAuE,oBAAa,EAAC,KAAI+D,UAAK,EAAC,GAAG,EAAEX,cAAc,CAAC,CAAC,EAC7C1H,OAAO,EACPpB,IAAI,IAAI,IAAA0F,oBAAa,EAAC1F,IAAI,CAC5B,CAAC;AACH;AAUA,SAASkL,mBAAmBA,CAACpL,OAAO,EAAE+I,KAAK,EAAEC,cAAc,EAAEC,QAAQ,EAAE/I,IAAI,EAAE;EAC3E,MAAMD,MAAM,GAAG,4CAA4C8I,KAAK,cAC9DE,QAAQ,GAAG,SAAS,GAAG,SAAS,mBACf,IAAArD,oBAAa,EAACoD,cAAc,CAAC,EAAE;EAClD,MAAM,IAAIjJ,4BAA4B,CACpCC,OAAO,EACPC,MAAM,EACNC,IAAI,IAAI,IAAA0F,oBAAa,EAAC1F,IAAI,CAC5B,CAAC;AACH;AAUA,SAASmL,oBAAoBA,CAAC/J,OAAO,EAAEZ,MAAM,EAAEsI,cAAc,EAAEC,QAAQ,EAAE/I,IAAI,EAAE;EAC7EQ,MAAM,GACJ,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,GACzCI,IAAI,CAACC,SAAS,CAACL,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC,GAChC,GAAGA,MAAM,EAAE;EAEjB,OAAO,IAAIH,0BAA0B,CACnC,IAAAqF,oBAAa,EAAC,KAAI+D,UAAK,EAAC,GAAG,EAAEX,cAAc,CAAC,CAAC,EAC7C1H,OAAO,EACPZ,MAAM,EACNuI,QAAQ,EACR/I,IAAI,IAAI,IAAA0F,oBAAa,EAAC1F,IAAI,CAC5B,CAAC;AACH;AAcA,SAASoL,0BAA0BA,CACjC5K,MAAM,EACNY,OAAO,EACPyH,KAAK,EACLC,cAAc,EACd9I,IAAI,EACJqL,OAAO,EACPtC,QAAQ,EACRuC,SAAS,EACTxD,UAAU,EACV;EACA,IAAI1G,OAAO,KAAK,EAAE,IAAI,CAACiK,OAAO,IAAI7K,MAAM,CAACA,MAAM,CAACxC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EACjE,MAAMmN,oBAAoB,CAACtC,KAAK,EAAErI,MAAM,EAAEsI,cAAc,EAAEC,QAAQ,EAAE/I,IAAI,CAAC;EAE3E,IAAI,CAACQ,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC,EAAE;IAC5B,IAAIoI,QAAQ,IAAI,CAACvI,MAAM,CAACG,UAAU,CAAC,KAAK,CAAC,IAAI,CAACH,MAAM,CAACG,UAAU,CAAC,GAAG,CAAC,EAAE;MACpE,IAAI4K,KAAK,GAAG,KAAK;MAEjB,IAAI;QACF,KAAI9B,UAAK,EAACjJ,MAAM,CAAC;QACjB+K,KAAK,GAAG,IAAI;MACd,CAAC,CAAC,OAAAC,QAAA,EAAM,CAER;MAEA,IAAI,CAACD,KAAK,EAAE;QACV,MAAME,YAAY,GAAGJ,OAAO,GACxBtD,4BAA4B,CAAC3K,IAAI,CAC/BoL,YAAY,EACZhI,MAAM,EACN,MAAMY,OACR,CAAC,GACDZ,MAAM,GAAGY,OAAO;QAEpB,OAAOsK,cAAc,CAACD,YAAY,EAAE3C,cAAc,EAAEhB,UAAU,CAAC;MACjE;IACF;IAEA,MAAMqD,oBAAoB,CAACtC,KAAK,EAAErI,MAAM,EAAEsI,cAAc,EAAEC,QAAQ,EAAE/I,IAAI,CAAC;EAC3E;EAEA,IAAIqI,mBAAmB,CAAC7I,IAAI,CAACgB,MAAM,CAACtC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;IACtD,IAAIoK,6BAA6B,CAAC9I,IAAI,CAACgB,MAAM,CAACtC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;MAChE,IAAI,CAACoN,SAAS,EAAE;QACd,MAAMxL,OAAO,GAAGuL,OAAO,GACnBxC,KAAK,CAACV,OAAO,CAAC,GAAG,EAAE,MAAM/G,OAAO,CAAC,GACjCyH,KAAK,GAAGzH,OAAO;QACnB,MAAMuK,cAAc,GAAGN,OAAO,GAC1BtD,4BAA4B,CAAC3K,IAAI,CAC/BoL,YAAY,EACZhI,MAAM,EACN,MAAMY,OACR,CAAC,GACDZ,MAAM;QACVoI,6BAA6B,CAC3B+C,cAAc,EACd7L,OAAO,EACP+I,KAAK,EACLC,cAAc,EACdC,QAAQ,EACR/I,IAAI,EACJ,IACF,CAAC;MACH;IACF,CAAC,MAAM;MACL,MAAMmL,oBAAoB,CAACtC,KAAK,EAAErI,MAAM,EAAEsI,cAAc,EAAEC,QAAQ,EAAE/I,IAAI,CAAC;IAC3E;EACF;EAEA,MAAM4F,QAAQ,GAAG,KAAI6D,UAAK,EAACjJ,MAAM,EAAEsI,cAAc,CAAC;EAClD,MAAM8C,YAAY,GAAGhG,QAAQ,CAACI,QAAQ;EACtC,MAAM1F,WAAW,GAAG,KAAImJ,UAAK,EAAC,GAAG,EAAEX,cAAc,CAAC,CAAC9C,QAAQ;EAE3D,IAAI,CAAC4F,YAAY,CAACjL,UAAU,CAACL,WAAW,CAAC,EACvC,MAAM6K,oBAAoB,CAACtC,KAAK,EAAErI,MAAM,EAAEsI,cAAc,EAAEC,QAAQ,EAAE/I,IAAI,CAAC;EAE3E,IAAIoB,OAAO,KAAK,EAAE,EAAE,OAAOwE,QAAQ;EAEnC,IAAIyC,mBAAmB,CAAC7I,IAAI,CAAC4B,OAAO,CAAC,KAAK,IAAI,EAAE;IAC9C,MAAMtB,OAAO,GAAGuL,OAAO,GACnBxC,KAAK,CAACV,OAAO,CAAC,GAAG,EAAE,MAAM/G,OAAO,CAAC,GACjCyH,KAAK,GAAGzH,OAAO;IACnB,IAAIkH,6BAA6B,CAAC9I,IAAI,CAAC4B,OAAO,CAAC,KAAK,IAAI,EAAE;MACxD,IAAI,CAACkK,SAAS,EAAE;QACd,MAAMK,cAAc,GAAGN,OAAO,GAC1BtD,4BAA4B,CAAC3K,IAAI,CAC/BoL,YAAY,EACZhI,MAAM,EACN,MAAMY,OACR,CAAC,GACDZ,MAAM;QACVoI,6BAA6B,CAC3B+C,cAAc,EACd7L,OAAO,EACP+I,KAAK,EACLC,cAAc,EACdC,QAAQ,EACR/I,IAAI,EACJ,KACF,CAAC;MACH;IACF,CAAC,MAAM;MACLkL,mBAAmB,CAACpL,OAAO,EAAE+I,KAAK,EAAEC,cAAc,EAAEC,QAAQ,EAAE/I,IAAI,CAAC;IACrE;EACF;EAEA,IAAIqL,OAAO,EAAE;IACX,OAAO,KAAI5B,UAAK,EACd1B,4BAA4B,CAAC3K,IAAI,CAC/BoL,YAAY,EACZ5C,QAAQ,CAAC2D,IAAI,EACb,MAAMnI,OACR,CACF,CAAC;EACH;EAEA,OAAO,KAAIqI,UAAK,EAACrI,OAAO,EAAEwE,QAAQ,CAAC;AACrC;AAMA,SAASiG,YAAYA,CAACtL,GAAG,EAAE;EACzB,MAAMuL,SAAS,GAAGtI,MAAM,CAACjD,GAAG,CAAC;EAC7B,IAAI,GAAGuL,SAAS,EAAE,KAAKvL,GAAG,EAAE,OAAO,KAAK;EACxC,OAAOuL,SAAS,IAAI,CAAC,IAAIA,SAAS,GAAG,UAAa;AACpD;AAcA,SAASC,oBAAoBA,CAC3BjD,cAAc,EACdtI,MAAM,EACNY,OAAO,EACP4K,cAAc,EACdhM,IAAI,EACJqL,OAAO,EACPtC,QAAQ,EACRuC,SAAS,EACTxD,UAAU,EACV;EACA,IAAI,OAAOtH,MAAM,KAAK,QAAQ,EAAE;IAC9B,OAAO4K,0BAA0B,CAC/B5K,MAAM,EACNY,OAAO,EACP4K,cAAc,EACdlD,cAAc,EACd9I,IAAI,EACJqL,OAAO,EACPtC,QAAQ,EACRuC,SAAS,EACTxD,UACF,CAAC;EACH;EAEA,IAAIjJ,KAAK,CAACC,OAAO,CAAC0B,MAAM,CAAC,EAAE;IAEzB,MAAMyL,UAAU,GAAGzL,MAAM;IACzB,IAAIyL,UAAU,CAACjO,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAGxC,IAAIkO,aAAa;IACjB,IAAItP,CAAC,GAAG,CAAC,CAAC;IAEV,OAAO,EAAEA,CAAC,GAAGqP,UAAU,CAACjO,MAAM,EAAE;MAC9B,MAAMmO,UAAU,GAAGF,UAAU,CAACrP,CAAC,CAAC;MAEhC,IAAIwP,aAAa;MACjB,IAAI;QACFA,aAAa,GAAGL,oBAAoB,CAClCjD,cAAc,EACdqD,UAAU,EACV/K,OAAO,EACP4K,cAAc,EACdhM,IAAI,EACJqL,OAAO,EACPtC,QAAQ,EACRuC,SAAS,EACTxD,UACF,CAAC;MACH,CAAC,CAAC,OAAOzF,KAAK,EAAE;QACd,MAAM0C,SAAS,GAAkC1C,KAAM;QACvD6J,aAAa,GAAGnH,SAAS;QACzB,IAAIA,SAAS,CAAClC,IAAI,KAAK,4BAA4B,EAAE;QACrD,MAAMR,KAAK;MACb;MAEA,IAAI+J,aAAa,KAAKnM,SAAS,EAAE;MAEjC,IAAImM,aAAa,KAAK,IAAI,EAAE;QAC1BF,aAAa,GAAG,IAAI;QACpB;MACF;MAEA,OAAOE,aAAa;IACtB;IAEA,IAAIF,aAAa,KAAKjM,SAAS,IAAIiM,aAAa,KAAK,IAAI,EAAE;MACzD,OAAO,IAAI;IACb;IAEA,MAAMA,aAAa;EACrB;EAEA,IAAI,OAAO1L,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,EAAE;IACjD,MAAM6L,IAAI,GAAGhP,MAAM,CAACiP,mBAAmB,CAAC9L,MAAM,CAAC;IAC/C,IAAI5D,CAAC,GAAG,CAAC,CAAC;IAEV,OAAO,EAAEA,CAAC,GAAGyP,IAAI,CAACrO,MAAM,EAAE;MACxB,MAAMuC,GAAG,GAAG8L,IAAI,CAACzP,CAAC,CAAC;MACnB,IAAIiP,YAAY,CAACtL,GAAG,CAAC,EAAE;QACrB,MAAM,IAAIL,0BAA0B,CAClC,IAAAwF,oBAAa,EAACoD,cAAc,CAAC,EAC7B9I,IAAI,EACJ,iDACF,CAAC;MACH;IACF;IAEApD,CAAC,GAAG,CAAC,CAAC;IAEN,OAAO,EAAEA,CAAC,GAAGyP,IAAI,CAACrO,MAAM,EAAE;MACxB,MAAMuC,GAAG,GAAG8L,IAAI,CAACzP,CAAC,CAAC;MACnB,IAAI2D,GAAG,KAAK,SAAS,IAAKuH,UAAU,IAAIA,UAAU,CAAC9K,GAAG,CAACuD,GAAG,CAAE,EAAE;QAE5D,MAAMgM,iBAAiB,GAA2B/L,MAAM,CAACD,GAAG,CAAE;QAC9D,MAAM6L,aAAa,GAAGL,oBAAoB,CACxCjD,cAAc,EACdyD,iBAAiB,EACjBnL,OAAO,EACP4K,cAAc,EACdhM,IAAI,EACJqL,OAAO,EACPtC,QAAQ,EACRuC,SAAS,EACTxD,UACF,CAAC;QACD,IAAIsE,aAAa,KAAKnM,SAAS,EAAE;QACjC,OAAOmM,aAAa;MACtB;IACF;IAEA,OAAO,IAAI;EACb;EAEA,IAAI5L,MAAM,KAAK,IAAI,EAAE;IACnB,OAAO,IAAI;EACb;EAEA,MAAM2K,oBAAoB,CACxBa,cAAc,EACdxL,MAAM,EACNsI,cAAc,EACdC,QAAQ,EACR/I,IACF,CAAC;AACH;AAQA,SAASwM,6BAA6BA,CAACpH,OAAO,EAAE0D,cAAc,EAAE9I,IAAI,EAAE;EACpE,IAAI,OAAOoF,OAAO,KAAK,QAAQ,IAAIvG,KAAK,CAACC,OAAO,CAACsG,OAAO,CAAC,EAAE,OAAO,IAAI;EACtE,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,IAAI,EAAE,OAAO,KAAK;EAEjE,MAAMiH,IAAI,GAAGhP,MAAM,CAACiP,mBAAmB,CAAClH,OAAO,CAAC;EAChD,IAAIqH,kBAAkB,GAAG,KAAK;EAC9B,IAAI7P,CAAC,GAAG,CAAC;EACT,IAAI8P,QAAQ,GAAG,CAAC,CAAC;EACjB,OAAO,EAAEA,QAAQ,GAAGL,IAAI,CAACrO,MAAM,EAAE;IAC/B,MAAMuC,GAAG,GAAG8L,IAAI,CAACK,QAAQ,CAAC;IAC1B,MAAMC,yBAAyB,GAAGpM,GAAG,KAAK,EAAE,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG;IAC9D,IAAI3D,CAAC,EAAE,KAAK,CAAC,EAAE;MACb6P,kBAAkB,GAAGE,yBAAyB;IAChD,CAAC,MAAM,IAAIF,kBAAkB,KAAKE,yBAAyB,EAAE;MAC3D,MAAM,IAAIzM,0BAA0B,CAClC,IAAAwF,oBAAa,EAACoD,cAAc,CAAC,EAC7B9I,IAAI,EACJ,sEAAsE,GACpE,sEAAsE,GACtE,uDACJ,CAAC;IACH;EACF;EAEA,OAAOyM,kBAAkB;AAC3B;AAOA,SAASG,mCAAmCA,CAAC/D,KAAK,EAAEgE,QAAQ,EAAE7M,IAAI,EAAE;EAElE,IAAIiJ,SAAMA,CAAC,CAACC,aAAa,EAAE;IACzB;EACF;EAEA,MAAMhE,SAAS,GAAG,IAAAQ,oBAAa,EAACmH,QAAQ,CAAC;EACzC,IAAInE,sBAAsB,CAAC1L,GAAG,CAACkI,SAAS,GAAG,GAAG,GAAG2D,KAAK,CAAC,EAAE;EACzDH,sBAAsB,CAACoE,GAAG,CAAC5H,SAAS,GAAG,GAAG,GAAG2D,KAAK,CAAC;EACnDI,SAAMA,CAAC,CAACG,WAAW,CACjB,qDAAqDP,KAAK,WAAW,GACnE,uDAAuD3D,SAAS,GAC9DlF,IAAI,GAAG,kBAAkB,IAAA0F,oBAAa,EAAC1F,IAAI,CAAC,EAAE,GAAG,EAAE,4DACO,EAC9D,oBAAoB,EACpB,SACF,CAAC;AACH;AAUA,SAAS+M,qBAAqBA,CAC5BjE,cAAc,EACdkD,cAAc,EACd/F,aAAa,EACbjG,IAAI,EACJ8H,UAAU,EACV;EACA,IAAI1C,OAAO,GAAGa,aAAa,CAACb,OAAO;EAEnC,IAAIoH,6BAA6B,CAACpH,OAAO,EAAE0D,cAAc,EAAE9I,IAAI,CAAC,EAAE;IAChEoF,OAAO,GAAG;MAAC,GAAG,EAAEA;IAAO,CAAC;EAC1B;EAEA,IACEgD,GAAG,CAAChL,IAAI,CAACgI,OAAO,EAAE4G,cAAc,CAAC,IACjC,CAACA,cAAc,CAAC/M,QAAQ,CAAC,GAAG,CAAC,IAC7B,CAAC+M,cAAc,CAAChN,QAAQ,CAAC,GAAG,CAAC,EAC7B;IAEA,MAAMwB,MAAM,GAAG4E,OAAO,CAAC4G,cAAc,CAAC;IACtC,MAAMI,aAAa,GAAGL,oBAAoB,CACxCjD,cAAc,EACdtI,MAAM,EACN,EAAE,EACFwL,cAAc,EACdhM,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL8H,UACF,CAAC;IACD,IAAIsE,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAKnM,SAAS,EAAE;MACzD,MAAMgL,eAAe,CAACe,cAAc,EAAElD,cAAc,EAAE9I,IAAI,CAAC;IAC7D;IAEA,OAAOoM,aAAa;EACtB;EAEA,IAAIY,SAAS,GAAG,EAAE;EAClB,IAAIC,gBAAgB,GAAG,EAAE;EACzB,MAAMZ,IAAI,GAAGhP,MAAM,CAACiP,mBAAmB,CAAClH,OAAO,CAAC;EAChD,IAAIxI,CAAC,GAAG,CAAC,CAAC;EAEV,OAAO,EAAEA,CAAC,GAAGyP,IAAI,CAACrO,MAAM,EAAE;IACxB,MAAMuC,GAAG,GAAG8L,IAAI,CAACzP,CAAC,CAAC;IACnB,MAAMsQ,YAAY,GAAG3M,GAAG,CAACb,OAAO,CAAC,GAAG,CAAC;IAErC,IACEwN,YAAY,KAAK,CAAC,CAAC,IACnBlB,cAAc,CAACrL,UAAU,CAACJ,GAAG,CAACrC,KAAK,CAAC,CAAC,EAAEgP,YAAY,CAAC,CAAC,EACrD;MAOA,IAAIlB,cAAc,CAAChN,QAAQ,CAAC,GAAG,CAAC,EAAE;QAChC4N,mCAAmC,CACjCZ,cAAc,EACdlD,cAAc,EACd9I,IACF,CAAC;MACH;MAEA,MAAMmN,cAAc,GAAG5M,GAAG,CAACrC,KAAK,CAACgP,YAAY,GAAG,CAAC,CAAC;MAElD,IACElB,cAAc,CAAChO,MAAM,IAAIuC,GAAG,CAACvC,MAAM,IACnCgO,cAAc,CAAChN,QAAQ,CAACmO,cAAc,CAAC,IACvCC,iBAAiB,CAACJ,SAAS,EAAEzM,GAAG,CAAC,KAAK,CAAC,IACvCA,GAAG,CAAC8M,WAAW,CAAC,GAAG,CAAC,KAAKH,YAAY,EACrC;QACAF,SAAS,GAAGzM,GAAG;QACf0M,gBAAgB,GAAGjB,cAAc,CAAC9N,KAAK,CACrCgP,YAAY,EACZlB,cAAc,CAAChO,MAAM,GAAGmP,cAAc,CAACnP,MACzC,CAAC;MACH;IACF;EACF;EAEA,IAAIgP,SAAS,EAAE;IAEb,MAAMxM,MAAM,GAA2B4E,OAAO,CAAC4H,SAAS,CAAE;IAC1D,MAAMZ,aAAa,GAAGL,oBAAoB,CACxCjD,cAAc,EACdtI,MAAM,EACNyM,gBAAgB,EAChBD,SAAS,EACThN,IAAI,EACJ,IAAI,EACJ,KAAK,EACLgM,cAAc,CAAChN,QAAQ,CAAC,GAAG,CAAC,EAC5B8I,UACF,CAAC;IAED,IAAIsE,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAKnM,SAAS,EAAE;MACzD,MAAMgL,eAAe,CAACe,cAAc,EAAElD,cAAc,EAAE9I,IAAI,CAAC;IAC7D;IAEA,OAAOoM,aAAa;EACtB;EAEA,MAAMnB,eAAe,CAACe,cAAc,EAAElD,cAAc,EAAE9I,IAAI,CAAC;AAC7D;AAMA,SAASoN,iBAAiBA,CAACE,CAAC,EAAEC,CAAC,EAAE;EAC/B,MAAMC,aAAa,GAAGF,CAAC,CAAC5N,OAAO,CAAC,GAAG,CAAC;EACpC,MAAM+N,aAAa,GAAGF,CAAC,CAAC7N,OAAO,CAAC,GAAG,CAAC;EACpC,MAAMgO,WAAW,GAAGF,aAAa,KAAK,CAAC,CAAC,GAAGF,CAAC,CAACtP,MAAM,GAAGwP,aAAa,GAAG,CAAC;EACvE,MAAMG,WAAW,GAAGF,aAAa,KAAK,CAAC,CAAC,GAAGF,CAAC,CAACvP,MAAM,GAAGyP,aAAa,GAAG,CAAC;EACvE,IAAIC,WAAW,GAAGC,WAAW,EAAE,OAAO,CAAC,CAAC;EACxC,IAAIA,WAAW,GAAGD,WAAW,EAAE,OAAO,CAAC;EACvC,IAAIF,aAAa,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC;EAClC,IAAIC,aAAa,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;EACnC,IAAIH,CAAC,CAACtP,MAAM,GAAGuP,CAAC,CAACvP,MAAM,EAAE,OAAO,CAAC,CAAC;EAClC,IAAIuP,CAAC,CAACvP,MAAM,GAAGsP,CAAC,CAACtP,MAAM,EAAE,OAAO,CAAC;EACjC,OAAO,CAAC;AACV;AAQA,SAAS4P,qBAAqBA,CAACnP,IAAI,EAAEuB,IAAI,EAAE8H,UAAU,EAAE;EACrD,IAAIrJ,IAAI,KAAK,GAAG,IAAIA,IAAI,CAACkC,UAAU,CAAC,IAAI,CAAC,IAAIlC,IAAI,CAACO,QAAQ,CAAC,GAAG,CAAC,EAAE;IAC/D,MAAMe,MAAM,GAAG,gDAAgD;IAC/D,MAAM,IAAIF,4BAA4B,CAACpB,IAAI,EAAEsB,MAAM,EAAE,IAAA2F,oBAAa,EAAC1F,IAAI,CAAC,CAAC;EAC3E;EAGA,IAAI8I,cAAc;EAElB,MAAM7C,aAAa,GAAGN,qBAAqB,CAAC3F,IAAI,CAAC;EAEjD,IAAIiG,aAAa,CAAChB,MAAM,EAAE;IACxB6D,cAAc,GAAG,IAAAgC,oBAAa,EAAC7E,aAAa,CAACf,SAAS,CAAC;IACvD,MAAMG,OAAO,GAAGY,aAAa,CAACZ,OAAO;IACrC,IAAIA,OAAO,EAAE;MACX,IAAI+C,GAAG,CAAChL,IAAI,CAACiI,OAAO,EAAE5G,IAAI,CAAC,IAAI,CAACA,IAAI,CAACQ,QAAQ,CAAC,GAAG,CAAC,EAAE;QAClD,MAAMmN,aAAa,GAAGL,oBAAoB,CACxCjD,cAAc,EACdzD,OAAO,CAAC5G,IAAI,CAAC,EACb,EAAE,EACFA,IAAI,EACJuB,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,KAAK,EACL8H,UACF,CAAC;QACD,IAAIsE,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAKnM,SAAS,EAAE;UACzD,OAAOmM,aAAa;QACtB;MACF,CAAC,MAAM;QACL,IAAIY,SAAS,GAAG,EAAE;QAClB,IAAIC,gBAAgB,GAAG,EAAE;QACzB,MAAMZ,IAAI,GAAGhP,MAAM,CAACiP,mBAAmB,CAACjH,OAAO,CAAC;QAChD,IAAIzI,CAAC,GAAG,CAAC,CAAC;QAEV,OAAO,EAAEA,CAAC,GAAGyP,IAAI,CAACrO,MAAM,EAAE;UACxB,MAAMuC,GAAG,GAAG8L,IAAI,CAACzP,CAAC,CAAC;UACnB,MAAMsQ,YAAY,GAAG3M,GAAG,CAACb,OAAO,CAAC,GAAG,CAAC;UAErC,IAAIwN,YAAY,KAAK,CAAC,CAAC,IAAIzO,IAAI,CAACkC,UAAU,CAACJ,GAAG,CAACrC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;YAC5D,MAAMiP,cAAc,GAAG5M,GAAG,CAACrC,KAAK,CAACgP,YAAY,GAAG,CAAC,CAAC;YAClD,IACEzO,IAAI,CAACT,MAAM,IAAIuC,GAAG,CAACvC,MAAM,IACzBS,IAAI,CAACO,QAAQ,CAACmO,cAAc,CAAC,IAC7BC,iBAAiB,CAACJ,SAAS,EAAEzM,GAAG,CAAC,KAAK,CAAC,IACvCA,GAAG,CAAC8M,WAAW,CAAC,GAAG,CAAC,KAAKH,YAAY,EACrC;cACAF,SAAS,GAAGzM,GAAG;cACf0M,gBAAgB,GAAGxO,IAAI,CAACP,KAAK,CAC3BgP,YAAY,EACZzO,IAAI,CAACT,MAAM,GAAGmP,cAAc,CAACnP,MAC/B,CAAC;YACH;UACF;QACF;QAEA,IAAIgP,SAAS,EAAE;UACb,MAAMxM,MAAM,GAAG6E,OAAO,CAAC2H,SAAS,CAAC;UACjC,MAAMZ,aAAa,GAAGL,oBAAoB,CACxCjD,cAAc,EACdtI,MAAM,EACNyM,gBAAgB,EAChBD,SAAS,EACThN,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,KAAK,EACL8H,UACF,CAAC;UAED,IAAIsE,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAKnM,SAAS,EAAE;YACzD,OAAOmM,aAAa;UACtB;QACF;MACF;IACF;EACF;EAEA,MAAMpB,gBAAgB,CAACvM,IAAI,EAAEqK,cAAc,EAAE9I,IAAI,CAAC;AACpD;AAMA,SAAS6N,gBAAgBA,CAAC3M,SAAS,EAAElB,IAAI,EAAE;EACzC,IAAI8N,cAAc,GAAG5M,SAAS,CAACxB,OAAO,CAAC,GAAG,CAAC;EAC3C,IAAIqO,gBAAgB,GAAG,IAAI;EAC3B,IAAIC,QAAQ,GAAG,KAAK;EACpB,IAAI9M,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACxB8M,QAAQ,GAAG,IAAI;IACf,IAAIF,cAAc,KAAK,CAAC,CAAC,IAAI5M,SAAS,CAAClD,MAAM,KAAK,CAAC,EAAE;MACnD+P,gBAAgB,GAAG,KAAK;IAC1B,CAAC,MAAM;MACLD,cAAc,GAAG5M,SAAS,CAACxB,OAAO,CAAC,GAAG,EAAEoO,cAAc,GAAG,CAAC,CAAC;IAC7D;EACF;EAEA,MAAMG,WAAW,GACfH,cAAc,KAAK,CAAC,CAAC,GAAG5M,SAAS,GAAGA,SAAS,CAAChD,KAAK,CAAC,CAAC,EAAE4P,cAAc,CAAC;EAIxE,IAAIvF,uBAAuB,CAAC/I,IAAI,CAACyO,WAAW,CAAC,KAAK,IAAI,EAAE;IACtDF,gBAAgB,GAAG,KAAK;EAC1B;EAEA,IAAI,CAACA,gBAAgB,EAAE;IACrB,MAAM,IAAIlO,4BAA4B,CACpCqB,SAAS,EACT,6BAA6B,EAC7B,IAAAwE,oBAAa,EAAC1F,IAAI,CACpB,CAAC;EACH;EAEA,MAAMgM,cAAc,GAClB,GAAG,IAAI8B,cAAc,KAAK,CAAC,CAAC,GAAG,EAAE,GAAG5M,SAAS,CAAChD,KAAK,CAAC4P,cAAc,CAAC,CAAC;EAEtE,OAAO;IAACG,WAAW;IAAEjC,cAAc;IAAEgC;EAAQ,CAAC;AAChD;AAQA,SAAStC,cAAcA,CAACxK,SAAS,EAAElB,IAAI,EAAE8H,UAAU,EAAE;EACnD,IAAIoG,wBAAc,CAACjP,QAAQ,CAACiC,SAAS,CAAC,EAAE;IACtC,OAAO,KAAIuI,UAAK,EAAC,OAAO,GAAGvI,SAAS,CAAC;EACvC;EAEA,MAAM;IAAC+M,WAAW;IAAEjC,cAAc;IAAEgC;EAAQ,CAAC,GAAGH,gBAAgB,CAC9D3M,SAAS,EACTlB,IACF,CAAC;EAGD,MAAMiG,aAAa,GAAGN,qBAAqB,CAAC3F,IAAI,CAAC;EAIjD,IAAIiG,aAAa,CAAChB,MAAM,EAAE;IACxB,MAAM6D,cAAc,GAAG,IAAAgC,oBAAa,EAAC7E,aAAa,CAACf,SAAS,CAAC;IAC7D,IACEe,aAAa,CAACxH,IAAI,KAAKwP,WAAW,IAClChI,aAAa,CAACb,OAAO,KAAKnF,SAAS,IACnCgG,aAAa,CAACb,OAAO,KAAK,IAAI,EAC9B;MACA,OAAO2H,qBAAqB,CAC1BjE,cAAc,EACdkD,cAAc,EACd/F,aAAa,EACbjG,IAAI,EACJ8H,UACF,CAAC;IACH;EACF;EAEA,IAAIgB,cAAc,GAAG,KAAIW,UAAK,EAC5B,iBAAiB,GAAGwE,WAAW,GAAG,eAAe,EACjDjO,IACF,CAAC;EACD,IAAImO,eAAe,GAAG,IAAAzI,oBAAa,EAACoD,cAAc,CAAC;EAEnD,IAAIsF,QAAQ;EACZ,GAAG;IACD,MAAMC,IAAI,GAAGzE,WAAW,CAACuE,eAAe,CAACjQ,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACvD,IAAI,CAACmQ,IAAI,IAAI,CAACA,IAAI,CAAC5D,WAAW,CAAC,CAAC,EAAE;MAChC2D,QAAQ,GAAGD,eAAe;MAC1BrF,cAAc,GAAG,KAAIW,UAAK,EACxB,CAACuE,QAAQ,GAAG,2BAA2B,GAAG,wBAAwB,IAChEC,WAAW,GACX,eAAe,EACjBnF,cACF,CAAC;MACDqF,eAAe,GAAG,IAAAzI,oBAAa,EAACoD,cAAc,CAAC;MAC/C;IACF;IAGA,MAAM7C,aAAa,GAAGzB,IAAI,CAAC2J,eAAe,EAAE;MAACnO,IAAI;MAAEkB;IAAS,CAAC,CAAC;IAC9D,IAAI+E,aAAa,CAACb,OAAO,KAAKnF,SAAS,IAAIgG,aAAa,CAACb,OAAO,KAAK,IAAI,EAAE;MACzE,OAAO2H,qBAAqB,CAC1BjE,cAAc,EACdkD,cAAc,EACd/F,aAAa,EACbjG,IAAI,EACJ8H,UACF,CAAC;IACH;IAEA,IAAIkE,cAAc,KAAK,GAAG,EAAE;MAC1B,OAAO7B,iBAAiB,CAACrB,cAAc,EAAE7C,aAAa,EAAEjG,IAAI,CAAC;IAC/D;IAEA,OAAO,KAAIyJ,UAAK,EAACuC,cAAc,EAAElD,cAAc,CAAC;EAElD,CAAC,QAAQqF,eAAe,CAACnQ,MAAM,KAAKoQ,QAAQ,CAACpQ,MAAM;EAEnD,MAAM,IAAI8C,oBAAoB,CAACmN,WAAW,EAAE,IAAAvI,oBAAa,EAAC1F,IAAI,CAAC,EAAE,KAAK,CAAC;AACzE;AAMA,SAASsO,mBAAmBA,CAACpN,SAAS,EAAE;EACtC,IAAIA,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACxB,IAAIA,SAAS,CAAClD,MAAM,KAAK,CAAC,IAAIkD,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,OAAO,IAAI;IAC/D,IACEA,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,KACnBA,SAAS,CAAClD,MAAM,KAAK,CAAC,IAAIkD,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,EAChD;MACA,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd;AAMA,SAASqN,uCAAuCA,CAACrN,SAAS,EAAE;EAC1D,IAAIA,SAAS,KAAK,EAAE,EAAE,OAAO,KAAK;EAClC,IAAIA,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,OAAO,IAAI;EACrC,OAAOoN,mBAAmB,CAACpN,SAAS,CAAC;AACvC;AAiBA,SAASsN,aAAaA,CAACtN,SAAS,EAAElB,IAAI,EAAE8H,UAAU,EAAEyC,gBAAgB,EAAE;EAGpE,MAAMhD,QAAQ,GAAGvH,IAAI,CAACuH,QAAQ;EAC9B,MAAMkH,MAAM,GAAGlH,QAAQ,KAAK,OAAO;EACnC,MAAMmH,QAAQ,GAAGD,MAAM,IAAIlH,QAAQ,KAAK,OAAO,IAAIA,QAAQ,KAAK,QAAQ;EAIxE,IAAI3B,QAAQ;EAEZ,IAAI2I,uCAAuC,CAACrN,SAAS,CAAC,EAAE;IACtD,IAAI;MACF0E,QAAQ,GAAG,KAAI6D,UAAK,EAACvI,SAAS,EAAElB,IAAI,CAAC;IACvC,CAAC,CAAC,OAAOwF,MAAM,EAAE;MACf,MAAMnD,KAAK,GAAG,IAAIf,+BAA+B,CAACJ,SAAS,EAAElB,IAAI,CAAC;MAClEqC,KAAK,CAACoD,KAAK,GAAGD,MAAM;MACpB,MAAMnD,KAAK;IACb;EACF,CAAC,MAAM,IAAIkF,QAAQ,KAAK,OAAO,IAAIrG,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACvD0E,QAAQ,GAAGgI,qBAAqB,CAAC1M,SAAS,EAAElB,IAAI,EAAE8H,UAAU,CAAC;EAC/D,CAAC,MAAM;IACL,IAAI;MACFlC,QAAQ,GAAG,KAAI6D,UAAK,EAACvI,SAAS,CAAC;IACjC,CAAC,CAAC,OAAOsE,MAAM,EAAE;MAEf,IAAIkJ,QAAQ,IAAI,CAACR,wBAAc,CAACjP,QAAQ,CAACiC,SAAS,CAAC,EAAE;QACnD,MAAMmB,KAAK,GAAG,IAAIf,+BAA+B,CAACJ,SAAS,EAAElB,IAAI,CAAC;QAClEqC,KAAK,CAACoD,KAAK,GAAGD,MAAM;QACpB,MAAMnD,KAAK;MACb;MAEAuD,QAAQ,GAAG8F,cAAc,CAACxK,SAAS,EAAElB,IAAI,EAAE8H,UAAU,CAAC;IACxD;EACF;EAEAlJ,QAAKA,CAAC,CAACgH,QAAQ,KAAK3F,SAAS,EAAE,wBAAwB,CAAC;EAExD,IAAI2F,QAAQ,CAAC2B,QAAQ,KAAK,OAAO,EAAE;IACjC,OAAO3B,QAAQ;EACjB;EAEA,OAAO0E,kBAAkB,CAAC1E,QAAQ,EAAE5F,IAAI,EAAEuK,gBAAgB,CAAC;AAC7D;AAOA,SAASoE,uBAAuBA,CAACzN,SAAS,EAAEoE,MAAM,EAAEsJ,eAAe,EAAE;EACnE,IAAIA,eAAe,EAAE;IAEnB,MAAMC,cAAc,GAAGD,eAAe,CAACrH,QAAQ;IAE/C,IAAIsH,cAAc,KAAK,OAAO,IAAIA,cAAc,KAAK,QAAQ,EAAE;MAC7D,IAAIN,uCAAuC,CAACrN,SAAS,CAAC,EAAE;QAEtD,MAAM4N,cAAc,GAAGxJ,MAAM,oBAANA,MAAM,CAAEiC,QAAQ;QAIvC,IACEuH,cAAc,IACdA,cAAc,KAAK,QAAQ,IAC3BA,cAAc,KAAK,OAAO,EAC1B;UACA,MAAM,IAAI9N,6BAA6B,CACrCE,SAAS,EACT0N,eAAe,EACf,qDACF,CAAC;QACH;QAEA,OAAO;UAACxI,GAAG,EAAE,CAAAd,MAAM,oBAANA,MAAM,CAAEiE,IAAI,KAAI;QAAE,CAAC;MAClC;MAEA,IAAI2E,wBAAc,CAACjP,QAAQ,CAACiC,SAAS,CAAC,EAAE;QACtC,MAAM,IAAIF,6BAA6B,CACrCE,SAAS,EACT0N,eAAe,EACf,qDACF,CAAC;MACH;MAEA,MAAM,IAAI5N,6BAA6B,CACrCE,SAAS,EACT0N,eAAe,EACf,sDACF,CAAC;IACH;EACF;AACF;AAkBA,SAASrD,KAAKA,CAAC5H,IAAI,EAAE;EACnB,OAAOoL,OAAO,CACZpL,IAAI,IACF,OAAOA,IAAI,KAAK,QAAQ,IACxB,MAAM,IAAIA,IAAI,IACd,OAAOA,IAAI,CAAC4F,IAAI,KAAK,QAAQ,IAC7B,UAAU,IAAI5F,IAAI,IAClB,OAAOA,IAAI,CAAC4D,QAAQ,KAAK,QAAQ,IACjC5D,IAAI,CAAC4F,IAAI,IACT5F,IAAI,CAAC4D,QACT,CAAC;AACH;AAQA,SAASyH,uBAAuBA,CAAC1F,SAAS,EAAE;EAC1C,IAAIA,SAAS,KAAKrJ,SAAS,EAAE;IAC3B;EACF;EAEA,IAAI,OAAOqJ,SAAS,KAAK,QAAQ,IAAI,CAACiC,KAAK,CAACjC,SAAS,CAAC,EAAE;IACtD,MAAM,IAAI1L,KAAK,CAACW,oBAAoB,CAClC,WAAW,EACX,CAAC,QAAQ,EAAE,KAAK,CAAC,EACjB+K,SACF,CAAC;EACH;AACF;AAOA,SAAS2F,cAAcA,CAAC/N,SAAS,EAAEoG,OAAO,GAAG,CAAC,CAAC,EAAE;EAC/C,MAAM;IAACgC;EAAS,CAAC,GAAGhC,OAAO;EAC3B1I,QAAKA,CAAC,CAAC0K,SAAS,KAAKrJ,SAAS,EAAE,oCAAoC,CAAC;EACrE+O,uBAAuB,CAAC1F,SAAS,CAAC;EAGlC,IAAIsF,eAAe;EACnB,IAAItF,SAAS,EAAE;IACb,IAAI;MACFsF,eAAe,GAAG,KAAInF,UAAK,EAACH,SAAS,CAAC;IACxC,CAAC,CAAC,OAAA4F,QAAA,EAAM,CAER;EACF;EAGA,IAAI5J,MAAM;EAEV,IAAIiC,QAAQ;EAEZ,IAAI;IACFjC,MAAM,GAAGiJ,uCAAuC,CAACrN,SAAS,CAAC,GACvD,KAAIuI,UAAK,EAACvI,SAAS,EAAE0N,eAAe,CAAC,GACrC,KAAInF,UAAK,EAACvI,SAAS,CAAC;IAGxBqG,QAAQ,GAAGjC,MAAM,CAACiC,QAAQ;IAE1B,IAAIA,QAAQ,KAAK,OAAO,EAAE;MACxB,OAAO;QAACnB,GAAG,EAAEd,MAAM,CAACiE,IAAI;QAAEtF,MAAM,EAAE;MAAI,CAAC;IACzC;EACF,CAAC,CAAC,OAAAkL,QAAA,EAAM,CAER;EAKA,MAAMC,WAAW,GAAGT,uBAAuB,CACzCzN,SAAS,EACToE,MAAM,EACNsJ,eACF,CAAC;EAED,IAAIQ,WAAW,EAAE,OAAOA,WAAW;EAGnC,IAAI7H,QAAQ,KAAKtH,SAAS,IAAIqF,MAAM,EAAE;IACpCiC,QAAQ,GAAGjC,MAAM,CAACiC,QAAQ;EAC5B;EAEA,IAAIA,QAAQ,KAAK,OAAO,EAAE;IACxB,OAAO;MAACnB,GAAG,EAAElF;IAAS,CAAC;EACzB;EAGA,IAAIoE,MAAM,IAAIA,MAAM,CAACiC,QAAQ,KAAK,OAAO,EAAE,OAAO;IAACnB,GAAG,EAAElF;EAAS,CAAC;EAElE,MAAM4G,UAAU,GAAGD,gBAAgB,CAACP,OAAO,CAACQ,UAAU,CAAC;EAEvD,MAAM1B,GAAG,GAAGoI,aAAa,CAACtN,SAAS,EAAE,KAAIuI,UAAK,EAACH,SAAS,CAAC,EAAExB,UAAU,EAAE,KAAK,CAAC;EAE7E,OAAO;IAGL1B,GAAG,EAAEA,GAAG,CAACmD,IAAI;IACbtF,MAAM,EAAEoD,6BAA6B,CAACjB,GAAG,EAAE;MAACkD;IAAS,CAAC;EACxD,CAAC;AACH;AAsBA,SAASK,OAAOA,CAACzI,SAAS,EAAEmO,MAAM,EAAE;EAClC,IAAI,CAACA,MAAM,EAAE;IACX,MAAM,IAAIjP,KAAK,CACb,kEACF,CAAC;EACH;EAEA,IAAI;IACF,OAAO6O,cAAc,CAAC/N,SAAS,EAAE;MAACoI,SAAS,EAAE+F;IAAM,CAAC,CAAC,CAACjJ,GAAG;EAC3D,CAAC,CAAC,OAAO/D,KAAK,EAAE;IAEd,MAAM0C,SAAS,GAAkC1C,KAAM;IAEvD,IACE,CAAC0C,SAAS,CAAClC,IAAI,KAAK,4BAA4B,IAC9CkC,SAAS,CAAClC,IAAI,KAAK,sBAAsB,KAC3C,OAAOkC,SAAS,CAACqB,GAAG,KAAK,QAAQ,EACjC;MACA,OAAOrB,SAAS,CAACqB,GAAG;IACtB;IAEA,MAAM/D,KAAK;EACb;AACF;AAAC", "ignoreList": []}