var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
import { a as axios } from "./index-DEw2ppt0.js";
var define_process_env_default = {};
const API_BASE_URL = define_process_env_default.REACT_APP_API_BASE_URL || "http://localhost:8000";
const API_TIMEOUT = 3e4;
const createApiClient = () => {
  const client = axios.create({
    baseURL: API_BASE_URL,
    timeout: API_TIMEOUT,
    headers: {
      "Content-Type": "application/json"
    }
  });
  client.interceptors.request.use(
    (config) => {
      var _a;
      config.metadata = { startTime: /* @__PURE__ */ new Date() };
      console.log(`🚀 API请求: ${(_a = config.method) == null ? void 0 : _a.toUpperCase()} ${config.url}`);
      return config;
    },
    (error) => {
      console.error("❌ API请求错误:", error);
      return Promise.reject(error);
    }
  );
  client.interceptors.response.use(
    (response) => {
      var _a, _b, _c;
      const duration = (/* @__PURE__ */ new Date()).getTime() - ((_b = (_a = response.config.metadata) == null ? void 0 : _a.startTime) == null ? void 0 : _b.getTime());
      console.log(`✅ API响应: ${(_c = response.config.method) == null ? void 0 : _c.toUpperCase()} ${response.config.url} (${duration}ms)`);
      return response;
    },
    (error) => {
      var _a, _b, _c, _d, _e, _f, _g, _h, _i;
      const duration = ((_b = (_a = error.config) == null ? void 0 : _a.metadata) == null ? void 0 : _b.startTime) ? (/* @__PURE__ */ new Date()).getTime() - error.config.metadata.startTime.getTime() : 0;
      console.error(`❌ API错误: ${(_d = (_c = error.config) == null ? void 0 : _c.method) == null ? void 0 : _d.toUpperCase()} ${(_e = error.config) == null ? void 0 : _e.url} (${duration}ms)`, error);
      const apiError = {
        message: ((_g = (_f = error.response) == null ? void 0 : _f.data) == null ? void 0 : _g.detail) || error.message || "请求失败",
        status: ((_h = error.response) == null ? void 0 : _h.status) || 0,
        details: (_i = error.response) == null ? void 0 : _i.data
      };
      return Promise.reject(apiError);
    }
  );
  return client;
};
const apiClient = createApiClient();
class ApiClient {
  constructor() {
    __publicField(this, "client");
    this.client = apiClient;
  }
  // GET请求
  get(url, config) {
    return __async(this, null, function* () {
      const response = yield this.client.get(url, config);
      console.log("🔍 API Client GET - URL:", url);
      console.log("🔍 API Client GET - 原始响应:", response);
      console.log("🔍 API Client GET - response.data:", response.data);
      return response.data;
    });
  }
  // POST请求
  post(url, data, config) {
    return __async(this, null, function* () {
      const response = yield this.client.post(url, data, config);
      return response.data;
    });
  }
  // PUT请求
  put(url, data, config) {
    return __async(this, null, function* () {
      const response = yield this.client.put(url, data, config);
      return response.data;
    });
  }
  // DELETE请求
  delete(url, config) {
    return __async(this, null, function* () {
      const response = yield this.client.delete(url, config);
      return response.data;
    });
  }
  // PATCH请求
  patch(url, data, config) {
    return __async(this, null, function* () {
      const response = yield this.client.patch(url, data, config);
      return response.data;
    });
  }
}
const api = new ApiClient();
const buildQueryParams = (params) => {
  const searchParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== void 0 && value !== null && value !== "") {
      if (Array.isArray(value)) {
        value.forEach((item) => searchParams.append(key, String(item)));
      } else {
        searchParams.append(key, String(value));
      }
    }
  });
  return searchParams.toString();
};
const uploadFile = (url, file, onProgress) => __async(null, null, function* () {
  const formData = new FormData();
  formData.append("file", file);
  return apiClient.post(url, formData, {
    headers: {
      "Content-Type": "multipart/form-data"
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round(progressEvent.loaded * 100 / progressEvent.total);
        onProgress(progress);
      }
    }
  });
});
const handleApiError = (error) => {
  if (error.status === 404) {
    return "请求的资源不存在";
  } else if (error.status === 401) {
    return "未授权访问，请重新登录";
  } else if (error.status === 403) {
    return "权限不足，无法访问";
  } else if (error.status === 500) {
    return "服务器内部错误，请稍后重试";
  } else if (error.status === 0) {
    return "网络连接失败，请检查网络";
  } else {
    return error.message || "请求失败";
  }
};
export {
  api as a,
  buildQueryParams as b,
  handleApiError as h,
  uploadFile as u
};
