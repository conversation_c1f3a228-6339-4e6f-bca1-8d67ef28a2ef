import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, Plus } from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { ImageWithFallback } from './figma/ImageWithFallback';

interface Library {
  id: string;
  name: string;
  fileCount: number;
  createdAt: string;
  modifiedAt: string;
  coverImage: string;
}

interface LibraryManagementProps {
  currentLibrary: string;
  onLibraryChange: (library: string) => void;
  onClose: () => void;
}

export const LibraryManagement: React.FC<LibraryManagementProps> = ({
  currentLibrary,
  onLibraryChange,
  onClose,
}) => {
  const [isCreating, setIsCreating] = useState(false);
  const [newLibraryName, setNewLibraryName] = useState('');
  const [selectedLibrary, setSelectedLibrary] = useState<Library | null>(null);
  const [scrollIndex, setScrollIndex] = useState(0);

  const libraries: Library[] = [
    {
      id: '1',
      name: '默认档案库',
      fileCount: 234,
      createdAt: '2024-01-15',
      modifiedAt: '2024-03-20',
      coverImage: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop'
    },
    {
      id: '2',
      name: '工作项目',
      fileCount: 156,
      createdAt: '2024-02-01',
      modifiedAt: '2024-03-18',
      coverImage: 'https://images.unsplash.com/photo-1497436072909-f5e4be1453c1?w=300&h=200&fit=crop'
    },
    {
      id: '3',
      name: '个人收藏',
      fileCount: 89,
      createdAt: '2024-02-15',
      modifiedAt: '2024-03-15',
      coverImage: 'https://images.unsplash.com/photo-1519452575417-564c1401ecc0?w=300&h=200&fit=crop'
    },
    {
      id: '4',
      name: '旅行摄影',
      fileCount: 312,
      createdAt: '2024-01-20',
      modifiedAt: '2024-03-10',
      coverImage: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop'
    },
    {
      id: '5',
      name: '街头摄影',
      fileCount: 78,
      createdAt: '2024-03-01',
      modifiedAt: '2024-03-08',
      coverImage: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=300&h=200&fit=crop'
    },
  ];

  const visibleLibraries = libraries.slice(scrollIndex, scrollIndex + 5);

  const handleLibrarySelect = (library: Library) => {
    setSelectedLibrary(library);
  };

  const handleLibraryOpen = (library: Library) => {
    onLibraryChange(library.name);
    onClose();
  };

  const handleCreateLibrary = () => {
    if (newLibraryName.trim()) {
      // 这里实际应该调用API创建新档案库
      console.log('创建档案库:', newLibraryName);
      setNewLibraryName('');
      setIsCreating(false);
    }
  };

  const scrollLeft = () => {
    setScrollIndex(Math.max(0, scrollIndex - 1));
  };

  const scrollRight = () => {
    setScrollIndex(Math.min(libraries.length - 5, scrollIndex + 1));
  };

  if (isCreating) {
    return (
      <div 
        className="p-8 h-[600px] flex flex-col items-center justify-center"
        style={{ background: 'var(--mizzy-gallery)', color: 'var(--mizzy-content)' }}
      >
        {/* 应用Logo */}
        <div className="mb-8">
          <h1 
            className="text-4xl font-bold text-center"
            style={{ color: 'var(--mizzy-highlight)' }}
          >
            迷星
          </h1>
        </div>

        {/* 新建档案库表单 */}
        <div className="w-full max-w-md space-y-6">
          <div>
            <Input
              placeholder="请输入档案库的名称"
              value={newLibraryName}
              onChange={(e) => setNewLibraryName(e.target.value)}
              className="w-full h-12 text-center"
              style={{ 
                background: newLibraryName ? 'var(--mizzy-content)' : 'var(--mizzy-input)',
                color: newLibraryName ? 'var(--mizzy-nav)' : 'var(--mizzy-icon)',
                borderColor: 'var(--mizzy-border)'
              }}
            />
          </div>

          <div className="flex justify-center gap-4">
            <Button
              onClick={() => setIsCreating(false)}
              variant="outline"
              className="px-8"
              style={{ 
                borderColor: 'var(--mizzy-border)',
                color: 'var(--mizzy-content)'
              }}
            >
              返回
            </Button>
            <Button
              onClick={handleCreateLibrary}
              className="px-8"
              style={{ 
                background: 'var(--mizzy-highlight)',
                color: 'white'
              }}
            >
              新建
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div 
      className="p-6 h-[600px]"
      style={{ background: 'var(--mizzy-gallery)', color: 'var(--mizzy-content)' }}
    >
      <div className="flex h-full">
        {/* 左侧档案库列表 */}
        <div className="flex-1 pr-6">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-lg" style={{ color: 'var(--mizzy-title)' }}>
              档案库列表
            </h2>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={scrollLeft}
                disabled={scrollIndex === 0}
                style={{ color: 'var(--mizzy-icon)' }}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={scrollRight}
                disabled={scrollIndex >= libraries.length - 5}
                style={{ color: 'var(--mizzy-icon)' }}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* 档案库卡片 */}
          <div className="grid grid-cols-5 gap-4 mb-6">
            {visibleLibraries.map((library) => (
              <div
                key={library.id}
                className={`
                  cursor-pointer rounded-lg overflow-hidden border-2 transition-all
                  ${selectedLibrary?.id === library.id ? 'ring-2' : 'hover:border-opacity-50'}
                `}
                style={{ 
                  borderColor: selectedLibrary?.id === library.id ? 'var(--mizzy-highlight)' : 'var(--mizzy-border)',
                  ringColor: 'var(--mizzy-highlight)'
                }}
                onClick={() => handleLibrarySelect(library)}
                onDoubleClick={() => handleLibraryOpen(library)}
              >
                <div className="aspect-[3/2] relative">
                  <ImageWithFallback
                    src={library.coverImage}
                    alt={library.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="p-2">
                  <h3 className="text-sm font-medium truncate" style={{ color: 'var(--mizzy-content)' }}>
                    {library.name}
                  </h3>
                  <p className="text-xs" style={{ color: 'var(--mizzy-icon)' }}>
                    {library.fileCount} 个文件
                  </p>
                </div>
              </div>
            ))}

            {/* 新建档案库按钮 */}
            <div
              className="aspect-[3/2] border-2 border-dashed rounded-lg flex items-center justify-center cursor-pointer hover:border-opacity-50 transition-all"
              style={{ borderColor: 'var(--mizzy-border)' }}
              onClick={() => setIsCreating(true)}
            >
              <div className="text-center">
                <Plus className="h-8 w-8 mx-auto mb-2" style={{ color: 'var(--mizzy-icon)' }} />
                <p className="text-xs" style={{ color: 'var(--mizzy-icon)' }}>
                  新建档案库
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧档案库信息 */}
        <div 
          className="w-80 pl-6 border-l"
          style={{ borderColor: 'var(--mizzy-border)' }}
        >
          {selectedLibrary ? (
            <div>
              <h3 className="text-lg mb-4" style={{ color: 'var(--mizzy-title)' }}>
                档案库信息
              </h3>
              
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2" style={{ color: 'var(--mizzy-content)' }}>
                    {selectedLibrary.name}
                  </h4>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span style={{ color: 'var(--mizzy-icon)' }}>文件数量:</span>
                    <span style={{ color: 'var(--mizzy-content)' }}>{selectedLibrary.fileCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span style={{ color: 'var(--mizzy-icon)' }}>创建时间:</span>
                    <span style={{ color: 'var(--mizzy-content)' }}>{selectedLibrary.createdAt}</span>
                  </div>
                  <div className="flex justify-between">
                    <span style={{ color: 'var(--mizzy-icon)' }}>修改时间:</span>
                    <span style={{ color: 'var(--mizzy-content)' }}>{selectedLibrary.modifiedAt}</span>
                  </div>
                </div>

                <div className="pt-4">
                  <Button
                    onClick={() => handleLibraryOpen(selectedLibrary)}
                    className="w-full"
                    style={{ 
                      background: 'var(--mizzy-highlight)',
                      color: 'white'
                    }}
                  >
                    打开档案库
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center pt-12">
              <p style={{ color: 'var(--mizzy-icon)' }}>
                选择一个档案库查看详细信息
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};