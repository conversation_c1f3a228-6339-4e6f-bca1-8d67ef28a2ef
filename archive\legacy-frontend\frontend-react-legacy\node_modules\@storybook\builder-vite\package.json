{"name": "@storybook/builder-vite", "version": "9.0.18", "description": "A plugin to run and build Storybooks with Vite", "homepage": "https://github.com/storybookjs/storybook/tree/next/code/builders/builder-vite/#readme", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/builders/builder-vite"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "author": "<PERSON><PERSON><PERSON>", "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./input/iframe.html": "./input/iframe.html", "./package.json": "./package.json"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist/**/*", "input/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "jiti ../../../scripts/prepare/check.ts", "prep": "jiti ../../../scripts/prepare/bundle.ts"}, "dependencies": {"@storybook/csf-plugin": "9.0.18", "ts-dedent": "^2.0.0"}, "devDependencies": {"@types/find-cache-dir": "^3.2.1", "@types/node": "^22.0.0", "es-module-lexer": "^1.5.0", "find-cache-dir": "^3.0.0", "glob": "^10.0.0", "knitwork": "^1.1.0", "magic-string": "^0.30.0", "pathe": "^1.1.2", "slash": "^5.0.0", "vite": "^6.2.5"}, "peerDependencies": {"storybook": "^9.0.18", "vite": "^5.0.0 || ^6.0.0 || ^7.0.0"}, "publishConfig": {"access": "public"}, "bundler": {"entries": ["./src/index.ts"], "platform": "node"}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae16"}