{"version": 3, "sources": ["../../src/infiniteQueryOptions.ts"], "sourcesContent": ["import type {\n  DataTag,\n  DefaultError,\n  InfiniteData,\n  InitialDataFunction,\n  NonUndefinedGuard,\n  Omit<PERSON>eyof,\n  Query<PERSON>ey,\n  SkipToken,\n} from '@tanstack/query-core'\nimport type { UseInfiniteQueryOptions } from './types'\n\nexport type UndefinedInitialDataInfiniteOptions<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n> = UseInfiniteQueryOptions<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey,\n  TPageParam\n> & {\n  initialData?:\n    | undefined\n    | NonUndefinedGuard<InfiniteData<TQueryFnData, TPageParam>>\n    | InitialDataFunction<\n        NonUndefinedGuard<InfiniteData<TQueryFnData, TPageParam>>\n      >\n}\n\nexport type UnusedSkipTokenInfiniteOptions<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQ<PERSON>y<PERSON>ey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n> = OmitKeyof<\n  UseInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey, TPageParam>,\n  'queryFn'\n> & {\n  queryFn?: Exclude<\n    UseInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >['queryFn'],\n    SkipToken | undefined\n  >\n}\n\nexport type DefinedInitialDataInfiniteOptions<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n> = UseInfiniteQueryOptions<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey,\n  TPageParam\n> & {\n  initialData:\n    | NonUndefinedGuard<InfiniteData<TQueryFnData, TPageParam>>\n    | (() => NonUndefinedGuard<InfiniteData<TQueryFnData, TPageParam>>)\n    | undefined\n}\n\nexport function infiniteQueryOptions<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: DefinedInitialDataInfiniteOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n): DefinedInitialDataInfiniteOptions<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey,\n  TPageParam\n> & {\n  queryKey: DataTag<TQueryKey, InfiniteData<TQueryFnData>, TError>\n}\n\nexport function infiniteQueryOptions<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: UnusedSkipTokenInfiniteOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n): UnusedSkipTokenInfiniteOptions<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey,\n  TPageParam\n> & {\n  queryKey: DataTag<TQueryKey, InfiniteData<TQueryFnData>, TError>\n}\n\nexport function infiniteQueryOptions<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: UndefinedInitialDataInfiniteOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n): UndefinedInitialDataInfiniteOptions<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey,\n  TPageParam\n> & {\n  queryKey: DataTag<TQueryKey, InfiniteData<TQueryFnData>, TError>\n}\n\nexport function infiniteQueryOptions(options: unknown) {\n  return options\n}\n"], "mappings": ";AAkJO,SAAS,qBAAqB,SAAkB;AACrD,SAAO;AACT;", "names": []}