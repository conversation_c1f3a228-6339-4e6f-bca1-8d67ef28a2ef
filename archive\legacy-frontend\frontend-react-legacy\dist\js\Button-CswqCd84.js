var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __objRest = (source, exclude) => {
  var target = {};
  for (var prop in source)
    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)
      target[prop] = source[prop];
  if (source != null && __getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(source)) {
      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))
        target[prop] = source[prop];
    }
  return target;
};
import { j as jsxRuntimeExports } from "./state-management-CeNIv-64.js";
import "./router-DbSvV1fW.js";
import { c as cn } from "./index-BaeIiao7.js";
const LoadingSpinner = ({ size }) => {
  const sizeClasses = {
    xs: "w-3 h-3",
    sm: "w-3 h-3",
    md: "w-4 h-4",
    lg: "w-5 h-5",
    xl: "w-6 h-6"
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(
    "svg",
    {
      className: cn("animate-spin", sizeClasses[size]),
      fill: "none",
      viewBox: "0 0 24 24",
      children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "circle",
          {
            className: "opacity-25",
            cx: "12",
            cy: "12",
            r: "10",
            stroke: "currentColor",
            strokeWidth: "4"
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "path",
          {
            className: "opacity-75",
            fill: "currentColor",
            d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          }
        )
      ]
    }
  );
};
const Button = (_a) => {
  var _b = _a, {
    variant = "secondary",
    size = "md",
    loading = false,
    disabled = false,
    icon,
    iconPosition = "left",
    fullWidth = false,
    children,
    className,
    onClick
  } = _b, props = __objRest(_b, [
    "variant",
    "size",
    "loading",
    "disabled",
    "icon",
    "iconPosition",
    "fullWidth",
    "children",
    "className",
    "onClick"
  ]);
  const variantClasses = {
    primary: "bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500",
    secondary: "bg-secondary-bg text-primary hover:bg-secondary-bg-hover focus:ring-gray-500",
    success: "bg-green-600 text-white hover:bg-green-700 focus:ring-green-500",
    warning: "bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500",
    danger: "bg-red-600 text-white hover:bg-red-700 focus:ring-red-500",
    ghost: "bg-transparent text-primary hover:bg-secondary-bg focus:ring-gray-500",
    outline: "border border-gray-300 bg-transparent text-primary hover:bg-gray-50 focus:ring-gray-500"
  };
  const sizeClasses = {
    xs: "px-2 py-1 text-xs",
    sm: "px-3 py-1.5 text-sm",
    md: "px-4 py-2 text-sm",
    lg: "px-6 py-3 text-base",
    xl: "px-8 py-4 text-lg"
  };
  const handleClick = (e) => {
    if (loading || disabled) return;
    onClick == null ? void 0 : onClick(e);
  };
  const renderIcon = () => {
    if (loading) {
      return /* @__PURE__ */ jsxRuntimeExports.jsx(LoadingSpinner, { size });
    }
    return icon;
  };
  const renderContent = () => {
    if (iconPosition === "only") {
      return renderIcon();
    }
    if (iconPosition === "left") {
      return /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
        renderIcon() && /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "mr-2", children: renderIcon() }),
        children
      ] });
    }
    if (iconPosition === "right") {
      return /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
        children,
        renderIcon() && /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "ml-2", children: renderIcon() })
      ] });
    }
    return children;
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx(
    "button",
    __spreadProps(__spreadValues({}, props), {
      className: cn(
        // 基础样式
        "inline-flex items-center justify-center font-medium rounded-md transition-colors",
        "focus:outline-none focus:ring-2 focus:ring-offset-2",
        "disabled:opacity-50 disabled:cursor-not-allowed",
        // 变体样式
        variantClasses[variant],
        // 尺寸样式
        sizeClasses[size],
        // 全宽样式
        fullWidth && "w-full",
        // 加载状态
        loading && "cursor-wait",
        // 自定义类名
        className
      ),
      disabled: disabled || loading,
      onClick: handleClick,
      children: renderContent()
    })
  );
};
const PrimaryButton = (props) => /* @__PURE__ */ jsxRuntimeExports.jsx(Button, __spreadProps(__spreadValues({}, props), { variant: "primary" }));
const SecondaryButton = (props) => /* @__PURE__ */ jsxRuntimeExports.jsx(Button, __spreadProps(__spreadValues({}, props), { variant: "secondary" }));
const DangerButton = (props) => /* @__PURE__ */ jsxRuntimeExports.jsx(Button, __spreadProps(__spreadValues({}, props), { variant: "danger" }));
export {
  Button as B,
  DangerButton as D,
  PrimaryButton as P,
  SecondaryButton as S
};
