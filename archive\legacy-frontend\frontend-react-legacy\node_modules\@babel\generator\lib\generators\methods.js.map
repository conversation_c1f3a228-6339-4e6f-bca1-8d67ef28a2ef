{"version": 3, "names": ["_t", "require", "_index", "isIdentifier", "_params", "node", "idNode", "parentNode", "print", "typeParameters", "nameInfo", "_getFuncIdName", "call", "sourceIdentifierName", "name", "pos", "token", "_parameters", "params", "noLineTerminator", "type", "returnType", "_noLineTerminator", "parameters", "endToken", "exit", "enterDelimited", "trailingComma", "shouldPrintTrailingComma", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "i", "_param", "space", "parameter", "printJoin", "decorators", "optional", "typeAnnotation", "_methodHead", "kind", "key", "word", "async", "generator", "computed", "undefined", "_predicate", "noLineTerminatorAfter", "predicate", "_functionHead", "parent", "format", "preserveFormat", "_endsWithInnerRaw", "id", "FunctionExpression", "body", "ArrowFunctionExpression", "_shouldPrintArrowParamsParens", "printInnerComments", "tokenContext", "TokenContext", "arrowBody", "_firstParam$leadingCo", "_firstParam$trailingC", "firstParam", "leadingComments", "trailingComments", "tokenMap", "loc", "findMatching", "arrowToken", "start", "line", "retainLines", "parentType", "left", "_id$loc", "_id$loc2", "identifierName", "_id$loc3", "_id$loc4", "value"], "sources": ["../../src/generators/methods.ts"], "sourcesContent": ["import type Printer from \"../printer.ts\";\nimport type * as t from \"@babel/types\";\nimport { isIdentifier, type ParentMaps } from \"@babel/types\";\nimport { TokenContext } from \"../node/index.ts\";\n\ntype ParentsOf<T extends t.Node> = ParentMaps[T[\"type\"]];\n\nexport function _params(\n  this: Printer,\n  node: t.Function | t.TSDeclareMethod | t.TSDeclareFunction,\n  idNode: t.Expression | t.PrivateName,\n  parentNode: ParentsOf<typeof node>,\n) {\n  this.print(node.typeParameters);\n\n  const nameInfo = _getFuncIdName.call(this, idNode, parentNode);\n  if (nameInfo) {\n    this.sourceIdentifierName(nameInfo.name, nameInfo.pos);\n  }\n\n  this.token(\"(\");\n  this._parameters(node.params, \")\");\n\n  const noLineTerminator = node.type === \"ArrowFunctionExpression\";\n  this.print(node.returnType, noLineTerminator);\n\n  this._noLineTerminator = noLineTerminator;\n}\n\nexport function _parameters(\n  this: Printer,\n  parameters: t.Function[\"params\"],\n  endToken: string,\n) {\n  const exit = this.enterDelimited();\n\n  const trailingComma = this.shouldPrintTrailingComma(endToken);\n\n  const paramLength = parameters.length;\n  for (let i = 0; i < paramLength; i++) {\n    this._param(parameters[i]);\n\n    if (trailingComma || i < paramLength - 1) {\n      this.token(\",\", null, i);\n      this.space();\n    }\n  }\n\n  this.token(endToken);\n  exit();\n}\n\nexport function _param(\n  this: Printer,\n  parameter: t.Identifier | t.RestElement | t.Pattern | t.TSParameterProperty,\n) {\n  // @ts-expect-error decorators is not in VoidPattern\n  this.printJoin(parameter.decorators);\n  this.print(parameter);\n  if (\n    // @ts-expect-error optional is not in TSParameterProperty\n    parameter.optional\n  ) {\n    this.token(\"?\"); // TS / flow\n  }\n\n  this.print(\n    // @ts-expect-error typeAnnotation is not in TSParameterProperty\n    parameter.typeAnnotation,\n  ); // TS / flow\n}\n\nexport function _methodHead(this: Printer, node: t.Method | t.TSDeclareMethod) {\n  const kind = node.kind;\n  const key = node.key;\n\n  if (kind === \"get\" || kind === \"set\") {\n    this.word(kind);\n    this.space();\n  }\n\n  if (node.async) {\n    this.word(\"async\", true);\n    this.space();\n  }\n\n  if (\n    kind === \"method\" ||\n    // @ts-expect-error Fixme: kind: \"init\" is not defined\n    kind === \"init\"\n  ) {\n    if (node.generator) {\n      this.token(\"*\");\n    }\n  }\n\n  if (node.computed) {\n    this.token(\"[\");\n    this.print(key);\n    this.token(\"]\");\n  } else {\n    this.print(key);\n  }\n\n  if (\n    // @ts-expect-error optional is not in ObjectMethod\n    node.optional\n  ) {\n    // TS\n    this.token(\"?\");\n  }\n\n  this._params(\n    node,\n    node.computed && node.key.type !== \"StringLiteral\" ? undefined : node.key,\n    undefined,\n  );\n}\n\nexport function _predicate(\n  this: Printer,\n  node:\n    | t.FunctionDeclaration\n    | t.FunctionExpression\n    | t.ArrowFunctionExpression,\n  noLineTerminatorAfter?: boolean,\n) {\n  if (node.predicate) {\n    if (!node.returnType) {\n      this.token(\":\");\n    }\n    this.space();\n    this.print(node.predicate, noLineTerminatorAfter);\n  }\n}\n\nexport function _functionHead(\n  this: Printer,\n  node: t.FunctionDeclaration | t.FunctionExpression | t.TSDeclareFunction,\n  parent: ParentsOf<typeof node>,\n) {\n  if (node.async) {\n    this.word(\"async\");\n    if (!this.format.preserveFormat) {\n      // We prevent inner comments from being printed here,\n      // so that they are always consistently printed in the\n      // same place regardless of the function type.\n      this._endsWithInnerRaw = false;\n    }\n    this.space();\n  }\n  this.word(\"function\");\n  if (node.generator) {\n    if (!this.format.preserveFormat) {\n      // We prevent inner comments from being printed here,\n      // so that they are always consistently printed in the\n      // same place regardless of the function type.\n      this._endsWithInnerRaw = false;\n    }\n    this.token(\"*\");\n  }\n\n  this.space();\n  if (node.id) {\n    this.print(node.id);\n  }\n\n  this._params(node, node.id, parent);\n  if (node.type !== \"TSDeclareFunction\") {\n    this._predicate(node);\n  }\n}\n\nexport function FunctionExpression(\n  this: Printer,\n  node: t.FunctionExpression,\n  parent: ParentsOf<typeof node>,\n) {\n  this._functionHead(node, parent);\n  this.space();\n  this.print(node.body);\n}\n\nexport { FunctionExpression as FunctionDeclaration };\n\nexport function ArrowFunctionExpression(\n  this: Printer,\n  node: t.ArrowFunctionExpression,\n  parent: ParentsOf<typeof node>,\n) {\n  if (node.async) {\n    this.word(\"async\", true);\n    this.space();\n  }\n\n  if (this._shouldPrintArrowParamsParens(node)) {\n    this._params(node, undefined, parent);\n  } else {\n    this.print(node.params[0], true);\n  }\n\n  this._predicate(node, true);\n  this.space();\n  // When printing (x)/*1*/=>{}, we remove the parentheses\n  // and thus there aren't two contiguous inner tokens.\n  // We forcefully print inner comments here.\n  this.printInnerComments();\n  this.token(\"=>\");\n\n  this.space();\n\n  this.tokenContext |= TokenContext.arrowBody;\n  this.print(node.body);\n}\n\n// Try to avoid printing parens in simple cases, but only if we're pretty\n// sure that they aren't needed by type annotations or potential newlines.\nexport function _shouldPrintArrowParamsParens(\n  this: Printer,\n  node: t.ArrowFunctionExpression,\n): boolean {\n  if (node.params.length !== 1) return true;\n\n  if (node.typeParameters || node.returnType || node.predicate) {\n    return true;\n  }\n\n  const firstParam = node.params[0];\n  if (\n    !isIdentifier(firstParam) ||\n    firstParam.typeAnnotation ||\n    firstParam.optional ||\n    // Flow does not support `foo /*: string*/ => {};`\n    firstParam.leadingComments?.length ||\n    firstParam.trailingComments?.length\n  ) {\n    return true;\n  }\n\n  if (this.tokenMap) {\n    if (node.loc == null) return true;\n    if (this.tokenMap.findMatching(node, \"(\") !== null) return true;\n    const arrowToken = this.tokenMap.findMatching(node, \"=>\");\n    if (arrowToken?.loc == null) return true;\n    return arrowToken.loc.start.line !== node.loc.start.line;\n  }\n\n  if (this.format.retainLines) return true;\n\n  return false;\n}\n\nfunction _getFuncIdName(\n  this: Printer,\n  idNode: t.Expression | t.PrivateName,\n  parent: ParentsOf<t.Function | t.TSDeclareMethod | t.TSDeclareFunction>,\n) {\n  let id: t.Expression | t.PrivateName | t.LVal | t.VoidPattern = idNode;\n\n  if (!id && parent) {\n    const parentType = parent.type;\n\n    if (parentType === \"VariableDeclarator\") {\n      id = parent.id;\n    } else if (\n      parentType === \"AssignmentExpression\" ||\n      parentType === \"AssignmentPattern\"\n    ) {\n      id = parent.left;\n    } else if (\n      parentType === \"ObjectProperty\" ||\n      parentType === \"ClassProperty\"\n    ) {\n      if (!parent.computed || parent.key.type === \"StringLiteral\") {\n        id = parent.key;\n      }\n    } else if (\n      parentType === \"ClassPrivateProperty\" ||\n      parentType === \"ClassAccessorProperty\"\n    ) {\n      id = parent.key;\n    }\n  }\n\n  if (!id) return;\n\n  let nameInfo;\n\n  if (id.type === \"Identifier\") {\n    nameInfo = {\n      pos: id.loc?.start,\n      name: id.loc?.identifierName || id.name,\n    };\n  } else if (id.type === \"PrivateName\") {\n    nameInfo = {\n      pos: id.loc?.start,\n      name: \"#\" + id.id.name,\n    };\n  } else if (id.type === \"StringLiteral\") {\n    nameInfo = {\n      pos: id.loc?.start,\n      name: id.value,\n    };\n  }\n\n  return nameInfo;\n}\n"], "mappings": ";;;;;;;;;;;;;;AAEA,IAAAA,EAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAAgD;EADvCE;AAAY,IAAAH,EAAA;AAKd,SAASI,OAAOA,CAErBC,IAA0D,EAC1DC,MAAoC,EACpCC,UAAkC,EAClC;EACA,IAAI,CAACC,KAAK,CAACH,IAAI,CAACI,cAAc,CAAC;EAE/B,MAAMC,QAAQ,GAAGC,cAAc,CAACC,IAAI,CAAC,IAAI,EAAEN,MAAM,EAAEC,UAAU,CAAC;EAC9D,IAAIG,QAAQ,EAAE;IACZ,IAAI,CAACG,oBAAoB,CAACH,QAAQ,CAACI,IAAI,EAAEJ,QAAQ,CAACK,GAAG,CAAC;EACxD;EAEA,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,WAAW,CAACZ,IAAI,CAACa,MAAM,EAAE,GAAG,CAAC;EAElC,MAAMC,gBAAgB,GAAGd,IAAI,CAACe,IAAI,KAAK,yBAAyB;EAChE,IAAI,CAACZ,KAAK,CAACH,IAAI,CAACgB,UAAU,EAAEF,gBAAgB,CAAC;EAE7C,IAAI,CAACG,iBAAiB,GAAGH,gBAAgB;AAC3C;AAEO,SAASF,WAAWA,CAEzBM,UAAgC,EAChCC,QAAgB,EAChB;EACA,MAAMC,IAAI,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;EAElC,MAAMC,aAAa,GAAG,IAAI,CAACC,wBAAwB,CAACJ,QAAQ,CAAC;EAE7D,MAAMK,WAAW,GAAGN,UAAU,CAACO,MAAM;EACrC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,WAAW,EAAEE,CAAC,EAAE,EAAE;IACpC,IAAI,CAACC,MAAM,CAACT,UAAU,CAACQ,CAAC,CAAC,CAAC;IAE1B,IAAIJ,aAAa,IAAII,CAAC,GAAGF,WAAW,GAAG,CAAC,EAAE;MACxC,IAAI,CAACb,KAAK,CAAC,GAAG,EAAE,IAAI,EAAEe,CAAC,CAAC;MACxB,IAAI,CAACE,KAAK,CAAC,CAAC;IACd;EACF;EAEA,IAAI,CAACjB,KAAK,CAACQ,QAAQ,CAAC;EACpBC,IAAI,CAAC,CAAC;AACR;AAEO,SAASO,MAAMA,CAEpBE,SAA2E,EAC3E;EAEA,IAAI,CAACC,SAAS,CAACD,SAAS,CAACE,UAAU,CAAC;EACpC,IAAI,CAAC5B,KAAK,CAAC0B,SAAS,CAAC;EACrB,IAEEA,SAAS,CAACG,QAAQ,EAClB;IACA,IAAI,CAACrB,SAAK,GAAI,CAAC;EACjB;EAEA,IAAI,CAACR,KAAK,CAER0B,SAAS,CAACI,cACZ,CAAC;AACH;AAEO,SAASC,WAAWA,CAAgBlC,IAAkC,EAAE;EAC7E,MAAMmC,IAAI,GAAGnC,IAAI,CAACmC,IAAI;EACtB,MAAMC,GAAG,GAAGpC,IAAI,CAACoC,GAAG;EAEpB,IAAID,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,KAAK,EAAE;IACpC,IAAI,CAACE,IAAI,CAACF,IAAI,CAAC;IACf,IAAI,CAACP,KAAK,CAAC,CAAC;EACd;EAEA,IAAI5B,IAAI,CAACsC,KAAK,EAAE;IACd,IAAI,CAACD,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;IACxB,IAAI,CAACT,KAAK,CAAC,CAAC;EACd;EAEA,IACEO,IAAI,KAAK,QAAQ,IAEjBA,IAAI,KAAK,MAAM,EACf;IACA,IAAInC,IAAI,CAACuC,SAAS,EAAE;MAClB,IAAI,CAAC5B,SAAK,GAAI,CAAC;IACjB;EACF;EAEA,IAAIX,IAAI,CAACwC,QAAQ,EAAE;IACjB,IAAI,CAAC7B,SAAK,GAAI,CAAC;IACf,IAAI,CAACR,KAAK,CAACiC,GAAG,CAAC;IACf,IAAI,CAACzB,SAAK,GAAI,CAAC;EACjB,CAAC,MAAM;IACL,IAAI,CAACR,KAAK,CAACiC,GAAG,CAAC;EACjB;EAEA,IAEEpC,IAAI,CAACgC,QAAQ,EACb;IAEA,IAAI,CAACrB,SAAK,GAAI,CAAC;EACjB;EAEA,IAAI,CAACZ,OAAO,CACVC,IAAI,EACJA,IAAI,CAACwC,QAAQ,IAAIxC,IAAI,CAACoC,GAAG,CAACrB,IAAI,KAAK,eAAe,GAAG0B,SAAS,GAAGzC,IAAI,CAACoC,GAAG,EACzEK,SACF,CAAC;AACH;AAEO,SAASC,UAAUA,CAExB1C,IAG6B,EAC7B2C,qBAA+B,EAC/B;EACA,IAAI3C,IAAI,CAAC4C,SAAS,EAAE;IAClB,IAAI,CAAC5C,IAAI,CAACgB,UAAU,EAAE;MACpB,IAAI,CAACL,SAAK,GAAI,CAAC;IACjB;IACA,IAAI,CAACiB,KAAK,CAAC,CAAC;IACZ,IAAI,CAACzB,KAAK,CAACH,IAAI,CAAC4C,SAAS,EAAED,qBAAqB,CAAC;EACnD;AACF;AAEO,SAASE,aAAaA,CAE3B7C,IAAwE,EACxE8C,MAA8B,EAC9B;EACA,IAAI9C,IAAI,CAACsC,KAAK,EAAE;IACd,IAAI,CAACD,IAAI,CAAC,OAAO,CAAC;IAClB,IAAI,CAAC,IAAI,CAACU,MAAM,CAACC,cAAc,EAAE;MAI/B,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAChC;IACA,IAAI,CAACrB,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACS,IAAI,CAAC,UAAU,CAAC;EACrB,IAAIrC,IAAI,CAACuC,SAAS,EAAE;IAClB,IAAI,CAAC,IAAI,CAACQ,MAAM,CAACC,cAAc,EAAE;MAI/B,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAChC;IACA,IAAI,CAACtC,SAAK,GAAI,CAAC;EACjB;EAEA,IAAI,CAACiB,KAAK,CAAC,CAAC;EACZ,IAAI5B,IAAI,CAACkD,EAAE,EAAE;IACX,IAAI,CAAC/C,KAAK,CAACH,IAAI,CAACkD,EAAE,CAAC;EACrB;EAEA,IAAI,CAACnD,OAAO,CAACC,IAAI,EAAEA,IAAI,CAACkD,EAAE,EAAEJ,MAAM,CAAC;EACnC,IAAI9C,IAAI,CAACe,IAAI,KAAK,mBAAmB,EAAE;IACrC,IAAI,CAAC2B,UAAU,CAAC1C,IAAI,CAAC;EACvB;AACF;AAEO,SAASmD,kBAAkBA,CAEhCnD,IAA0B,EAC1B8C,MAA8B,EAC9B;EACA,IAAI,CAACD,aAAa,CAAC7C,IAAI,EAAE8C,MAAM,CAAC;EAChC,IAAI,CAAClB,KAAK,CAAC,CAAC;EACZ,IAAI,CAACzB,KAAK,CAACH,IAAI,CAACoD,IAAI,CAAC;AACvB;AAIO,SAASC,uBAAuBA,CAErCrD,IAA+B,EAC/B8C,MAA8B,EAC9B;EACA,IAAI9C,IAAI,CAACsC,KAAK,EAAE;IACd,IAAI,CAACD,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;IACxB,IAAI,CAACT,KAAK,CAAC,CAAC;EACd;EAEA,IAAI,IAAI,CAAC0B,6BAA6B,CAACtD,IAAI,CAAC,EAAE;IAC5C,IAAI,CAACD,OAAO,CAACC,IAAI,EAAEyC,SAAS,EAAEK,MAAM,CAAC;EACvC,CAAC,MAAM;IACL,IAAI,CAAC3C,KAAK,CAACH,IAAI,CAACa,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;EAClC;EAEA,IAAI,CAAC6B,UAAU,CAAC1C,IAAI,EAAE,IAAI,CAAC;EAC3B,IAAI,CAAC4B,KAAK,CAAC,CAAC;EAIZ,IAAI,CAAC2B,kBAAkB,CAAC,CAAC;EACzB,IAAI,CAAC5C,KAAK,CAAC,IAAI,CAAC;EAEhB,IAAI,CAACiB,KAAK,CAAC,CAAC;EAEZ,IAAI,CAAC4B,YAAY,IAAIC,mBAAY,CAACC,SAAS;EAC3C,IAAI,CAACvD,KAAK,CAACH,IAAI,CAACoD,IAAI,CAAC;AACvB;AAIO,SAASE,6BAA6BA,CAE3CtD,IAA+B,EACtB;EAAA,IAAA2D,qBAAA,EAAAC,qBAAA;EACT,IAAI5D,IAAI,CAACa,MAAM,CAACY,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EAEzC,IAAIzB,IAAI,CAACI,cAAc,IAAIJ,IAAI,CAACgB,UAAU,IAAIhB,IAAI,CAAC4C,SAAS,EAAE;IAC5D,OAAO,IAAI;EACb;EAEA,MAAMiB,UAAU,GAAG7D,IAAI,CAACa,MAAM,CAAC,CAAC,CAAC;EACjC,IACE,CAACf,YAAY,CAAC+D,UAAU,CAAC,IACzBA,UAAU,CAAC5B,cAAc,IACzB4B,UAAU,CAAC7B,QAAQ,KAAA2B,qBAAA,GAEnBE,UAAU,CAACC,eAAe,aAA1BH,qBAAA,CAA4BlC,MAAM,KAAAmC,qBAAA,GAClCC,UAAU,CAACE,gBAAgB,aAA3BH,qBAAA,CAA6BnC,MAAM,EACnC;IACA,OAAO,IAAI;EACb;EAEA,IAAI,IAAI,CAACuC,QAAQ,EAAE;IACjB,IAAIhE,IAAI,CAACiE,GAAG,IAAI,IAAI,EAAE,OAAO,IAAI;IACjC,IAAI,IAAI,CAACD,QAAQ,CAACE,YAAY,CAAClE,IAAI,EAAE,GAAG,CAAC,KAAK,IAAI,EAAE,OAAO,IAAI;IAC/D,MAAMmE,UAAU,GAAG,IAAI,CAACH,QAAQ,CAACE,YAAY,CAAClE,IAAI,EAAE,IAAI,CAAC;IACzD,IAAI,CAAAmE,UAAU,oBAAVA,UAAU,CAAEF,GAAG,KAAI,IAAI,EAAE,OAAO,IAAI;IACxC,OAAOE,UAAU,CAACF,GAAG,CAACG,KAAK,CAACC,IAAI,KAAKrE,IAAI,CAACiE,GAAG,CAACG,KAAK,CAACC,IAAI;EAC1D;EAEA,IAAI,IAAI,CAACtB,MAAM,CAACuB,WAAW,EAAE,OAAO,IAAI;EAExC,OAAO,KAAK;AACd;AAEA,SAAShE,cAAcA,CAErBL,MAAoC,EACpC6C,MAAuE,EACvE;EACA,IAAII,EAAyD,GAAGjD,MAAM;EAEtE,IAAI,CAACiD,EAAE,IAAIJ,MAAM,EAAE;IACjB,MAAMyB,UAAU,GAAGzB,MAAM,CAAC/B,IAAI;IAE9B,IAAIwD,UAAU,KAAK,oBAAoB,EAAE;MACvCrB,EAAE,GAAGJ,MAAM,CAACI,EAAE;IAChB,CAAC,MAAM,IACLqB,UAAU,KAAK,sBAAsB,IACrCA,UAAU,KAAK,mBAAmB,EAClC;MACArB,EAAE,GAAGJ,MAAM,CAAC0B,IAAI;IAClB,CAAC,MAAM,IACLD,UAAU,KAAK,gBAAgB,IAC/BA,UAAU,KAAK,eAAe,EAC9B;MACA,IAAI,CAACzB,MAAM,CAACN,QAAQ,IAAIM,MAAM,CAACV,GAAG,CAACrB,IAAI,KAAK,eAAe,EAAE;QAC3DmC,EAAE,GAAGJ,MAAM,CAACV,GAAG;MACjB;IACF,CAAC,MAAM,IACLmC,UAAU,KAAK,sBAAsB,IACrCA,UAAU,KAAK,uBAAuB,EACtC;MACArB,EAAE,GAAGJ,MAAM,CAACV,GAAG;IACjB;EACF;EAEA,IAAI,CAACc,EAAE,EAAE;EAET,IAAI7C,QAAQ;EAEZ,IAAI6C,EAAE,CAACnC,IAAI,KAAK,YAAY,EAAE;IAAA,IAAA0D,OAAA,EAAAC,QAAA;IAC5BrE,QAAQ,GAAG;MACTK,GAAG,GAAA+D,OAAA,GAAEvB,EAAE,CAACe,GAAG,qBAANQ,OAAA,CAAQL,KAAK;MAClB3D,IAAI,EAAE,EAAAiE,QAAA,GAAAxB,EAAE,CAACe,GAAG,qBAANS,QAAA,CAAQC,cAAc,KAAIzB,EAAE,CAACzC;IACrC,CAAC;EACH,CAAC,MAAM,IAAIyC,EAAE,CAACnC,IAAI,KAAK,aAAa,EAAE;IAAA,IAAA6D,QAAA;IACpCvE,QAAQ,GAAG;MACTK,GAAG,GAAAkE,QAAA,GAAE1B,EAAE,CAACe,GAAG,qBAANW,QAAA,CAAQR,KAAK;MAClB3D,IAAI,EAAE,GAAG,GAAGyC,EAAE,CAACA,EAAE,CAACzC;IACpB,CAAC;EACH,CAAC,MAAM,IAAIyC,EAAE,CAACnC,IAAI,KAAK,eAAe,EAAE;IAAA,IAAA8D,QAAA;IACtCxE,QAAQ,GAAG;MACTK,GAAG,GAAAmE,QAAA,GAAE3B,EAAE,CAACe,GAAG,qBAANY,QAAA,CAAQT,KAAK;MAClB3D,IAAI,EAAEyC,EAAE,CAAC4B;IACX,CAAC;EACH;EAEA,OAAOzE,QAAQ;AACjB", "ignoreList": []}