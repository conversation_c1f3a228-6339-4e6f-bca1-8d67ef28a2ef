import React, { useState } from 'react';
import { Search, Grid3X3, List, Layers, Settings, Upload, ZoomIn, ZoomOut, X } from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Slider } from './ui/slider';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from './ui/dropdown-menu';
import { ImageWithFallback } from './figma/ImageWithFallback';

interface GalleryProps {
  selectedImages: string[];
  onSelectedImagesChange: (images: string[]) => void;
  selectedTags: string[];
  currentLibrary: string;
  isVisible: boolean;
}

interface ImageItem {
  id: string;
  filename: string;
  src: string;
  type: string;
  size: string;
  tags: string[];
}

export const Gallery: React.FC<GalleryProps> = ({
  selectedImages,
  onSelectedImagesChange,
  selectedTags,
  currentLibrary,
  isVisible,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [zoomLevel, setZoomLevel] = useState([5]);
  const [viewMode, setViewMode] = useState<'adaptive' | 'masonry' | 'grid' | 'list'>('adaptive');
  const [showImageInfo, setShowImageInfo] = useState(true);
  const [imageSearchResults, setImageSearchResults] = useState<string[]>([]);
  const [isDraggingImage, setIsDraggingImage] = useState(false);

  // 模拟图像数据
  const images: ImageItem[] = [
    {
      id: '1',
      filename: '街头摄影_001.jpg',
      src: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=300&fit=crop',
      type: 'JPEG',
      size: '2.4MB',
      tags: ['街头摄影', '人物', '黑白', '高质量']
    },
    {
      id: '2',
      filename: '建筑摄影_002.jpg',
      src: 'https://images.unsplash.com/photo-1497436072909-f5e4be1453c1?w=400&h=300&fit=crop',
      type: 'JPEG',
      size: '3.1MB',
      tags: ['建筑', '现代', '清晰度优秀', '构图良好']
    },
    {
      id: '3',
      filename: '风景摄影_003.jpg',
      src: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop',
      type: 'JPEG',
      size: '4.2MB',
      tags: ['风景', '自然', '色彩丰富', '高分辨率']
    },
    {
      id: '4',
      filename: '人像摄影_004.jpg',
      src: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop',
      type: 'JPEG',
      size: '2.8MB',
      tags: ['人像', '肖像', '专业', '高质量']
    },
    {
      id: '5',
      filename: '抽象艺术_005.jpg',
      src: 'https://images.unsplash.com/photo-1519452575417-564c1401ecc0?w=400&h=300&fit=crop',
      type: 'JPEG',
      size: '1.9MB',
      tags: ['抽象', '艺术作品', '创意作品', '色彩丰富']
    },
    {
      id: '6',
      filename: '城市夜景_006.jpg',
      src: 'https://images.unsplash.com/photo-1514565131-fce0801e5785?w=400&h=300&fit=crop',
      type: 'JPEG',
      size: '3.5MB',
      tags: ['城市', '夜景', '灯光', '建筑']
    },
    {
      id: '7',
      filename: '动物摄影_007.jpg',
      src: 'https://images.unsplash.com/photo-1544568100-847a948585b9?w=400&h=300&fit=crop',
      type: 'JPEG',
      size: '2.7MB',
      tags: ['动物', '自然', '野生动物', '清晰度优秀']
    },
    {
      id: '8',
      filename: '静物摄影_008.jpg',
      src: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=400&h=300&fit=crop',
      type: 'JPEG',
      size: '1.8MB',
      tags: ['静物', '产品', '商业', '高质量']
    }
  ];

  const handleImageClick = (imageId: string) => {
    if (selectedImages.includes(imageId)) {
      onSelectedImagesChange(selectedImages.filter(id => id !== imageId));
    } else {
      onSelectedImagesChange([...selectedImages, imageId]);
    }
  };

  const handleFileUpload = () => {
    // 实现文件上传逻辑
    console.log('上传文件到:', currentLibrary);
  };

  const getImageSize = () => {
    const baseSize = 200;
    const zoomFactor = zoomLevel[0] / 5;
    return Math.max(100, Math.min(400, baseSize * zoomFactor));
  };

  const getGridCols = () => {
    const containerWidth = 800; // 假设容器宽度
    const imageSize = getImageSize();
    return Math.max(1, Math.min(10, Math.floor(containerWidth / imageSize)));
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    if (term) {
      // 模拟搜索逻辑
      const results = images
        .filter(img => 
          img.filename.toLowerCase().includes(term.toLowerCase()) ||
          img.tags.some(tag => tag.toLowerCase().includes(term.toLowerCase()))
        )
        .map(img => img.id);
      setImageSearchResults(results);
    } else {
      setImageSearchResults([]);
    }
  };

  const handleImageSearch = (imageId: string) => {
    // 模拟以图搜图功能
    console.log('以图搜图:', imageId);
  };

  const clearSearch = () => {
    setSearchTerm('');
    setImageSearchResults([]);
  };

  const filteredImages = searchTerm 
    ? images.filter(img => imageSearchResults.includes(img.id))
    : images;

  return (
    <div className="h-full flex flex-col" style={{ background: 'var(--mizzy-gallery)' }}>
      {/* 顶部工具栏 */}
      <div className="p-4 border-b" style={{ borderColor: 'var(--mizzy-border)' }}>
        <div className="flex items-center justify-between mb-4">
          {/* 档案库标题和上传 */}
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              onClick={handleFileUpload}
              className="hover:bg-opacity-10"
              style={{ color: 'var(--mizzy-content)' }}
            >
              <Upload className="h-4 w-4 mr-2" />
              {currentLibrary}
            </Button>
          </div>

          {/* 缩放控制 */}
          <div className="flex items-center gap-2">
            <ZoomOut className="h-4 w-4" style={{ color: 'var(--mizzy-icon)' }} />
            <Slider
              value={zoomLevel}
              onValueChange={setZoomLevel}
              max={10}
              min={1}
              step={1}
              className="w-24"
            />
            <ZoomIn className="h-4 w-4" style={{ color: 'var(--mizzy-icon)' }} />
          </div>

          {/* 视图切换 */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                size="sm"
                className="hover:bg-opacity-10"
                style={{ color: 'var(--mizzy-icon)' }}
              >
                <Layers className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuCheckboxItem
                checked={viewMode === 'adaptive'}
                onCheckedChange={() => setViewMode('adaptive')}
              >
                自适应
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={viewMode === 'masonry'}
                onCheckedChange={() => setViewMode('masonry')}
              >
                瀑布流
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={viewMode === 'grid'}
                onCheckedChange={() => setViewMode('grid')}
              >
                网格
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={viewMode === 'list'}
                onCheckedChange={() => setViewMode('list')}
              >
                列表
              </DropdownMenuCheckboxItem>
              <DropdownMenuSeparator />
              <DropdownMenuCheckboxItem
                checked={showImageInfo}
                onCheckedChange={setShowImageInfo}
              >
                显示图像信息
              </DropdownMenuCheckboxItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* 搜索框 */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4" style={{ color: 'var(--mizzy-icon)' }} />
          <Input
            placeholder="搜索图像"
            value={searchTerm}
            onChange={(e) => handleSearch(e.target.value)}
            className="pl-10 pr-10"
            style={{ 
              background: 'var(--mizzy-input)',
              color: 'var(--mizzy-content)',
              borderColor: 'var(--mizzy-border)'
            }}
          />
          {searchTerm && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearSearch}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
              style={{ color: 'var(--mizzy-icon)' }}
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>

      {/* 图像网格 */}
      <div className="flex-1 overflow-auto p-4">
        <div 
          className={`grid gap-4 ${
            viewMode === 'list' 
              ? 'grid-cols-1' 
              : viewMode === 'grid'
              ? 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4'
              : 'grid-cols-auto'
          }`}
          style={{
            gridTemplateColumns: viewMode === 'adaptive' 
              ? `repeat(${getGridCols()}, minmax(${getImageSize()}px, 1fr))`
              : undefined
          }}
        >
          {filteredImages.map((image) => {
            const isSelected = selectedImages.includes(image.id);
            const isHighlighted = imageSearchResults.includes(image.id);
            
            return (
              <div
                key={image.id}
                className={`relative group cursor-pointer transition-all duration-200 ${
                  isSelected ? 'ring-2' : ''
                } ${isHighlighted ? 'ring-2 ring-yellow-500' : ''}`}
                style={{
                  borderColor: isSelected ? 'var(--mizzy-highlight)' : 'transparent'
                }}
                onClick={() => handleImageClick(image.id)}
                onDragStart={(e) => {
                  setIsDraggingImage(true);
                  e.dataTransfer.setData('text/plain', image.id);
                }}
                onDragEnd={() => setIsDraggingImage(false)}
                draggable
              >
                <div className="relative">
                  <ImageWithFallback
                    src={image.src}
                    alt={image.filename}
                    className="w-full h-auto rounded"
                    style={{ 
                      width: viewMode === 'adaptive' ? `${getImageSize()}px` : '100%',
                      height: viewMode === 'adaptive' ? `${getImageSize()}px` : 'auto'
                    }}
                  />
                  
                  {/* 图像信息覆盖层 */}
                  {showImageInfo && (
                    <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-2 text-xs">
                      <div className="font-medium truncate">{image.filename}</div>
                      <div className="text-gray-300">
                        {image.type} • {image.size}
                      </div>
                    </div>
                  )}

                  {/* 选择指示器 */}
                  {isSelected && (
                    <div className="absolute top-2 right-2 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                  )}
                </div>

                {/* 标签显示 */}
                {showImageInfo && image.tags.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-1">
                    {image.tags.slice(0, 3).map((tag, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 text-xs rounded"
                        style={{ 
                          background: 'var(--mizzy-button)',
                          color: 'var(--mizzy-content)'
                        }}
                      >
                        {tag}
                      </span>
                    ))}
                    {image.tags.length > 3 && (
                      <span
                        className="px-2 py-1 text-xs rounded"
                        style={{ 
                          background: 'var(--mizzy-button)',
                          color: 'var(--mizzy-content)'
                        }}
                      >
                        +{image.tags.length - 3}
                      </span>
                    )}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};