# Mizzy Star 性能优化指南

## 🚀 已实现的性能优化

### 1. 代码分割 (Code Splitting)

#### 路由级代码分割
```typescript
// 使用 React.lazy 和 Suspense 实现路由级代码分割
const CasesPage = createLazyRoute(
  () => import('./pages/CasesPage'),
  { fallback: <LoadingFallbacks.Page />, preload: true }
);
```

#### 手动代码块分割
```javascript
// vite.config.ts 中的配置
manualChunks: {
  'react-vendor': ['react', 'react-dom'],
  'router': ['react-router-dom'],
  'state-management': ['zustand', '@tanstack/react-query'],
  'ui-vendor': ['clsx'],
  'virtual': ['@tanstack/react-virtual'],
}
```

### 2. 组件优化

#### React.memo 优化
```typescript
// 使用 React.memo 防止不必要的重渲染
const CaseCard = React.memo(({ case: caseItem, onEdit, onDelete }) => {
  // 组件实现
});

const ImageThumbnail = React.memo(({ file, isHighlighted }) => {
  // 组件实现
});
```

#### useMemo 和 useCallback 优化
```typescript
// 缓存计算结果
const formattedDate = useMemo(() => {
  return new Date(caseItem.created_at).toLocaleDateString('zh-CN');
}, [caseItem.created_at]);

// 缓存回调函数
const handleClick = useCallback(() => {
  navigate(`/cases/${caseItem.id}`);
}, [navigate, caseItem.id]);
```

### 3. 虚拟滚动

#### VirtualGrid 组件
```typescript
// 处理大量数据的网格显示
<VirtualGrid
  items={files}
  renderItem={(file, index) => <ImageThumbnail file={file} />}
  itemHeight={200}
  itemsPerRow={5}
  containerHeight={600}
/>
```

#### VirtualList 组件
```typescript
// 处理大量数据的列表显示
<VirtualList
  items={tags}
  renderItem={(tag, index) => <TagItem tag={tag} />}
  itemHeight={40}
  containerHeight={400}
/>
```

### 4. 状态管理优化

#### TanStack Query 缓存策略
```typescript
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000,    // 5分钟新鲜时间
      gcTime: 10 * 60 * 1000,      // 10分钟缓存时间
      retry: 3,                     // 重试3次
      refetchOnWindowFocus: false,  // 窗口聚焦时不重新获取
    },
  },
});
```

#### Zustand 状态优化
```typescript
// 使用 devtools 中间件进行调试
export const useTagManagementStore = create<TagManagementState>()(
  devtools(
    (set, get) => ({
      // 状态和 actions
    }),
    { name: 'tag-management-store' }
  )
);
```

### 5. 图片优化

#### 懒加载
```typescript
<img
  src={imageUrl}
  alt={file.filename}
  className="w-full h-full object-cover"
  loading="lazy"  // 原生懒加载
  onError={handleImageError}
/>
```

#### 缩略图策略
```typescript
// 优先使用缩略图，失败时降级到原图
const imageUrl = useMemo(() => {
  if (file.thumbnail_small_path) {
    return `file://${file.thumbnail_small_path}`;
  }
  return `/api/v1/cases/${file.case_id}/files/${file.id}/thumbnail`;
}, [file.thumbnail_small_path, file.case_id, file.id]);
```

### 6. Bundle 优化

#### Vite 构建配置
```typescript
build: {
  rollupOptions: {
    output: {
      // 文件命名优化
      chunkFileNames: 'js/[name]-[hash].js',
      entryFileNames: 'js/[name]-[hash].js',
      assetFileNames: (assetInfo) => {
        // 按类型分类资源文件
      },
    },
  },
  // 压缩配置
  minify: 'terser',
  terserOptions: {
    compress: {
      drop_console: process.env.NODE_ENV === 'production',
      drop_debugger: true,
    },
  },
}
```

## 📊 性能监控

### 1. 性能监控工具

#### 自定义性能监控器
```typescript
// 使用 performanceMonitor 监控关键操作
performanceMonitor.start('component-render');
// 组件渲染逻辑
performanceMonitor.end('component-render');

// 监控异步操作
await performanceMonitor.measureAsync('api-call', async () => {
  return await apiClient.get('/api/data');
});
```

#### 内存使用监控
```typescript
// 记录内存使用情况
logMemoryUsage('after-large-operation');
```

#### 网络请求监控
```typescript
// 监控API请求性能
const monitor = monitorNetworkRequest('/api/cases', 'GET');
const response = await fetch('/api/cases');
monitor.end(response.ok);
```

### 2. 组件渲染监控

#### 渲染次数统计
```typescript
// 监控组件重渲染次数
function MyComponent() {
  const renderCount = useRenderCount('MyComponent');
  // 组件逻辑
}
```

#### 性能装饰器
```typescript
// 使用装饰器监控组件性能
const OptimizedComponent = withPerformanceMonitoring(
  MyComponent,
  'MyComponent'
);
```

## 🎯 性能优化最佳实践

### 1. 组件设计原则

#### 单一职责
- 每个组件只负责一个功能
- 避免过度复杂的组件

#### 合理的组件粒度
- 不要过度拆分组件
- 平衡复用性和性能

#### Props 设计
- 避免传递不必要的 props
- 使用稳定的引用

### 2. 状态管理原则

#### 状态分层
- 全局状态：应用级别的状态
- 功能状态：特定功能的状态
- 组件状态：组件内部状态

#### 避免不必要的状态更新
- 使用 useCallback 缓存函数
- 使用 useMemo 缓存计算结果
- 合理使用 React.memo

### 3. 数据获取优化

#### 缓存策略
- 合理设置缓存时间
- 使用乐观更新
- 预加载关键数据

#### 分页和虚拟滚动
- 大数据集使用分页
- 长列表使用虚拟滚动
- 按需加载数据

### 4. 资源优化

#### 图片优化
- 使用适当的图片格式
- 实现懒加载
- 提供多种尺寸

#### 字体优化
- 使用系统字体栈
- 预加载关键字体
- 字体显示优化

## 📈 性能指标

### 1. 核心 Web 指标

#### LCP (Largest Contentful Paint)
- 目标：< 2.5s
- 优化：图片优化、关键资源预加载

#### FID (First Input Delay)
- 目标：< 100ms
- 优化：代码分割、减少主线程阻塞

#### CLS (Cumulative Layout Shift)
- 目标：< 0.1
- 优化：图片尺寸预设、避免动态内容插入

### 2. 自定义指标

#### 组件渲染时间
- 监控关键组件的渲染性能
- 识别性能瓶颈

#### API 响应时间
- 监控网络请求性能
- 优化慢接口

#### 内存使用
- 监控内存泄漏
- 优化内存使用

## 🔧 性能调试工具

### 1. 浏览器开发者工具

#### Performance 面板
- 分析运行时性能
- 识别性能瓶颈

#### Memory 面板
- 检测内存泄漏
- 分析内存使用

#### Network 面板
- 分析网络请求
- 优化资源加载

### 2. React 开发者工具

#### Profiler
- 分析组件渲染性能
- 识别不必要的重渲染

#### Components
- 检查组件状态
- 调试 props 传递

### 3. 第三方工具

#### Bundle Analyzer
```bash
# 分析 bundle 大小
npm run build:analyze
```

#### Lighthouse
- 综合性能评估
- 最佳实践建议

## 📋 性能优化检查清单

### 代码层面
- [ ] 使用 React.memo 优化组件
- [ ] 使用 useMemo 缓存计算结果
- [ ] 使用 useCallback 缓存函数
- [ ] 实现代码分割
- [ ] 避免内联对象和函数

### 数据层面
- [ ] 实现数据缓存
- [ ] 使用分页加载
- [ ] 实现虚拟滚动
- [ ] 优化 API 调用
- [ ] 避免不必要的数据获取

### 资源层面
- [ ] 图片懒加载
- [ ] 资源压缩
- [ ] 使用 CDN
- [ ] 缓存策略
- [ ] 预加载关键资源

### 监控层面
- [ ] 性能监控
- [ ] 错误监控
- [ ] 用户体验监控
- [ ] 定期性能审查
- [ ] 性能预算设置
