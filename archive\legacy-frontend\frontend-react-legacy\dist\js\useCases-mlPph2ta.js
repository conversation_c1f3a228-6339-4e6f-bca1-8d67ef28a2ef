import { R as React } from "./router-DbSvV1fW.js";
import { b as useQuery, u as useQueryClient, d as useMutation } from "./state-management-CeNIv-64.js";
import { C as CaseService } from "./caseService-rs6u721W.js";
import { u as useAppStore, q as queryKeys } from "./index-BaeIiao7.js";
const useCases = () => {
  const { addNotification } = useAppStore();
  return useQuery({
    queryKey: queryKeys.cases,
    queryFn: CaseService.getCases,
    onError: (error) => {
      addNotification({
        type: "error",
        title: "获取案例失败",
        message: error.message || "无法获取案例列表"
      });
    }
  });
};
const useCase = (id) => {
  const { addNotification } = useAppStore();
  return useQuery({
    queryKey: queryKeys.case(id),
    queryFn: () => CaseService.getCase(id),
    enabled: !!id,
    onError: (error) => {
      addNotification({
        type: "error",
        title: "获取案例详情失败",
        message: error.message || `无法获取案例 ${id} 的详情`
      });
    }
  });
};
const useCaseFiles = (caseId) => {
  const { addNotification } = useAppStore();
  const result = useQuery({
    queryKey: queryKeys.caseFiles(caseId || 0),
    queryFn: () => CaseService.getCaseFiles(caseId),
    enabled: !!caseId && caseId > 0,
    retry: 3,
    staleTime: 3e4,
    // 30秒内不重新获取
    refetchOnWindowFocus: false,
    // 防止窗口聚焦时重新获取
    refetchOnMount: false
    // 防止组件挂载时重新获取（如果数据仍然新鲜）
  });
  React.useEffect(() => {
    if (result.error) {
      console.error("获取文件列表失败:", result.error);
      addNotification({
        type: "error",
        title: "获取文件列表失败",
        message: result.error.message || "无法获取案例文件列表"
      });
    }
  }, [result.error, addNotification]);
  return result;
};
const useCreateCase = () => {
  const queryClient = useQueryClient();
  const { addNotification } = useAppStore();
  return useMutation({
    mutationFn: (data) => CaseService.createCase(data),
    onSuccess: (newCase) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.cases });
      addNotification({
        type: "success",
        title: "案例创建成功",
        message: `案例 "${newCase.case_name}" 已成功创建`
      });
    },
    onError: (error) => {
      addNotification({
        type: "error",
        title: "创建案例失败",
        message: error.message || "无法创建新案例"
      });
    }
  });
};
const useUpdateCase = () => {
  const queryClient = useQueryClient();
  const { addNotification } = useAppStore();
  return useMutation({
    mutationFn: ({ id, data }) => CaseService.updateCase(id, data),
    onSuccess: (updatedCase) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.cases });
      queryClient.invalidateQueries({ queryKey: queryKeys.case(updatedCase.id) });
      addNotification({
        type: "success",
        title: "案例更新成功",
        message: `案例 "${updatedCase.case_name}" 已成功更新`
      });
    },
    onError: (error) => {
      addNotification({
        type: "error",
        title: "更新案例失败",
        message: error.message || "无法更新案例"
      });
    }
  });
};
const useDeleteCase = () => {
  const queryClient = useQueryClient();
  const { addNotification } = useAppStore();
  return useMutation({
    mutationFn: (id) => CaseService.deleteCase(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.cases });
      addNotification({
        type: "success",
        title: "案例删除成功",
        message: "案例已成功删除"
      });
    },
    onError: (error) => {
      addNotification({
        type: "error",
        title: "删除案例失败",
        message: error.message || "无法删除案例"
      });
    }
  });
};
export {
  useCreateCase as a,
  useUpdateCase as b,
  useDeleteCase as c,
  useCase as d,
  useCaseFiles as e,
  useCases as u
};
