# Mizzy Star 设计系统

## 🎨 设计原则

### 1. 一致性 (Consistency)
- 统一的视觉语言和交互模式
- 可预测的用户体验
- 标准化的组件行为

### 2. 简洁性 (Simplicity)
- 清晰的信息层级
- 最小化认知负担
- 直观的操作流程

### 3. 可访问性 (Accessibility)
- 符合WCAG 2.1标准
- 键盘导航支持
- 屏幕阅读器友好

### 4. 响应式 (Responsive)
- 适配不同屏幕尺寸
- 灵活的布局系统
- 移动优先设计

## 🎯 颜色系统

### 主色调 (Primary Colors)
```css
primary-50:  #eff6ff
primary-100: #dbeafe
primary-200: #bfdbfe
primary-300: #93c5fd
primary-400: #60a5fa
primary-500: #3b82f6  /* 主色 */
primary-600: #2563eb
primary-700: #1d4ed8
primary-800: #1e40af
primary-900: #1e3a8a
```

### 辅助色调 (Secondary Colors)
```css
secondary-50:  #f8fafc
secondary-100: #f1f5f9
secondary-200: #e2e8f0
secondary-300: #cbd5e1
secondary-400: #94a3b8
secondary-500: #64748b  /* 辅助色 */
secondary-600: #475569
secondary-700: #334155
secondary-800: #1e293b
secondary-900: #0f172a
```

### 语义色彩 (Semantic Colors)
- **成功**: `#10b981` (green-500)
- **警告**: `#f59e0b` (amber-500)
- **错误**: `#ef4444` (red-500)
- **信息**: `#3b82f6` (blue-500)

## 📝 字体系统

### 字体族
```css
font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
```

### 字体大小
- **xs**: 12px (0.75rem)
- **sm**: 14px (0.875rem)
- **base**: 16px (1rem)
- **lg**: 18px (1.125rem)
- **xl**: 20px (1.25rem)
- **2xl**: 24px (1.5rem)
- **3xl**: 30px (1.875rem)
- **4xl**: 36px (2.25rem)

### 字体重量
- **normal**: 400
- **medium**: 500
- **semibold**: 600
- **bold**: 700

## 📏 间距系统

基于 4px 网格系统：

```css
spacing-1:  4px   (0.25rem)
spacing-2:  8px   (0.5rem)
spacing-3:  12px  (0.75rem)
spacing-4:  16px  (1rem)
spacing-5:  20px  (1.25rem)
spacing-6:  24px  (1.5rem)
spacing-8:  32px  (2rem)
spacing-10: 40px  (2.5rem)
spacing-12: 48px  (3rem)
spacing-16: 64px  (4rem)
spacing-20: 80px  (5rem)
spacing-24: 96px  (6rem)
```

## 🔲 组件规范

### 按钮 (Button)

#### 变体
- **Primary**: 主要操作按钮
- **Secondary**: 次要操作按钮
- **Outline**: 轮廓按钮
- **Ghost**: 幽灵按钮
- **Danger**: 危险操作按钮

#### 尺寸
- **Small**: 高度 32px，内边距 12px 16px
- **Medium**: 高度 40px，内边距 16px 20px
- **Large**: 高度 48px，内边距 20px 24px

#### 状态
- **Default**: 默认状态
- **Hover**: 悬停状态
- **Active**: 激活状态
- **Disabled**: 禁用状态
- **Loading**: 加载状态

### 输入框 (Input)

#### 结构
- Label (可选)
- Input Field
- Helper Text / Error Message (可选)
- Left/Right Icon (可选)

#### 状态
- **Default**: 默认状态
- **Focus**: 聚焦状态
- **Error**: 错误状态
- **Disabled**: 禁用状态

### 卡片 (Card)

#### 结构
- Header (可选)
- Content
- Footer (可选)

#### 变体
- **Default**: 基础卡片
- **Elevated**: 带阴影的卡片
- **Outlined**: 带边框的卡片

## 🎭 动画系统

### 过渡时间
- **Fast**: 150ms - 快速交互反馈
- **Normal**: 200ms - 标准过渡
- **Slow**: 300ms - 复杂动画

### 缓动函数
- **Ease**: cubic-bezier(0.25, 0.1, 0.25, 1) - 标准缓动
- **Ease-in**: cubic-bezier(0.4, 0, 1, 1) - 加速
- **Ease-out**: cubic-bezier(0, 0, 0.2, 1) - 减速
- **Ease-in-out**: cubic-bezier(0.4, 0, 0.2, 1) - 先加速后减速

### 常用动画
```css
/* 淡入 */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

/* 滑入 */
.slide-up {
  animation: slideUp 0.3s ease-out;
}

/* 缩放 */
.scale-in {
  animation: scaleIn 0.2s ease-out;
}
```

## 📱 响应式断点

```css
/* 移动设备 */
sm: 640px

/* 平板设备 */
md: 768px

/* 小型桌面 */
lg: 1024px

/* 大型桌面 */
xl: 1280px

/* 超大屏幕 */
2xl: 1536px
```

## 🔧 组件使用指南

### 按钮使用指南
- 主要按钮用于最重要的操作（如保存、提交）
- 次要按钮用于辅助操作（如取消、重置）
- 危险按钮用于不可逆操作（如删除）
- 一个界面中最多只有一个主要按钮

### 颜色使用指南
- 主色用于品牌元素和主要操作
- 辅助色用于文本和边框
- 语义色彩用于状态反馈
- 保持足够的对比度确保可读性

### 间距使用指南
- 相关元素使用较小间距（4px-8px）
- 不相关元素使用较大间距（16px-24px）
- 页面级间距使用更大值（32px-64px）

## 📋 可访问性检查清单

### 颜色和对比度
- [ ] 文本与背景对比度至少 4.5:1
- [ ] 大文本对比度至少 3:1
- [ ] 不仅依赖颜色传达信息

### 键盘导航
- [ ] 所有交互元素可通过键盘访问
- [ ] Tab 顺序逻辑合理
- [ ] 焦点指示器清晰可见

### 屏幕阅读器
- [ ] 图片有适当的 alt 文本
- [ ] 表单控件有关联的标签
- [ ] 页面结构使用语义化标签

### 交互反馈
- [ ] 操作有明确的反馈
- [ ] 错误信息清晰易懂
- [ ] 加载状态有适当提示

## 🎨 图标系统

使用 Heroicons 作为主要图标库：
- 24x24px 用于大多数界面元素
- 20x20px 用于较小的界面元素
- 16x16px 用于内联图标

常用图标：
- 添加: `plus`
- 编辑: `pencil`
- 删除: `trash`
- 搜索: `magnifying-glass`
- 设置: `cog-6-tooth`
- 用户: `user`
- 文件: `document`
- 图片: `photo`

## 📚 组件库结构

```
components/
├── Button/
│   ├── Button.tsx
│   ├── Button.stories.tsx
│   └── Button.test.tsx
├── Input/
│   ├── Input.tsx
│   ├── Input.stories.tsx
│   └── Input.test.tsx
└── ...
```

每个组件包含：
- 组件实现 (.tsx)
- Storybook 文档 (.stories.tsx)
- 单元测试 (.test.tsx)
