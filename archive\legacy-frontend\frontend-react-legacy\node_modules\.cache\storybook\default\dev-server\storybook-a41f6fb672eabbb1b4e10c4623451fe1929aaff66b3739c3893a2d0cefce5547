{"key": "lastEvents", "content": {"boot": {"body": {"eventType": "boot", "eventId": "P8v7i-Z3-cozQS7zwvJg3", "sessionId": "sWDZFaj-n05JZhunh8Cb-", "payload": {"eventType": "init"}, "context": {"inCI": false, "isTTY": true, "platform": "Windows", "nodeVersion": "22.17.0", "storybookVersion": "9.0.18"}}, "timestamp": 1753283556789}, "init-step": {"body": {"eventType": "init-step", "eventId": "bR4wx09YDVIn2t5t-B2py", "sessionId": "sWDZFaj-n05JZhunh8Cb-", "metadata": {"generatedAt": 1753283695079, "userSince": 1753283557832, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "testPackages": {}, "hasRouterPackage": true, "storybookVersionSpecifier": "9.0.18", "language": "typescript"}, "payload": {"step": "new-user-check", "newUser": true}, "context": {"inCI": false, "isTTY": true, "platform": "Windows", "nodeVersion": "22.17.0", "storybookVersion": "9.0.18", "cliVersion": "9.0.18", "anonymousId": "9dba4b2c8d52daf666b7afbc736caa0f53e65d32db6d14d891bab6e22db1701c"}}, "timestamp": 1753283695668}, "init": {"body": {"eventType": "init", "eventId": "rzvWA3unerKU3FQk1ESy4", "sessionId": "sWDZFaj-n05JZhunh8Cb-", "metadata": {"generatedAt": 1753283695079, "userSince": 1753283557832, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "testPackages": {}, "hasRouterPackage": true, "storybookVersionSpecifier": "9.0.18", "language": "typescript"}, "payload": {"projectType": "REACT", "features": {"dev": true, "docs": true, "test": true, "onboarding": true}, "newUser": true}, "context": {"inCI": false, "isTTY": true, "platform": "Windows", "nodeVersion": "22.17.0", "storybookVersion": "9.0.18", "cliVersion": "9.0.18", "anonymousId": "9dba4b2c8d52daf666b7afbc736caa0f53e65d32db6d14d891bab6e22db1701c"}}, "timestamp": 1753283707767}}}