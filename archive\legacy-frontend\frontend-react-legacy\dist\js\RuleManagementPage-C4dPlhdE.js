var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
import { j as jsxRuntimeExports, u as useQueryClient, b as useQuery, d as useMutation } from "./state-management-CeNIv-64.js";
import { d as useParams, r as reactExports } from "./router-DbSvV1fW.js";
import { c as cn, u as useAppStore } from "./index-BaeIiao7.js";
import { B as Button, P as PrimaryButton, D as DangerButton } from "./Button-CswqCd84.js";
import { S as SearchBox } from "./SearchBox-CwJS6ooP.js";
import { a as api, b as buildQueryParams, h as handleApiError } from "./client-BIlsoe8d.js";
import "./react-vendor-ZA51SWXd.js";
import "./ui-vendor-DgYk2OaC.js";
import "./index-DEw2ppt0.js";
const RuleStatusBadge = ({
  isActive,
  size = "sm"
}) => {
  const sizeClasses = {
    sm: "px-2 py-1 text-xs",
    md: "px-3 py-1.5 text-sm"
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx(
    "span",
    {
      className: cn(
        "inline-flex items-center rounded-full font-medium",
        sizeClasses[size],
        isActive ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
      ),
      children: isActive ? "✅ 激活" : "⏸️ 停用"
    }
  );
};
const RuleTypeDisplay = ({
  ruleType,
  compact = false
}) => {
  const typeMap = {
    "FILENAME_PARSING": {
      name: "文件名解析",
      icon: "📝",
      color: "bg-blue-100 text-blue-800"
    },
    "DATE_TAGGING_FORMAT": {
      name: "日期标签格式化",
      icon: "📅",
      color: "bg-purple-100 text-purple-800"
    },
    "POSTGRESQL_SEARCH": {
      name: "PostgreSQL搜索",
      icon: "🔍",
      color: "bg-orange-100 text-orange-800"
    }
  };
  const typeInfo = typeMap[ruleType] || {
    name: ruleType,
    icon: "⚙️",
    color: "bg-gray-100 text-gray-800"
  };
  if (compact) {
    return /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "flex items-center gap-1", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: typeInfo.icon }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm", children: typeInfo.name })
    ] });
  }
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(
    "span",
    {
      className: cn(
        "inline-flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium",
        typeInfo.color
      ),
      children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: typeInfo.icon }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: typeInfo.name })
      ]
    }
  );
};
const RuleConfigPreview = ({
  config,
  maxLines = 3
}) => {
  const configText = JSON.stringify(config, null, 2);
  const lines = configText.split("\n");
  const truncated = lines.length > maxLines;
  const displayLines = truncated ? lines.slice(0, maxLines) : lines;
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "bg-gray-50 rounded-md p-2 text-xs font-mono", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("pre", { className: "text-gray-700 whitespace-pre-wrap", children: [
    displayLines.join("\n"),
    truncated && "\n..."
  ] }) });
};
const RuleActions = ({
  rule,
  onEdit,
  onToggle,
  onDelete,
  onTest,
  showTest = true
}) => {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity", children: [
    showTest && onTest && /* @__PURE__ */ jsxRuntimeExports.jsx(
      Button,
      {
        onClick: () => onTest(rule),
        variant: "ghost",
        size: "xs",
        title: "测试规则",
        children: "🧪"
      }
    ),
    onEdit && /* @__PURE__ */ jsxRuntimeExports.jsx(
      Button,
      {
        onClick: () => onEdit(rule),
        variant: "ghost",
        size: "xs",
        title: "编辑规则",
        children: "✏️"
      }
    ),
    onToggle && /* @__PURE__ */ jsxRuntimeExports.jsx(
      Button,
      {
        onClick: () => onToggle(rule),
        variant: "ghost",
        size: "xs",
        title: rule.is_active ? "停用规则" : "激活规则",
        children: rule.is_active ? "⏸️" : "▶️"
      }
    ),
    onDelete && /* @__PURE__ */ jsxRuntimeExports.jsx(
      Button,
      {
        onClick: () => onDelete(rule),
        variant: "ghost",
        size: "xs",
        title: "删除规则",
        className: "text-red-600 hover:text-red-700",
        children: "🗑️"
      }
    )
  ] });
};
const RuleItem = ({
  rule,
  selected = false,
  showConfig = true,
  showActions = true,
  onSelect,
  onEdit,
  onToggle,
  onDelete,
  onTest,
  className
}) => {
  const handleClick = () => {
    onSelect == null ? void 0 : onSelect(rule);
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(
    "div",
    {
      className: cn(
        "group p-4 border rounded-lg cursor-pointer transition-all duration-200",
        "hover:shadow-md hover:border-gray-300",
        selected ? "border-blue-500 bg-blue-50 shadow-md" : "border-gray-200 bg-white",
        className
      ),
      onClick: handleClick,
      children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-start justify-between mb-3", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex-1 min-w-0", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2 mb-2", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(RuleTypeDisplay, { ruleType: rule.rule_type }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(RuleStatusBadge, { isActive: rule.is_active })
            ] }),
            rule.description && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-sm text-gray-600 truncate", children: rule.description })
          ] }),
          showActions && /* @__PURE__ */ jsxRuntimeExports.jsx(
            RuleActions,
            {
              rule,
              onEdit,
              onToggle,
              onDelete,
              onTest
            }
          )
        ] }),
        showConfig && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mb-3", children: /* @__PURE__ */ jsxRuntimeExports.jsx(RuleConfigPreview, { config: rule.rule_config }) }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between text-xs text-gray-500", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { children: [
            "ID: ",
            rule.id
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { children: [
            "创建: ",
            new Date(rule.created_at).toLocaleDateString()
          ] })
        ] })
      ]
    }
  );
};
class RulesApi {
  // 获取支持的规则类型
  getRuleTypes() {
    return __async(this, null, function* () {
      return api.get("/api/v1/rule-types");
    });
  }
  // 验证规则配置
  validateRuleConfig(request) {
    return __async(this, null, function* () {
      return api.post("/api/v1/rule-config/validate", request);
    });
  }
  // 创建案例规则
  createRule(caseId, rule) {
    return __async(this, null, function* () {
      return api.post(`/api/v1/cases/${caseId}/rules`, rule);
    });
  }
  // 获取案例规则列表
  getRules(caseId, options) {
    return __async(this, null, function* () {
      const params = buildQueryParams(options || {});
      const url = `/api/v1/cases/${caseId}/rules${params ? `?${params}` : ""}`;
      return api.get(url);
    });
  }
  // 获取规则详情
  getRule(ruleId) {
    return __async(this, null, function* () {
      return api.get(`/api/v1/rules/${ruleId}`);
    });
  }
  // 更新规则
  updateRule(ruleId, update) {
    return __async(this, null, function* () {
      return api.put(`/api/v1/rules/${ruleId}`, update);
    });
  }
  // 删除规则
  deleteRule(ruleId) {
    return __async(this, null, function* () {
      return api.delete(`/api/v1/rules/${ruleId}`);
    });
  }
  // 测试规则
  testRule(ruleId, testFilename) {
    return __async(this, null, function* () {
      const params = buildQueryParams({ test_filename: testFilename });
      return api.post(`/api/v1/rules/${ruleId}/test?${params}`);
    });
  }
  // 批量操作规则
  batchToggleRules(ruleIds, isActive) {
    return __async(this, null, function* () {
      const promises = ruleIds.map(
        (ruleId) => this.updateRule(ruleId, { is_active: isActive })
      );
      try {
        const results = yield Promise.allSettled(promises);
        return results.map((result, index) => {
          if (result.status === "fulfilled") {
            return {
              success: true,
              message: `规则 ${ruleIds[index]} 已${isActive ? "激活" : "停用"}`
            };
          } else {
            return {
              success: false,
              message: `规则 ${ruleIds[index]} 操作失败: ${result.reason.message}`
            };
          }
        });
      } catch (error) {
        throw error;
      }
    });
  }
  // 批量删除规则
  batchDeleteRules(ruleIds) {
    return __async(this, null, function* () {
      const promises = ruleIds.map((ruleId) => this.deleteRule(ruleId));
      try {
        const results = yield Promise.allSettled(promises);
        return results.map((result, index) => {
          if (result.status === "fulfilled") {
            return result.value;
          } else {
            return {
              success: false,
              message: `删除规则 ${ruleIds[index]} 失败: ${result.reason.message}`
            };
          }
        });
      } catch (error) {
        throw error;
      }
    });
  }
}
const rulesApi = new RulesApi();
const RuleManagementPage = () => {
  const { caseId } = useParams();
  const { addNotification } = useAppStore();
  const queryClient = useQueryClient();
  const [selectedRule, setSelectedRule] = reactExports.useState(null);
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [filterActive, setFilterActive] = reactExports.useState("all");
  const [showCreateModal, setShowCreateModal] = reactExports.useState(false);
  const {
    data: rules = [],
    isLoading: rulesLoading,
    error: rulesError,
    refetch: refetchRules
  } = useQuery({
    queryKey: ["rules", caseId, filterActive],
    queryFn: () => rulesApi.getRules(Number(caseId), {
      is_active: filterActive === "all" ? void 0 : filterActive === "active"
    }),
    enabled: !!caseId,
    staleTime: 3e4
    // 30秒内不重新请求
  });
  const updateRuleMutation = useMutation({
    mutationFn: ({ ruleId, update }) => rulesApi.updateRule(ruleId, update),
    onSuccess: (updatedRule) => {
      queryClient.invalidateQueries({ queryKey: ["rules", caseId] });
      addNotification({
        type: "success",
        title: "规则更新成功",
        message: `规则已${updatedRule.is_active ? "激活" : "停用"}`
      });
    },
    onError: (error) => {
      addNotification({
        type: "error",
        title: "规则更新失败",
        message: handleApiError(error)
      });
    }
  });
  const deleteRuleMutation = useMutation({
    mutationFn: (ruleId) => rulesApi.deleteRule(ruleId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["rules", caseId] });
      setSelectedRule(null);
      addNotification({
        type: "success",
        title: "规则删除成功",
        message: "规则已成功删除"
      });
    },
    onError: (error) => {
      addNotification({
        type: "error",
        title: "规则删除失败",
        message: handleApiError(error)
      });
    }
  });
  const testRuleMutation = useMutation({
    mutationFn: ({ ruleId, filename }) => rulesApi.testRule(ruleId, filename),
    onSuccess: (result) => {
      addNotification({
        type: result.success ? "success" : "warning",
        title: "规则测试完成",
        message: `测试文件: ${result.test_filename}，结果: ${result.success ? "成功" : "失败"}`
      });
    },
    onError: (error) => {
      addNotification({
        type: "error",
        title: "规则测试失败",
        message: handleApiError(error)
      });
    }
  });
  const filteredRules = rules.filter((rule) => {
    var _a;
    if (!searchTerm) return true;
    const searchLower = searchTerm.toLowerCase();
    return rule.rule_type.toLowerCase().includes(searchLower) || ((_a = rule.description) == null ? void 0 : _a.toLowerCase().includes(searchLower));
  });
  const handleRuleSelect = (rule) => {
    setSelectedRule(rule);
  };
  const handleRuleEdit = (rule) => {
    addNotification({
      type: "info",
      title: "编辑规则",
      message: `编辑规则: ${rule.rule_type} (ID: ${rule.id})`
    });
  };
  const handleRuleToggle = (rule) => {
    updateRuleMutation.mutate({
      ruleId: rule.id,
      update: { is_active: !rule.is_active }
    });
  };
  const handleRuleDelete = (rule) => {
    if (confirm(`确定要删除规则 "${rule.rule_type}" 吗？`)) {
      deleteRuleMutation.mutate(rule.id);
    }
  };
  const handleRuleTest = (rule) => {
    const testFilename = prompt("请输入测试文件名:", "test_20250726_001.jpg");
    if (testFilename) {
      testRuleMutation.mutate({
        ruleId: rule.id,
        filename: testFilename
      });
    }
  };
  const handleCreateRule = () => {
    setShowCreateModal(true);
    addNotification({
      type: "info",
      title: "创建规则",
      message: "打开规则创建界面"
    });
  };
  reactExports.useEffect(() => {
    if (rulesError) {
      addNotification({
        type: "error",
        title: "加载规则失败",
        message: handleApiError(rulesError)
      });
    }
  }, [rulesError, addNotification]);
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "h-screen flex bg-gray-50", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "w-1/2 border-r border-gray-200 bg-white", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-4 border-b border-gray-200", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between mb-4", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("h1", { className: "text-xl font-semibold text-gray-900", children: "规则管理" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(PrimaryButton, { onClick: handleCreateRule, size: "sm", children: "➕ 创建规则" })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-3", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            SearchBox,
            {
              value: searchTerm,
              onChange: setSearchTerm,
              placeholder: "搜索规则...",
              size: "sm"
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs(
              Button,
              {
                onClick: () => setFilterActive("all"),
                variant: filterActive === "all" ? "primary" : "ghost",
                size: "xs",
                disabled: rulesLoading,
                children: [
                  "全部 (",
                  rulesLoading ? "..." : rules.length,
                  ")"
                ]
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsxs(
              Button,
              {
                onClick: () => setFilterActive("active"),
                variant: filterActive === "active" ? "primary" : "ghost",
                size: "xs",
                disabled: rulesLoading,
                children: [
                  "激活 (",
                  rulesLoading ? "..." : rules.filter((r) => r.is_active).length,
                  ")"
                ]
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsxs(
              Button,
              {
                onClick: () => setFilterActive("inactive"),
                variant: filterActive === "inactive" ? "primary" : "ghost",
                size: "xs",
                disabled: rulesLoading,
                children: [
                  "停用 (",
                  rulesLoading ? "..." : rules.filter((r) => !r.is_active).length,
                  ")"
                ]
              }
            )
          ] })
        ] })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 overflow-y-auto p-4", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "space-y-3", children: rulesLoading ? /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center py-8 text-gray-500", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-4xl mb-2", children: "⏳" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("p", { children: "加载规则中..." })
      ] }) : rulesError ? /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center py-8 text-red-500", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-4xl mb-2", children: "❌" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("p", { children: "加载规则失败" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Button,
          {
            onClick: () => refetchRules(),
            variant: "ghost",
            size: "sm",
            className: "mt-2",
            children: "重试"
          }
        )
      ] }) : filteredRules.length > 0 ? filteredRules.map((rule) => /* @__PURE__ */ jsxRuntimeExports.jsx(
        RuleItem,
        {
          rule,
          selected: (selectedRule == null ? void 0 : selectedRule.id) === rule.id,
          onSelect: handleRuleSelect,
          onEdit: handleRuleEdit,
          onToggle: handleRuleToggle,
          onDelete: handleRuleDelete,
          onTest: handleRuleTest
        },
        rule.id
      )) : /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center py-8 text-gray-500", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-4xl mb-2", children: "🔍" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("p", { children: searchTerm ? "没有找到匹配的规则" : "暂无规则" }),
        !searchTerm && /* @__PURE__ */ jsxRuntimeExports.jsx(
          Button,
          {
            onClick: handleCreateRule,
            variant: "primary",
            size: "sm",
            className: "mt-2",
            children: "创建第一个规则"
          }
        )
      ] }) }) })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-1/2 bg-white", children: selectedRule ? /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "h-full flex flex-col", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-4 border-b border-gray-200", children: /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-lg font-semibold text-gray-900", children: "规则详情" }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 overflow-y-auto p-4", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-6", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-sm font-medium text-gray-900 mb-3", children: "基本信息" }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "bg-gray-50 rounded-lg p-4 space-y-3", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm text-gray-600", children: "规则ID:" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm font-medium", children: selectedRule.id })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm text-gray-600", children: "规则类型:" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm font-medium", children: selectedRule.rule_type })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm text-gray-600", children: "状态:" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: cn(
                "text-sm font-medium",
                selectedRule.is_active ? "text-green-600" : "text-gray-600"
              ), children: selectedRule.is_active ? "激活" : "停用" })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm text-gray-600", children: "创建时间:" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm font-medium", children: new Date(selectedRule.created_at).toLocaleString() })
            ] })
          ] })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-sm font-medium text-gray-900 mb-3", children: "规则配置" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "bg-gray-50 rounded-lg p-4", children: /* @__PURE__ */ jsxRuntimeExports.jsx("pre", { className: "text-xs font-mono text-gray-700 whitespace-pre-wrap", children: JSON.stringify(selectedRule.rule_config, null, 2) }) })
        ] }),
        selectedRule.description && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-sm font-medium text-gray-900 mb-3", children: "描述" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-sm text-gray-600 bg-gray-50 rounded-lg p-4", children: selectedRule.description })
        ] })
      ] }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-4 border-t border-gray-200", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          PrimaryButton,
          {
            onClick: () => handleRuleEdit(selectedRule),
            size: "sm",
            disabled: updateRuleMutation.isPending || deleteRuleMutation.isPending,
            children: "编辑规则"
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Button,
          {
            onClick: () => handleRuleToggle(selectedRule),
            variant: selectedRule.is_active ? "warning" : "success",
            size: "sm",
            loading: updateRuleMutation.isPending,
            disabled: deleteRuleMutation.isPending,
            children: selectedRule.is_active ? "停用" : "激活"
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Button,
          {
            onClick: () => handleRuleTest(selectedRule),
            variant: "ghost",
            size: "sm",
            loading: testRuleMutation.isPending,
            disabled: updateRuleMutation.isPending || deleteRuleMutation.isPending,
            children: "测试规则"
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          DangerButton,
          {
            onClick: () => handleRuleDelete(selectedRule),
            size: "sm",
            loading: deleteRuleMutation.isPending,
            disabled: updateRuleMutation.isPending || testRuleMutation.isPending,
            children: "删除规则"
          }
        )
      ] }) })
    ] }) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "h-full flex items-center justify-center text-gray-500", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-4xl mb-2", children: "⚙️" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("p", { children: "选择一个规则查看详情" })
    ] }) }) })
  ] });
};
export {
  RuleManagementPage as default
};
