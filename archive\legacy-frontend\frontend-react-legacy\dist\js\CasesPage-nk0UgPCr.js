var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
import { j as jsxRuntimeExports } from "./state-management-CeNIv-64.js";
import { r as reactExports } from "./router-DbSvV1fW.js";
import { C as CaseCard } from "./CaseCard-DFH_m0iv.js";
import { B as Button } from "./Button-CswqCd84.js";
import { M as Modal } from "./caseService-rs6u721W.js";
import { u as useCases, a as useCreateCase, b as useUpdateCase, c as useDeleteCase } from "./useCases-mlPph2ta.js";
import { u as useAppStore } from "./index-BaeIiao7.js";
import "./react-vendor-ZA51SWXd.js";
import "./index-DEw2ppt0.js";
import "./ui-vendor-DgYk2OaC.js";
const CasesPage = () => {
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [showCreateModal, setShowCreateModal] = reactExports.useState(false);
  const [editingCase, setEditingCase] = reactExports.useState(null);
  const [formData, setFormData] = reactExports.useState({ name: "", description: "" });
  const { addNotification } = useAppStore();
  const { data: cases = [], isLoading, error, refetch } = useCases();
  const createCase = useCreateCase();
  const updateCase = useUpdateCase();
  const deleteCase = useDeleteCase();
  const filteredCases = reactExports.useMemo(() => {
    if (!searchTerm) return cases;
    return cases.filter(
      (caseItem) => caseItem.case_name.toLowerCase().includes(searchTerm.toLowerCase()) || caseItem.description && caseItem.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [cases, searchTerm]);
  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };
  const handleRefresh = () => {
    refetch();
  };
  const handleCreateCase = () => {
    setEditingCase(null);
    setFormData({ name: "", description: "" });
    setShowCreateModal(true);
  };
  const handleEditCase = (caseItem) => {
    setEditingCase(caseItem);
    setFormData({
      name: caseItem.case_name,
      description: caseItem.description || ""
    });
    setShowCreateModal(true);
  };
  const handleDeleteCase = (caseItem) => __async(null, null, function* () {
    if (window.confirm(`确定要删除案例 "${caseItem.name}" 吗？此操作不可撤销。`)) {
      try {
        yield deleteCase.mutateAsync(caseItem.id);
      } catch (error2) {
        console.error("删除案例失败:", error2);
      }
    }
  });
  const handleSubmit = (e) => __async(null, null, function* () {
    e.preventDefault();
    if (!formData.name.trim()) {
      addNotification({
        type: "error",
        title: "验证失败",
        message: "案例名称不能为空"
      });
      return;
    }
    try {
      if (editingCase) {
        yield updateCase.mutateAsync({
          id: editingCase.id,
          data: formData
        });
      } else {
        yield createCase.mutateAsync(formData);
      }
      setShowCreateModal(false);
      setFormData({ name: "", description: "" });
      setEditingCase(null);
    } catch (error2) {
      console.error("保存案例失败:", error2);
    }
  });
  const handleCloseModal = () => {
    setShowCreateModal(false);
    setFormData({ name: "", description: "" });
    setEditingCase(null);
  };
  const searchIcon = /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-5 h-5 text-gray-400", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" }) });
  if (error) {
    return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "page active h-full flex flex-col", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-6 border-b border-secondary-200 bg-white", children: /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-2xl font-bold text-secondary-900", children: "案例管理" }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-red-500 text-6xl mb-4", children: "⚠️" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-lg font-medium text-gray-900 mb-2", children: "加载失败" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-600 mb-4", children: "无法加载案例列表" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { onClick: handleRefresh, children: "重试" })
      ] }) })
    ] });
  }
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "page active h-full flex flex-col", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-6 border-b border-secondary-200 bg-white", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-2xl font-bold text-secondary-900", children: "案例管理" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-3", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Input,
          {
            type: "text",
            placeholder: "搜索案例...",
            value: searchTerm,
            onChange: handleSearch,
            leftIcon: searchIcon,
            className: "w-64"
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { variant: "secondary", onClick: handleRefresh, children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-5 h-5", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" }) }) }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { onClick: handleCreateCase, children: "新建案例" })
      ] })
    ] }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 p-6 overflow-auto", children: isLoading ? /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center py-8", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "mt-2 text-secondary-600", children: "加载中..." })
    ] }) : filteredCases.length === 0 ? /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center py-12", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-gray-400 text-6xl mb-4", children: "📁" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-lg font-medium text-gray-900 mb-2", children: searchTerm ? "没有找到匹配的案例" : "暂无案例" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-500 mb-4", children: searchTerm ? "尝试使用不同的关键词搜索" : "创建您的第一个案例来开始使用" }),
      !searchTerm && /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { onClick: handleCreateCase, children: "创建第一个案例" })
    ] }) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6", children: filteredCases.map((caseItem) => /* @__PURE__ */ jsxRuntimeExports.jsx(
      CaseCard,
      {
        case: caseItem,
        onEdit: handleEditCase,
        onDelete: handleDeleteCase
      },
      caseItem.id
    )) }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      Modal,
      {
        isOpen: showCreateModal,
        onClose: handleCloseModal,
        title: editingCase ? "编辑案例" : "新建案例",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("form", { onSubmit: handleSubmit, className: "space-y-4", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            Input,
            {
              label: "案例名称",
              type: "text",
              value: formData.name,
              onChange: (e) => setFormData(__spreadProps(__spreadValues({}, formData), { name: e.target.value })),
              placeholder: "请输入案例名称",
              required: true
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "案例描述" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "textarea",
              {
                value: formData.description,
                onChange: (e) => setFormData(__spreadProps(__spreadValues({}, formData), { description: e.target.value })),
                placeholder: "请输入案例描述（可选）",
                rows: 3,
                className: "input resize-none"
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-end space-x-3 pt-4", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { type: "button", variant: "outline", onClick: handleCloseModal, children: "取消" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              Button,
              {
                type: "submit",
                loading: createCase.isLoading || updateCase.isLoading,
                children: editingCase ? "保存" : "创建"
              }
            )
          ] })
        ] })
      }
    )
  ] });
};
export {
  CasesPage as default
};
