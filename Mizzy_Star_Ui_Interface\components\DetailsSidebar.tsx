import React from 'react';
import { Download, ExternalLink, Tag, Calendar, Camera, Palette, FileText, Image, BarChart3, Share2, Edit3, Trash2, <PERSON><PERSON>, Folder<PERSON><PERSON> } from 'lucide-react';
import { But<PERSON> } from './ui/button';
import { Badge } from './ui/badge';
import { Separator } from './ui/separator';
import { ImageWithFallback } from './figma/ImageWithFallback';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from './ui/dropdown-menu';

interface DetailsSidebarProps {
  selectedImages: string[];
  selectedTags: string[];
}

// 模拟图像详细信息
const imageDetails = {
  '1': {
    id: '1',
    filename: '街头摄影_001.jpg',
    src: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=300&fit=crop',
    size: '2.4MB',
    dimensions: '1920×1080',
    type: 'JPEG',
    created: '2024-03-15 14:30:22',
    modified: '2024-03-15 14:30:22',
    camera: 'Canon EOS R5',
    lens: 'RF 24-70mm f/2.8L IS USM',
    aperture: 'f/2.8',
    shutter: '1/125s',
    iso: 'ISO 400',
    focalLength: '35mm',
    tags: ['街头摄影', '人物', '黑白', '高质量', '构图良好'],
    description: '在繁忙的城市街道上捕捉到的瞬间，展现了现代都市生活的节奏感。',
    histogram: [0.2, 0.4, 0.7, 0.5, 0.3, 0.6, 0.8, 0.4, 0.2, 0.1],
    relatedTags: ['城市', '街头', '纪实', '摄影', '黑白摄影']
  },
  '2': {
    id: '2',
    filename: '建筑摄影_002.jpg',
    src: 'https://images.unsplash.com/photo-1497436072909-f5e4be1453c1?w=400&h=300&fit=crop',
    size: '3.1MB',
    dimensions: '2560×1440',
    type: 'JPEG',
    created: '2024-03-14 09:15:33',
    modified: '2024-03-14 09:15:33',
    camera: 'Sony A7R IV',
    lens: 'FE 16-35mm f/2.8 GM',
    aperture: 'f/4.0',
    shutter: '1/60s',
    iso: 'ISO 200',
    focalLength: '24mm',
    tags: ['建筑', '现代', '清晰度优秀', '构图良好'],
    description: '现代建筑的结构美学，展现了城市发展的时代特征。',
    histogram: [0.1, 0.3, 0.6, 0.8, 0.9, 0.7, 0.4, 0.2, 0.1, 0.05],
    relatedTags: ['现代', '设计', '结构', '城市', '几何']
  }
};

const tagDetails: Record<string, any> = {
  '街头摄影': {
    name: '街头摄影',
    count: 12,
    exampleImage: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=300&h=200&fit=crop',
    relatedTags: ['城市', '人物', '纪实', '现代', '生活']
  },
  '建筑': {
    name: '建筑',
    count: 23,
    exampleImage: 'https://images.unsplash.com/photo-1497436072909-f5e4be1453c1?w=300&h=200&fit=crop',
    relatedTags: ['现代', '设计', '结构', '城市', '几何']
  },
  '人物': {
    name: '人物',
    count: 8,
    exampleImage: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop',
    relatedTags: ['肖像', '表情', '情感', '人像', '专业']
  }
};

export const DetailsSidebar: React.FC<DetailsSidebarProps> = ({
  selectedImages,
  selectedTags,
}) => {
  // 情景1：选择单张图片
  if (selectedImages.length === 1) {
    const imageId = selectedImages[0];
    const image = imageDetails[imageId as keyof typeof imageDetails];
    
    if (!image) return (
      <div className="h-full flex items-center justify-center" style={{ color: 'var(--mizzy-icon)' }}>
        图像信息加载中...
      </div>
    );

    return (
      <div className="h-full overflow-auto" style={{ color: 'var(--mizzy-content)' }}>
        <div className="p-4">
          {/* 缩略图 */}
          <div className="mb-4">
            <div className="aspect-video rounded overflow-hidden border" style={{ borderColor: 'var(--mizzy-border)' }}>
              <ImageWithFallback
                src={image.src}
                alt={image.filename}
                className="w-full h-full object-cover"
              />
            </div>
          </div>

          {/* 基本信息 */}
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-medium truncate" style={{ color: 'var(--mizzy-content)' }}>
                {image.filename}
              </h3>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <Share2 className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem>
                    <Download className="h-4 w-4 mr-2" />
                    导出
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <ExternalLink className="h-4 w-4 mr-2" />
                    在其他应用中打开
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <FolderOpen className="h-4 w-4 mr-2" />
                    打开文件所在位置
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span style={{ color: 'var(--mizzy-icon)' }}>文件大小:</span>
                <span>{image.size}</span>
              </div>
              <div className="flex justify-between">
                <span style={{ color: 'var(--mizzy-icon)' }}>尺寸:</span>
                <span>{image.dimensions}</span>
              </div>
              <div className="flex justify-between">
                <span style={{ color: 'var(--mizzy-icon)' }}>格式:</span>
                <span>{image.type}</span>
              </div>
              <div className="flex justify-between">
                <span style={{ color: 'var(--mizzy-icon)' }}>创建时间:</span>
                <span>{image.created}</span>
              </div>
            </div>
          </div>

          <Separator className="my-4" style={{ background: 'var(--mizzy-border)' }} />

          {/* 相机信息 */}
          <div className="mb-4">
            <h4 className="font-medium mb-2 flex items-center gap-2" style={{ color: 'var(--mizzy-title)' }}>
              <Camera className="h-4 w-4" />
              相机信息
            </h4>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span style={{ color: 'var(--mizzy-icon)' }}>相机:</span>
                <span>{image.camera}</span>
              </div>
              <div className="flex justify-between">
                <span style={{ color: 'var(--mizzy-icon)' }}>镜头:</span>
                <span>{image.lens}</span>
              </div>
              <div className="flex justify-between">
                <span style={{ color: 'var(--mizzy-icon)' }}>光圈:</span>
                <span>{image.aperture}</span>
              </div>
              <div className="flex justify-between">
                <span style={{ color: 'var(--mizzy-icon)' }}>快门:</span>
                <span>{image.shutter}</span>
              </div>
              <div className="flex justify-between">
                <span style={{ color: 'var(--mizzy-icon)' }}>ISO:</span>
                <span>{image.iso}</span>
              </div>
              <div className="flex justify-between">
                <span style={{ color: 'var(--mizzy-icon)' }}>焦距:</span>
                <span>{image.focalLength}</span>
              </div>
            </div>
          </div>

          <Separator className="my-4" style={{ background: 'var(--mizzy-border)' }} />

          {/* 直方图 */}
          <div className="mb-4">
            <h4 className="font-medium mb-2 flex items-center gap-2" style={{ color: 'var(--mizzy-title)' }}>
              <BarChart3 className="h-4 w-4" />
              直方图
            </h4>
            <div className="h-20 bg-gray-800 rounded p-2">
              <div className="flex items-end justify-between h-full">
                {image.histogram.map((value, index) => (
                  <div
                    key={index}
                    className="bg-blue-500 rounded-sm"
                    style={{
                      width: '8px',
                      height: `${value * 100}%`,
                      background: 'var(--mizzy-highlight)'
                    }}
                  />
                ))}
              </div>
            </div>
          </div>

          <Separator className="my-4" style={{ background: 'var(--mizzy-border)' }} />

          {/* 标签 */}
          <div className="mb-4">
            <h4 className="font-medium mb-2 flex items-center gap-2" style={{ color: 'var(--mizzy-title)' }}>
              <Tag className="h-4 w-4" />
              标签
            </h4>
            <div className="flex flex-wrap gap-1">
              {image.tags.map((tag, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>

          {/* 相关标签 */}
          <div className="mb-4">
            <h4 className="font-medium mb-2" style={{ color: 'var(--mizzy-title)' }}>
              相关标签
            </h4>
            <div className="flex flex-wrap gap-1">
              {image.relatedTags.map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 情景2：选择多张图片
  if (selectedImages.length > 1) {
    const firstImageId = selectedImages[0];
    const firstImage = imageDetails[firstImageId as keyof typeof imageDetails];
    
    return (
      <div className="h-full overflow-auto" style={{ color: 'var(--mizzy-content)' }}>
        <div className="p-4">
          <div className="mb-4">
            <h3 className="font-medium mb-2" style={{ color: 'var(--mizzy-content)' }}>
              已选择 {selectedImages.length} 张图片
            </h3>
            {firstImage && (
              <div className="aspect-video rounded overflow-hidden border mb-4" style={{ borderColor: 'var(--mizzy-border)' }}>
                <ImageWithFallback
                  src={firstImage.src}
                  alt={firstImage.filename}
                  className="w-full h-full object-cover"
                />
              </div>
            )}
          </div>

          <Separator className="my-4" style={{ background: 'var(--mizzy-border)' }} />

          {/* 批量操作 */}
          <div className="mb-4">
            <h4 className="font-medium mb-2" style={{ color: 'var(--mizzy-title)' }}>
              批量操作
            </h4>
            <div className="space-y-2">
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Download className="h-4 w-4 mr-2" />
                导出选中图片
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Tag className="h-4 w-4 mr-2" />
                批量添加标签
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Copy className="h-4 w-4 mr-2" />
                复制到剪贴簿
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 情景3：选择单个原子标签
  if (selectedTags.length === 1) {
    const tagName = selectedTags[0].split('(')[0];
    const tagDetail = tagDetails[tagName];
    
    if (!tagDetail) return (
      <div className="h-full flex items-center justify-center" style={{ color: 'var(--mizzy-icon)' }}>
        标签信息加载中...
      </div>
    );

    return (
      <div className="h-full overflow-auto" style={{ color: 'var(--mizzy-content)' }}>
        <div className="p-4">
          {/* 示例图缩略图 */}
          <div className="mb-4">
            <div className="aspect-video rounded overflow-hidden border" style={{ borderColor: 'var(--mizzy-border)' }}>
              <ImageWithFallback
                src={tagDetail.exampleImage}
                alt={tagDetail.name}
                className="w-full h-full object-cover"
              />
            </div>
          </div>

          {/* 标签信息 */}
          <div className="mb-4">
            <h3 className="font-medium mb-2" style={{ color: 'var(--mizzy-content)' }}>
              {tagDetail.name}
            </h3>
            <div className="text-sm">
              <div className="flex justify-between mb-2">
                <span style={{ color: 'var(--mizzy-icon)' }}>包含图像数量:</span>
                <span>{tagDetail.count}</span>
              </div>
            </div>
          </div>

          <Separator className="my-4" style={{ background: 'var(--mizzy-border)' }} />

          {/* 相关标签 */}
          <div className="mb-4">
            <h4 className="font-medium mb-2" style={{ color: 'var(--mizzy-title)' }}>
              相关标签
            </h4>
            <div className="flex flex-wrap gap-1">
              {tagDetail.relatedTags.map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 情景4：选择多个原子标签
  if (selectedTags.length > 1) {
    const totalCount = selectedTags.reduce((sum, tag) => {
      const count = parseInt(tag.match(/\((\d+)\)/)?.[1] || '0');
      return sum + count;
    }, 0);

    return (
      <div className="h-full overflow-auto" style={{ color: 'var(--mizzy-content)' }}>
        <div className="p-4">
          <div className="mb-4">
            <h3 className="font-medium mb-2" style={{ color: 'var(--mizzy-content)' }}>
              已选择 {selectedTags.length} 个标签
            </h3>
            <div className="text-sm">
              <div className="flex justify-between">
                <span style={{ color: 'var(--mizzy-icon)' }}>总图像数量:</span>
                <span>{totalCount}</span>
              </div>
            </div>
          </div>

          <Separator className="my-4" style={{ background: 'var(--mizzy-border)' }} />

          {/* 选中的标签 */}
          <div className="mb-4">
            <h4 className="font-medium mb-2" style={{ color: 'var(--mizzy-title)' }}>
              选中的标签
            </h4>
            <div className="flex flex-wrap gap-1">
              {selectedTags.map((tag, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 情景5：选择一个标签单元
  if (selectedTags.length === 0) {
    return (
      <div className="h-full flex items-center justify-center" style={{ color: 'var(--mizzy-icon)' }}>
        <div className="text-center">
          <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>选择图像或标签查看详细信息</p>
        </div>
      </div>
    );
  }

  return null;
};