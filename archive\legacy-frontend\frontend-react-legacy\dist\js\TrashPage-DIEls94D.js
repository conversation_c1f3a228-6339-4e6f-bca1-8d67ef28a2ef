var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
import { j as jsxRuntimeExports } from "./state-management-CeNIv-64.js";
import { r as reactExports } from "./router-DbSvV1fW.js";
import { C as CaseCard } from "./CaseCard-DFH_m0iv.js";
import { S as SearchBox } from "./SearchBox-CwJS6ooP.js";
import { B as Button } from "./Button-CswqCd84.js";
import { u as useAppStore } from "./index-BaeIiao7.js";
import "./react-vendor-ZA51SWXd.js";
import "./ui-vendor-DgYk2OaC.js";
const TrashPage = () => {
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [trashCases, setTrashCases] = reactExports.useState([]);
  const [isLoading, setIsLoading] = reactExports.useState(false);
  const { addNotification } = useAppStore();
  const filteredCases = reactExports.useMemo(() => {
    if (!searchTerm) return trashCases;
    return trashCases.filter(
      (caseItem) => caseItem.name.toLowerCase().includes(searchTerm.toLowerCase()) || caseItem.description && caseItem.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [trashCases, searchTerm]);
  const handleSearch = (value) => {
    setSearchTerm(value);
  };
  const handleRefresh = () => __async(null, null, function* () {
    setIsLoading(true);
    try {
      const response = yield fetch("/api/v1/trash/");
      if (response.ok) {
        const data = yield response.json();
        setTrashCases(data);
      } else {
        throw new Error("获取回收站数据失败");
      }
    } catch (error) {
      console.error("获取回收站数据失败:", error);
      addNotification({
        type: "error",
        title: "加载失败",
        message: "无法获取回收站数据"
      });
    } finally {
      setIsLoading(false);
    }
  });
  const handleRestoreCase = (caseItem) => __async(null, null, function* () {
    if (window.confirm(`确定要恢复案例 "${caseItem.name}" 吗？`)) {
      try {
        const response = yield fetch(`/api/v1/trash/${caseItem.id}/restore`, {
          method: "POST"
        });
        if (response.ok) {
          setTrashCases((prev) => prev.filter((c) => c.id !== caseItem.id));
          addNotification({
            type: "success",
            title: "恢复成功",
            message: `案例 "${caseItem.name}" 已恢复`
          });
        } else {
          throw new Error("恢复失败");
        }
      } catch (error) {
        console.error("恢复案例失败:", error);
        addNotification({
          type: "error",
          title: "恢复失败",
          message: "无法恢复案例"
        });
      }
    }
  });
  const handlePermanentDelete = (caseItem) => __async(null, null, function* () {
    if (window.confirm(`确定要永久删除案例 "${caseItem.name}" 吗？此操作不可撤销！`)) {
      try {
        const response = yield fetch(`/api/v1/trash/${caseItem.id}`, {
          method: "DELETE"
        });
        if (response.ok) {
          setTrashCases((prev) => prev.filter((c) => c.id !== caseItem.id));
          addNotification({
            type: "success",
            title: "删除成功",
            message: `案例 "${caseItem.name}" 已永久删除`
          });
        } else {
          throw new Error("删除失败");
        }
      } catch (error) {
        console.error("永久删除案例失败:", error);
        addNotification({
          type: "error",
          title: "删除失败",
          message: "无法永久删除案例"
        });
      }
    }
  });
  const handleClearTrash = () => __async(null, null, function* () {
    if (window.confirm("确定要清空回收站吗？此操作将永久删除所有案例，不可撤销！")) {
      try {
        const response = yield fetch("/api/v1/trash/clear", {
          method: "DELETE"
        });
        if (response.ok) {
          setTrashCases([]);
          addNotification({
            type: "success",
            title: "清空成功",
            message: "回收站已清空"
          });
        } else {
          throw new Error("清空失败");
        }
      } catch (error) {
        console.error("清空回收站失败:", error);
        addNotification({
          type: "error",
          title: "清空失败",
          message: "无法清空回收站"
        });
      }
    }
  });
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "page active h-full flex flex-col", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-6 border-b border-secondary-200 bg-white", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-2xl font-bold text-secondary-900", children: "回收站" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-3", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          SearchBox,
          {
            placeholder: "搜索回收站...",
            value: searchTerm,
            onChange: handleSearch,
            className: "w-64"
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { variant: "secondary", onClick: handleRefresh, children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-5 h-5", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" }) }) }),
        trashCases.length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { variant: "danger", onClick: handleClearTrash, children: "清空回收站" })
      ] })
    ] }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 p-6 overflow-auto", children: isLoading ? /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center py-8", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "mt-2 text-secondary-600", children: "加载中..." })
    ] }) : filteredCases.length === 0 ? /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center py-12", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-gray-400 text-6xl mb-4", children: "🗑️" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-lg font-medium text-gray-900 mb-2", children: searchTerm ? "没有找到匹配的案例" : "回收站为空" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-500", children: searchTerm ? "尝试使用不同的关键词搜索" : "删除的案例会出现在这里" })
    ] }) : /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-4", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-sm text-gray-600 mb-4", children: [
        "找到 ",
        filteredCases.length,
        " 个已删除的案例"
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6", children: filteredCases.map((caseItem) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "relative", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          CaseCard,
          {
            case: caseItem,
            className: "opacity-75"
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "absolute top-2 right-2 flex space-x-1", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "button",
            {
              onClick: () => handleRestoreCase(caseItem),
              className: "p-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors",
              title: "恢复案例",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-4 h-4", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" }) })
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "button",
            {
              onClick: () => handlePermanentDelete(caseItem),
              className: "p-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors",
              title: "永久删除",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-4 h-4", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M6 18L18 6M6 6l12 12" }) })
            }
          )
        ] })
      ] }, caseItem.id)) })
    ] }) })
  ] });
};
export {
  TrashPage as default
};
