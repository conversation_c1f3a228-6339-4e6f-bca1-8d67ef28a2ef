{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_core", "TRACE_ID", "FILE_NAME_VAR", "createNodeFromNullish", "val", "fn", "t", "nullLiteral", "_default", "exports", "default", "declare", "api", "assertVersion", "makeTrace", "fileNameIdentifier", "line", "column", "fileLineLiteral", "numericLiteral", "fileColumnLiteral", "c", "template", "expression", "ast", "isSourceAttr", "attr", "isJSXAttribute", "name", "visitor", "JSXOpeningElement", "path", "state", "node", "loc", "attributes", "some", "fileNameId", "scope", "generateUidIdentifier", "getProgramParent", "push", "id", "init", "stringLiteral", "filename", "jsxAttribute", "jsxIdentifier", "jsxExpressionContainer", "cloneNode", "start"], "sources": ["../src/index.ts"], "sourcesContent": ["/**\n * This adds {fileName, lineNumber, columnNumber} annotations to JSX tags.\n *\n * NOTE: lineNumber and columnNumber are both 1-based.\n *\n * == JSX Literals ==\n *\n * <sometag />\n *\n * becomes:\n *\n * var __jsxFileName = 'this/file.js';\n * <sometag __source={{fileName: __jsxFileName, lineNumber: 10, columnNumber: 1}}/>\n */\nimport { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t, template } from \"@babel/core\";\n\nconst TRACE_ID = \"__source\";\nconst FILE_NAME_VAR = \"_jsxFileName\";\n\nconst createNodeFromNullish = <T, N extends t.Node>(\n  val: T | null,\n  fn: (val: T) => N,\n): N | t.NullLiteral => (val == null ? t.nullLiteral() : fn(val));\n\ntype State = {\n  fileNameIdentifier: t.Identifier;\n};\nexport default declare<State>(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  function makeTrace(\n    fileNameIdentifier: t.Identifier,\n    { line, column }: { line: number; column: number },\n  ) {\n    const fileLineLiteral = createNodeFromNullish(line, t.numericLiteral);\n    const fileColumnLiteral = createNodeFromNullish(column, c =>\n      // c + 1 to make it 1-based instead of 0-based.\n      t.numericLiteral(c + 1),\n    );\n\n    return template.expression.ast`{\n      fileName: ${fileNameIdentifier},\n      lineNumber: ${fileLineLiteral},\n      columnNumber: ${fileColumnLiteral},\n    }`;\n  }\n\n  const isSourceAttr = (attr: t.Node) =>\n    t.isJSXAttribute(attr) && attr.name.name === TRACE_ID;\n\n  return {\n    name: \"transform-react-jsx-source\",\n    visitor: {\n      JSXOpeningElement(path, state) {\n        const { node } = path;\n        if (\n          // the element was generated and doesn't have location information\n          !node.loc ||\n          // Already has __source\n          path.node.attributes.some(isSourceAttr)\n        ) {\n          return;\n        }\n\n        if (!state.fileNameIdentifier) {\n          const fileNameId = path.scope.generateUidIdentifier(FILE_NAME_VAR);\n          state.fileNameIdentifier = fileNameId;\n\n          path.scope.getProgramParent().push({\n            id: fileNameId,\n            init: t.stringLiteral(state.filename || \"\"),\n          });\n        }\n\n        node.attributes.push(\n          t.jsxAttribute(\n            t.jsxIdentifier(TRACE_ID),\n            t.jsxExpressionContainer(\n              makeTrace(t.cloneNode(state.fileNameIdentifier), node.loc.start),\n            ),\n          ),\n        );\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAcA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAEA,MAAME,QAAQ,GAAG,UAAU;AAC3B,MAAMC,aAAa,GAAG,cAAc;AAEpC,MAAMC,qBAAqB,GAAGA,CAC5BC,GAAa,EACbC,EAAiB,KACMD,GAAG,IAAI,IAAI,GAAGE,WAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,EAAE,CAACD,GAAG,CAAE;AAAC,IAAAI,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAKnD,IAAAC,0BAAO,EAAQC,GAAG,IAAI;EACnCA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EAEtC,SAASC,SAASA,CAChBC,kBAAgC,EAChC;IAAEC,IAAI;IAAEC;EAAyC,CAAC,EAClD;IACA,MAAMC,eAAe,GAAGf,qBAAqB,CAACa,IAAI,EAAEV,WAAC,CAACa,cAAc,CAAC;IACrE,MAAMC,iBAAiB,GAAGjB,qBAAqB,CAACc,MAAM,EAAEI,CAAC,IAEvDf,WAAC,CAACa,cAAc,CAACE,CAAC,GAAG,CAAC,CACxB,CAAC;IAED,OAAOC,cAAQ,CAACC,UAAU,CAACC,GAAG;AAClC,kBAAkBT,kBAAkB;AACpC,oBAAoBG,eAAe;AACnC,sBAAsBE,iBAAiB;AACvC,MAAM;EACJ;EAEA,MAAMK,YAAY,GAAIC,IAAY,IAChCpB,WAAC,CAACqB,cAAc,CAACD,IAAI,CAAC,IAAIA,IAAI,CAACE,IAAI,CAACA,IAAI,KAAK3B,QAAQ;EAEvD,OAAO;IACL2B,IAAI,EAAE,4BAA4B;IAClCC,OAAO,EAAE;MACPC,iBAAiBA,CAACC,IAAI,EAAEC,KAAK,EAAE;QAC7B,MAAM;UAAEC;QAAK,CAAC,GAAGF,IAAI;QACrB,IAEE,CAACE,IAAI,CAACC,GAAG,IAETH,IAAI,CAACE,IAAI,CAACE,UAAU,CAACC,IAAI,CAACX,YAAY,CAAC,EACvC;UACA;QACF;QAEA,IAAI,CAACO,KAAK,CAACjB,kBAAkB,EAAE;UAC7B,MAAMsB,UAAU,GAAGN,IAAI,CAACO,KAAK,CAACC,qBAAqB,CAACrC,aAAa,CAAC;UAClE8B,KAAK,CAACjB,kBAAkB,GAAGsB,UAAU;UAErCN,IAAI,CAACO,KAAK,CAACE,gBAAgB,CAAC,CAAC,CAACC,IAAI,CAAC;YACjCC,EAAE,EAAEL,UAAU;YACdM,IAAI,EAAErC,WAAC,CAACsC,aAAa,CAACZ,KAAK,CAACa,QAAQ,IAAI,EAAE;UAC5C,CAAC,CAAC;QACJ;QAEAZ,IAAI,CAACE,UAAU,CAACM,IAAI,CAClBnC,WAAC,CAACwC,YAAY,CACZxC,WAAC,CAACyC,aAAa,CAAC9C,QAAQ,CAAC,EACzBK,WAAC,CAAC0C,sBAAsB,CACtBlC,SAAS,CAACR,WAAC,CAAC2C,SAAS,CAACjB,KAAK,CAACjB,kBAAkB,CAAC,EAAEkB,IAAI,CAACC,GAAG,CAACgB,KAAK,CACjE,CACF,CACF,CAAC;MACH;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}