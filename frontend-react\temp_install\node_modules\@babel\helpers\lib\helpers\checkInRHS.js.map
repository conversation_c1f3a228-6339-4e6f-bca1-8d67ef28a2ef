{"version": 3, "names": ["_checkInRHS", "value", "Object", "TypeError"], "sources": ["../../src/helpers/checkInRHS.ts"], "sourcesContent": ["/* @minVersion 7.20.5 */\n\nexport default function _checkInRHS(value: unknown) {\n  if (Object(value) !== value) {\n    throw TypeError(\n      \"right-hand side of 'in' should be an object, got \" +\n        (value !== null ? typeof value : \"null\"),\n    );\n  }\n  return value;\n}\n"], "mappings": ";;;;;;AAEe,SAASA,WAAWA,CAACC,KAAc,EAAE;EAClD,IAAIC,MAAM,CAACD,KAAK,CAAC,KAAKA,KAAK,EAAE;IAC3B,MAAME,SAAS,CACb,mDAAmD,IAChDF,KAAK,KAAK,IAAI,GAAG,OAAOA,KAAK,GAAG,MAAM,CAC3C,CAAC;EACH;EACA,OAAOA,KAAK;AACd", "ignoreList": []}