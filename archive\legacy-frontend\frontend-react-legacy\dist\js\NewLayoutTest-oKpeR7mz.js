var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
import { j as jsxRuntimeExports } from "./state-management-CeNIv-64.js";
import { r as reactExports } from "./router-DbSvV1fW.js";
import { c as cn } from "./index-BaeIiao7.js";
import "./react-vendor-ZA51SWXd.js";
import "./ui-vendor-DgYk2OaC.js";
const ResizablePanel = ({
  children,
  direction,
  size,
  onResize,
  onResizeStart,
  onResizeEnd,
  minSize,
  maxSize,
  className,
  resizeHandle,
  unit = "px",
  disabled = false,
  showPreview = true
}) => {
  const [isDragging, setIsDragging] = reactExports.useState(false);
  const [isHovering, setIsHovering] = reactExports.useState(false);
  const [previewSize, setPreviewSize] = reactExports.useState(null);
  const [dragStartPos, setDragStartPos] = reactExports.useState({ x: 0, y: 0 });
  const [dragStartSize, setDragStartSize] = reactExports.useState(0);
  const panelRef = reactExports.useRef(null);
  const resizeHandleRef = reactExports.useRef(null);
  const animationFrameRef = reactExports.useRef();
  const getContainerSize = reactExports.useCallback(() => {
    var _a;
    if (!((_a = panelRef.current) == null ? void 0 : _a.parentElement)) return 0;
    return direction === "horizontal" ? panelRef.current.parentElement.clientWidth : panelRef.current.parentElement.clientHeight;
  }, [direction]);
  const calculateNewSize = reactExports.useCallback((clientX, clientY) => {
    if (!panelRef.current) return size;
    panelRef.current.getBoundingClientRect();
    const containerSize = getContainerSize();
    let newSize;
    if (direction === "horizontal") {
      const deltaX = clientX - dragStartPos.x;
      if (resizeHandle === "right") {
        newSize = unit === "percent" ? (dragStartSize * containerSize / 100 + deltaX) / containerSize * 100 : dragStartSize + deltaX;
      } else {
        newSize = unit === "percent" ? (dragStartSize * containerSize / 100 - deltaX) / containerSize * 100 : dragStartSize - deltaX;
      }
    } else {
      const deltaY = clientY - dragStartPos.y;
      if (resizeHandle === "bottom") {
        newSize = unit === "percent" ? (dragStartSize * containerSize / 100 + deltaY) / containerSize * 100 : dragStartSize + deltaY;
      } else {
        newSize = unit === "percent" ? (dragStartSize * containerSize / 100 - deltaY) / containerSize * 100 : dragStartSize - deltaY;
      }
    }
    return Math.max(minSize, Math.min(maxSize, newSize));
  }, [direction, resizeHandle, unit, dragStartPos, dragStartSize, size, minSize, maxSize, getContainerSize]);
  const handleMouseDown = reactExports.useCallback((e) => {
    if (disabled) return;
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
    setDragStartPos({ x: e.clientX, y: e.clientY });
    setDragStartSize(size);
    setPreviewSize(size);
    onResizeStart == null ? void 0 : onResizeStart();
    document.body.style.cursor = direction === "horizontal" ? "col-resize" : "row-resize";
    document.body.style.userSelect = "none";
    document.body.style.pointerEvents = "none";
    if (resizeHandleRef.current) {
      resizeHandleRef.current.style.pointerEvents = "auto";
    }
  }, [disabled, size, direction, onResizeStart]);
  const handleMouseMove = reactExports.useCallback((e) => {
    if (!isDragging) return;
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
    animationFrameRef.current = requestAnimationFrame(() => {
      const newSize = calculateNewSize(e.clientX, e.clientY);
      if (showPreview) {
        setPreviewSize(newSize);
      } else {
        onResize(newSize);
      }
    });
  }, [isDragging, calculateNewSize, showPreview, onResize]);
  const handleMouseUp = reactExports.useCallback(() => {
    if (!isDragging) return;
    setIsDragging(false);
    if (showPreview && previewSize !== null) {
      onResize(previewSize);
    }
    setPreviewSize(null);
    onResizeEnd == null ? void 0 : onResizeEnd();
    document.body.style.cursor = "";
    document.body.style.userSelect = "";
    document.body.style.pointerEvents = "";
    if (resizeHandleRef.current) {
      resizeHandleRef.current.style.pointerEvents = "";
    }
  }, [isDragging, showPreview, previewSize, onResize, onResizeEnd]);
  reactExports.useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
      document.addEventListener("mouseleave", handleMouseUp);
      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
        document.removeEventListener("mouseleave", handleMouseUp);
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
        }
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);
  reactExports.useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === "Escape" && isDragging) {
        setIsDragging(false);
        setPreviewSize(null);
        onResizeEnd == null ? void 0 : onResizeEnd();
        document.body.style.cursor = "";
        document.body.style.userSelect = "";
        document.body.style.pointerEvents = "";
      }
    };
    if (isDragging) {
      document.addEventListener("keydown", handleKeyDown);
      return () => document.removeEventListener("keydown", handleKeyDown);
    }
  }, [isDragging, onResizeEnd]);
  const getResizeHandleStyle = () => {
    const baseClasses = [
      "absolute",
      "transition-all duration-200 ease-out",
      "group",
      disabled ? "cursor-not-allowed" : direction === "horizontal" ? "cursor-col-resize" : "cursor-row-resize"
    ];
    const activeClasses = [
      isDragging && "bg-attention-border",
      isHovering && !isDragging && "bg-highlight-border opacity-50",
      !isHovering && !isDragging && "bg-transparent hover:bg-highlight-border hover:opacity-30"
    ];
    const positionClasses = {
      "right": "top-0 right-0 w-1 h-full",
      "left": "top-0 left-0 w-1 h-full",
      "bottom": "bottom-0 left-0 w-full h-1",
      "top": "top-0 left-0 w-full h-1"
    };
    return cn(
      ...baseClasses,
      ...activeClasses,
      positionClasses[resizeHandle]
    );
  };
  const currentSize = showPreview && previewSize !== null ? previewSize : size;
  const panelStyle = unit === "percent" ? direction === "horizontal" ? { width: `${currentSize}%` } : { height: `${currentSize}%` } : direction === "horizontal" ? { width: `${currentSize}px` } : { height: `${currentSize}px` };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(
    "div",
    {
      ref: panelRef,
      className: cn(
        "relative transition-all duration-200 ease-out",
        isDragging && "transition-none",
        // 拖拽时禁用过渡
        className
      ),
      style: panelStyle,
      "data-resizing": isDragging,
      children: [
        children,
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "div",
          {
            ref: resizeHandleRef,
            className: getResizeHandleStyle(),
            onMouseDown: handleMouseDown,
            onMouseEnter: () => setIsHovering(true),
            onMouseLeave: () => setIsHovering(false),
            title: disabled ? "调整大小已禁用" : `拖拽调整${direction === "horizontal" ? "宽度" : "高度"}`,
            children: (isHovering || isDragging) && !disabled && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: cn(
              "absolute inset-0 flex items-center justify-center",
              "opacity-0 group-hover:opacity-100 transition-opacity duration-200",
              isDragging && "opacity-100"
            ), children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: cn(
              "bg-white rounded-full shadow-lg",
              direction === "horizontal" ? "w-1 h-8" : "w-8 h-1"
            ) }) })
          }
        ),
        isDragging && showPreview && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: cn(
          "absolute bg-attention-border opacity-60 z-50 pointer-events-none",
          direction === "horizontal" ? resizeHandle === "right" ? "top-0 right-0 w-0.5 h-full" : "top-0 left-0 w-0.5 h-full" : resizeHandle === "bottom" ? "bottom-0 left-0 w-full h-0.5" : "top-0 left-0 w-full h-0.5"
        ) })
      ]
    }
  );
};
const DEFAULT_LAYOUT = {
  catalogWidth: 280,
  infoWidth: 320,
  galleryHeight: 60,
  // 百分比
  workbenchHeight: 40,
  // 百分比
  isGlobalView: false,
  catalogVisible: true,
  infoVisible: true,
  workbenchVisible: true
};
const DEFAULT_CONSTRAINTS = {
  catalogWidth: { min: 200, max: 400, default: 280 },
  infoWidth: { min: 200, max: 500, default: 320 },
  galleryHeight: { min: 20, max: 85, default: 60 }
};
const STORAGE_KEY = "mizzy-star-layout-state";
const STORAGE_VERSION = "1.0";
const useLayoutState = (initialLayout, constraints = DEFAULT_CONSTRAINTS) => {
  const [layout, setLayoutState] = reactExports.useState(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        if (parsed.version === STORAGE_VERSION) {
          const restoredLayout = __spreadValues({}, DEFAULT_LAYOUT);
          if (typeof parsed.catalogWidth === "number" && parsed.catalogWidth >= constraints.catalogWidth.min && parsed.catalogWidth <= constraints.catalogWidth.max) {
            restoredLayout.catalogWidth = parsed.catalogWidth;
          }
          if (typeof parsed.infoWidth === "number" && parsed.infoWidth >= constraints.infoWidth.min && parsed.infoWidth <= constraints.infoWidth.max) {
            restoredLayout.infoWidth = parsed.infoWidth;
          }
          if (typeof parsed.galleryHeight === "number" && parsed.galleryHeight >= constraints.galleryHeight.min && parsed.galleryHeight <= constraints.galleryHeight.max) {
            restoredLayout.galleryHeight = parsed.galleryHeight;
            restoredLayout.workbenchHeight = 100 - parsed.galleryHeight;
          }
          if (typeof parsed.catalogVisible === "boolean") {
            restoredLayout.catalogVisible = parsed.catalogVisible;
          }
          if (typeof parsed.infoVisible === "boolean") {
            restoredLayout.infoVisible = parsed.infoVisible;
          }
          if (typeof parsed.workbenchVisible === "boolean") {
            restoredLayout.workbenchVisible = parsed.workbenchVisible;
          }
          return __spreadValues(__spreadValues({}, restoredLayout), initialLayout);
        }
      }
    } catch (error) {
      console.warn("Failed to restore layout state:", error);
    }
    return __spreadValues(__spreadValues({}, DEFAULT_LAYOUT), initialLayout);
  });
  const saveTimeoutRef = reactExports.useRef();
  const saveToStorage = reactExports.useCallback((newLayout) => {
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }
    saveTimeoutRef.current = setTimeout(() => {
      try {
        const toStore = __spreadProps(__spreadValues({}, newLayout), {
          version: STORAGE_VERSION,
          timestamp: Date.now()
        });
        localStorage.setItem(STORAGE_KEY, JSON.stringify(toStore));
      } catch (error) {
        console.warn("Failed to save layout state:", error);
      }
    }, 500);
  }, []);
  const updateLayout = reactExports.useCallback((updates) => {
    setLayoutState((prev) => {
      const newLayout = __spreadValues(__spreadValues({}, prev), updates);
      if ("galleryHeight" in updates) {
        newLayout.workbenchHeight = 100 - newLayout.galleryHeight;
      } else if ("workbenchHeight" in updates) {
        newLayout.galleryHeight = 100 - newLayout.workbenchHeight;
      }
      newLayout.catalogWidth = Math.max(
        constraints.catalogWidth.min,
        Math.min(constraints.catalogWidth.max, newLayout.catalogWidth)
      );
      newLayout.infoWidth = Math.max(
        constraints.infoWidth.min,
        Math.min(constraints.infoWidth.max, newLayout.infoWidth)
      );
      newLayout.galleryHeight = Math.max(
        constraints.galleryHeight.min,
        Math.min(constraints.galleryHeight.max, newLayout.galleryHeight)
      );
      saveToStorage(newLayout);
      return newLayout;
    });
  }, [constraints, saveToStorage]);
  const resetLayout = reactExports.useCallback(() => {
    const resetState = __spreadValues(__spreadValues({}, DEFAULT_LAYOUT), initialLayout);
    setLayoutState(resetState);
    saveToStorage(resetState);
  }, [initialLayout, saveToStorage]);
  const toggleGlobalView = reactExports.useCallback(() => {
    const newGlobalView = !layout.isGlobalView;
    if (typeof window !== "undefined" && window.electronAPI) {
      window.electronAPI.toggleFullscreen(newGlobalView);
    }
    updateLayout({ isGlobalView: newGlobalView });
  }, [layout.isGlobalView, updateLayout]);
  const togglePanel = reactExports.useCallback((panel) => {
    updateLayout({ [panel]: !layout[panel] });
  }, [layout, updateLayout]);
  const resizeCatalog = reactExports.useCallback((width) => {
    updateLayout({ catalogWidth: width });
  }, [updateLayout]);
  const resizeInfo = reactExports.useCallback((width) => {
    updateLayout({ infoWidth: width });
  }, [updateLayout]);
  const resizeVertical = reactExports.useCallback((galleryHeight) => {
    updateLayout({ galleryHeight });
  }, [updateLayout]);
  const swapGalleryWorkbench = reactExports.useCallback(() => {
    updateLayout({
      galleryHeight: layout.workbenchHeight,
      workbenchHeight: layout.galleryHeight
    });
  }, [layout.galleryHeight, layout.workbenchHeight, updateLayout]);
  const getMainContentStyle = reactExports.useCallback(() => {
    let width = "100%";
    let marginLeft = "0";
    let marginRight = "0";
    if (layout.catalogVisible && !layout.isGlobalView) {
      width = `calc(100% - ${layout.catalogWidth}px)`;
      marginLeft = `${layout.catalogWidth}px`;
    }
    if (layout.infoVisible && !layout.isGlobalView) {
      width = layout.catalogVisible ? `calc(100% - ${layout.catalogWidth + layout.infoWidth}px)` : `calc(100% - ${layout.infoWidth}px)`;
      marginRight = `${layout.infoWidth}px`;
    }
    return { width, marginLeft, marginRight };
  }, [layout]);
  const handleResize = reactExports.useCallback(() => {
    const screenWidth = window.innerWidth;
    if (screenWidth < 768) {
      if (layout.catalogVisible || layout.infoVisible) {
        updateLayout({
          catalogVisible: false,
          infoVisible: false
        });
      }
    }
  }, [layout.catalogVisible, layout.infoVisible, updateLayout]);
  reactExports.useEffect(() => {
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [handleResize]);
  reactExports.useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, []);
  return {
    layout,
    updateLayout,
    resetLayout,
    toggleGlobalView,
    togglePanel,
    resizeCatalog,
    resizeInfo,
    resizeVertical,
    swapGalleryWorkbench,
    getMainContentStyle,
    constraints
  };
};
const LayoutDebugger = ({
  layout,
  constraints,
  onReset,
  enabled = false
}) => {
  const [isVisible, setIsVisible] = reactExports.useState(false);
  const [metrics, setMetrics] = reactExports.useState({
    resizeCount: 0,
    averageResizeTime: 0,
    lastResizeTime: 0
  });
  reactExports.useEffect(() => {
    if (!enabled) return;
    const startTime = performance.now();
    return () => {
      const endTime = performance.now();
      const resizeTime = endTime - startTime;
      setMetrics((prev) => {
        var _a;
        return {
          resizeCount: prev.resizeCount + 1,
          averageResizeTime: (prev.averageResizeTime * prev.resizeCount + resizeTime) / (prev.resizeCount + 1),
          lastResizeTime: resizeTime,
          memoryUsage: ((_a = performance.memory) == null ? void 0 : _a.usedJSHeapSize) || 0
        };
      });
    };
  }, [layout, enabled]);
  reactExports.useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.ctrlKey && e.shiftKey && e.key === "D") {
        e.preventDefault();
        setIsVisible(!isVisible);
      }
    };
    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [isVisible]);
  if (!enabled || !isVisible) {
    return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "fixed bottom-4 right-4 z-50", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
      "button",
      {
        onClick: () => setIsVisible(true),
        className: "bg-secondary-bg text-xs px-2 py-1 rounded opacity-50 hover:opacity-100",
        title: "显示布局调试器 (Ctrl+Shift+D)",
        children: "Debug"
      }
    ) });
  }
  const formatBytes = (bytes) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "fixed bottom-4 right-4 z-50 bg-secondary-bg border border-highlight-border rounded-lg p-4 max-w-sm", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between mb-3", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-sm font-semibold", children: "布局调试器" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "button",
        {
          onClick: () => setIsVisible(false),
          className: "text-xs hover:text-attention-border",
          children: "✕"
        }
      )
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mb-3", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("h4", { className: "text-xs font-medium mb-1", children: "当前状态" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-xs space-y-1 text-secondary", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          "目录栏: ",
          layout.catalogWidth,
          "px (",
          layout.catalogVisible ? "显示" : "隐藏",
          ")"
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          "信息栏: ",
          layout.infoWidth,
          "px (",
          layout.infoVisible ? "显示" : "隐藏",
          ")"
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          "画廊: ",
          layout.galleryHeight.toFixed(1),
          "%"
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          "工作台: ",
          layout.workbenchHeight.toFixed(1),
          "% (",
          layout.workbenchVisible ? "显示" : "隐藏",
          ")"
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          "全局视图: ",
          layout.isGlobalView ? "是" : "否"
        ] })
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mb-3", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("h4", { className: "text-xs font-medium mb-1", children: "约束范围" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-xs space-y-1 text-secondary", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          "目录栏: ",
          constraints.catalogWidth.min,
          "-",
          constraints.catalogWidth.max,
          "px"
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          "信息栏: ",
          constraints.infoWidth.min,
          "-",
          constraints.infoWidth.max,
          "px"
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          "画廊: ",
          constraints.galleryHeight.min,
          "-",
          constraints.galleryHeight.max,
          "%"
        ] })
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mb-3", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("h4", { className: "text-xs font-medium mb-1", children: "性能指标" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-xs space-y-1 text-secondary", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          "调整次数: ",
          metrics.resizeCount
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          "平均耗时: ",
          metrics.averageResizeTime.toFixed(2),
          "ms"
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          "最近耗时: ",
          metrics.lastResizeTime.toFixed(2),
          "ms"
        ] }),
        metrics.memoryUsage && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          "内存使用: ",
          formatBytes(metrics.memoryUsage)
        ] })
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "button",
        {
          onClick: onReset,
          className: "text-xs bg-attention-border text-white px-2 py-1 rounded hover:bg-opacity-80",
          children: "重置布局"
        }
      ),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "button",
        {
          onClick: () => setMetrics({
            resizeCount: 0,
            averageResizeTime: 0,
            lastResizeTime: 0
          }),
          className: "text-xs bg-secondary-bg-hover px-2 py-1 rounded hover:bg-opacity-80",
          children: "清除指标"
        }
      )
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mt-3 pt-2 border-t border-secondary-bg-hover", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-xs text-tertiary", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: "Ctrl+Shift+D: 切换调试器" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: "Tab: 切换全局视图" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: "ESC: 退出全局视图" })
    ] }) })
  ] });
};
const NewMainLayout = () => {
  const {
    layout,
    toggleGlobalView,
    togglePanel,
    resizeCatalog,
    resizeInfo,
    resizeVertical,
    getMainContentStyle,
    resetLayout,
    constraints
  } = useLayoutState();
  reactExports.useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === "Escape" && layout.isGlobalView) {
        toggleGlobalView();
      }
      if (e.key === "Tab" && !e.ctrlKey && !e.altKey && !e.shiftKey) {
        e.preventDefault();
        toggleGlobalView();
      }
    };
    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [layout.isGlobalView, toggleGlobalView]);
  const mainContentStyle = getMainContentStyle();
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(
    "div",
    {
      className: cn(
        "h-screen w-screen overflow-hidden",
        "flex relative bg-main text-primary",
        layout.isGlobalView && "global-view"
      ),
      "data-layout": "main",
      children: [
        layout.catalogVisible && !layout.isGlobalView && /* @__PURE__ */ jsxRuntimeExports.jsx(
          ResizablePanel,
          {
            direction: "horizontal",
            size: layout.catalogWidth,
            onResize: resizeCatalog,
            minSize: constraints.catalogWidth.min,
            maxSize: constraints.catalogWidth.max,
            className: "bg-sidebar border-r border-secondary-bg",
            resizeHandle: "right",
            showPreview: true,
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "h-full p-4", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between mb-4", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-lg font-semibold", children: "目录栏" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx(
                  "button",
                  {
                    onClick: toggleGlobalView,
                    className: "px-2 py-1 text-xs bg-secondary-bg rounded hover:bg-secondary-bg-hover",
                    title: "全局视图 (TAB)",
                    children: "全局"
                  }
                )
              ] }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mb-4", children: /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "w-full text-left p-2 hover:bg-secondary-bg rounded", children: "首选项" }) }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mb-4", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-2 bg-secondary-bg rounded", children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm", children: "当前档案库" }) }) }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mb-4", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                "input",
                {
                  type: "text",
                  placeholder: "标签搜索...",
                  className: "w-full p-2 bg-secondary-bg border border-transparent rounded focus:border-highlight-border"
                }
              ) }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-2", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-2 bg-secondary-bg rounded", children: "标签看板" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-2 bg-secondary-bg rounded", children: "研究者标签" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-2 bg-secondary-bg rounded", children: "元数据" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-2 bg-secondary-bg rounded", children: "计算机视觉" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-2 bg-secondary-bg rounded", children: "内容识别" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-2 bg-secondary-bg rounded", children: "语义识别" })
              ] }) }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mt-4", children: /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "w-full p-2 bg-secondary-bg rounded hover:bg-secondary-bg-hover", children: "屏蔽器" }) })
            ] })
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsxs(
          "div",
          {
            className: "flex-1 flex flex-col relative",
            style: mainContentStyle,
            children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                ResizablePanel,
                {
                  direction: "vertical",
                  size: layout.galleryHeight,
                  onResize: resizeVertical,
                  minSize: constraints.galleryHeight.min,
                  maxSize: constraints.galleryHeight.max,
                  className: "bg-main",
                  resizeHandle: "bottom",
                  unit: "percent",
                  showPreview: true,
                  children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "h-full p-4", children: [
                    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between mb-4", children: [
                      /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-lg font-semibold", children: "画廊" }),
                      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2", children: [
                        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "px-2 py-1 text-xs bg-secondary-bg rounded", children: "布局" }),
                        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "px-2 py-1 text-xs bg-secondary-bg rounded", children: "缩放" }),
                        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "px-2 py-1 text-xs bg-secondary-bg rounded", children: "排序" })
                      ] })
                    ] }),
                    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 bg-secondary-bg rounded p-4", children: /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-center text-secondary", children: "画廊内容区域" }) })
                  ] })
                }
              ),
              layout.workbenchVisible && /* @__PURE__ */ jsxRuntimeExports.jsx(
                "div",
                {
                  className: "bg-main border-t border-secondary-bg",
                  style: { height: `${layout.workbenchHeight}%` },
                  children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "h-full p-4", children: [
                    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between mb-4", children: [
                      /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-lg font-semibold", children: "工作台" }),
                      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2", children: [
                        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "px-2 py-1 text-xs bg-secondary-bg rounded", children: "剪贴板" }),
                        /* @__PURE__ */ jsxRuntimeExports.jsx(
                          "button",
                          {
                            onClick: () => togglePanel("workbenchVisible"),
                            className: "px-2 py-1 text-xs bg-secondary-bg rounded hover:bg-secondary-bg-hover",
                            children: "关闭"
                          }
                        )
                      ] })
                    ] }),
                    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 bg-secondary-bg rounded p-4", children: /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-center text-secondary", children: "工作台内容区域" }) })
                  ] })
                }
              )
            ]
          }
        ),
        layout.infoVisible && !layout.isGlobalView && /* @__PURE__ */ jsxRuntimeExports.jsx(
          ResizablePanel,
          {
            direction: "horizontal",
            size: layout.infoWidth,
            onResize: resizeInfo,
            minSize: constraints.infoWidth.min,
            maxSize: constraints.infoWidth.max,
            className: "bg-sidebar border-l border-secondary-bg",
            resizeHandle: "left",
            showPreview: true,
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "h-full p-4", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-lg font-semibold mb-4", children: "信息栏" }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mb-4 p-3 bg-secondary-bg rounded", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-sm font-medium mb-2", children: "元数据面板" }),
                /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-1 text-xs text-secondary", children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: "文件名: example.jpg" }),
                  /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: "尺寸: 1920x1080" }),
                  /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: "大小: 2.5MB" }),
                  /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: "格式: JPEG" })
                ] })
              ] }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mb-4 p-3 bg-secondary-bg rounded", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-sm font-medium mb-2", children: "研究者标签规则" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-xs text-secondary", children: "文件名生成标签功能" })
              ] }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mb-4 p-3 bg-secondary-bg rounded", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-sm font-medium mb-2", children: "计算机视觉规则" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-xs text-secondary", children: "质量评分映射" })
              ] }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-3 bg-secondary-bg rounded", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-sm font-medium mb-2", children: "AI分析" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-xs text-secondary", children: "模型配置和向量提取" })
              ] })
            ] })
          }
        ),
        layout.isGlobalView && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "absolute top-4 left-1/2 transform -translate-x-1/2 z-50", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "bg-secondary-bg border border-highlight-border rounded-md px-4 py-2 flex items-center gap-2", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm text-secondary", children: "全局视图模式" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "button",
            {
              onClick: toggleGlobalView,
              className: "text-xs bg-highlight-border text-main px-2 py-1 rounded hover:bg-opacity-80 transition-colors",
              children: "退出 (ESC)"
            }
          )
        ] }) }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          LayoutDebugger,
          {
            layout,
            constraints,
            onReset: resetLayout,
            enabled: false
          }
        )
      ]
    }
  );
};
const NewLayoutTest = () => {
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-full h-screen", children: /* @__PURE__ */ jsxRuntimeExports.jsx(NewMainLayout, {}) });
};
export {
  NewLayoutTest as default
};
