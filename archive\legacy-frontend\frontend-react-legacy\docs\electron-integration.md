# Electron 集成指南

## 🔗 将 React 应用集成到 Electron

### 1. 构建配置

#### 修改 Vite 配置
```typescript
// vite.config.ts
export default defineConfig({
  // Electron 特定配置
  base: './', // 使用相对路径
  
  build: {
    outDir: '../dist/renderer', // 输出到 Electron 可访问的目录
    emptyOutDir: true,
    
    rollupOptions: {
      // 外部化 Electron 模块
      external: ['electron'],
    },
  },
  
  // 开发服务器配置
  server: {
    port: 5173,
    strictPort: true,
  },
});
```

#### 更新 package.json
```json
{
  "main": "dist/main/main.js",
  "scripts": {
    "electron:dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron .\"",
    "electron:build": "npm run build && electron-builder",
    "build:electron": "npm run build && cp -r dist/* ../dist/renderer/"
  }
}
```

### 2. Electron 主进程修改

#### 更新 main.js
```javascript
const { app, BrowserWindow } = require('electron');
const path = require('path');
const isDev = process.env.NODE_ENV === 'development';

function createWindow() {
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  if (isDev) {
    // 开发模式：加载开发服务器
    mainWindow.loadURL('http://localhost:5173');
    mainWindow.webContents.openDevTools();
  } else {
    // 生产模式：加载构建后的文件
    mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
  }
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
```

### 3. API 路径配置

#### 环境变量配置
```typescript
// src/lib/api.ts
const API_BASE_URL = process.env.NODE_ENV === 'development' 
  ? 'http://localhost:8000'  // 开发环境
  : 'http://localhost:8000'; // 生产环境（Electron 中的后端服务）

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
});
```

#### 文件路径处理
```typescript
// src/utils/fileUtils.ts
export function getFileUrl(filePath: string): string {
  if (process.env.NODE_ENV === 'development') {
    return `file://${filePath}`;
  }
  
  // 在 Electron 中处理文件路径
  return `file://${filePath}`;
}

export function getThumbnailUrl(caseId: number, fileId: number): string {
  return `/api/v1/cases/${caseId}/files/${fileId}/thumbnail`;
}
```

### 4. Electron 特定功能

#### 文件系统访问
```typescript
// src/services/electronService.ts
declare global {
  interface Window {
    electronAPI: {
      openFile: () => Promise<string[]>;
      saveFile: (data: any) => Promise<string>;
      showMessageBox: (options: any) => Promise<any>;
    };
  }
}

export class ElectronService {
  static async openFileDialog(): Promise<string[]> {
    if (window.electronAPI) {
      return await window.electronAPI.openFile();
    }
    throw new Error('Electron API not available');
  }

  static async saveFile(data: any): Promise<string> {
    if (window.electronAPI) {
      return await window.electronAPI.saveFile(data);
    }
    throw new Error('Electron API not available');
  }

  static async showMessage(title: string, message: string): Promise<void> {
    if (window.electronAPI) {
      await window.electronAPI.showMessageBox({
        type: 'info',
        title,
        message,
      });
    } else {
      alert(`${title}: ${message}`);
    }
  }
}
```

#### Preload 脚本
```javascript
// preload.js
const { contextBridge, ipcRenderer } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  openFile: () => ipcRenderer.invoke('dialog:openFile'),
  saveFile: (data) => ipcRenderer.invoke('dialog:saveFile', data),
  showMessageBox: (options) => ipcRenderer.invoke('dialog:showMessageBox', options),
});
```

### 5. 开发工作流

#### 开发模式启动
```bash
# 终端 1: 启动 React 开发服务器
npm run dev

# 终端 2: 启动 Electron（等待 React 服务器启动）
npm run electron:dev
```

#### 生产构建
```bash
# 1. 构建 React 应用
npm run build

# 2. 复制到 Electron 目录
npm run build:electron

# 3. 构建 Electron 应用
npm run electron:build
```

### 6. 路由配置

#### 使用 HashRouter
```typescript
// src/App.tsx
import { HashRouter } from 'react-router-dom';

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <HashRouter> {/* 使用 HashRouter 而不是 BrowserRouter */}
        <Routes>
          {/* 路由配置 */}
        </Routes>
      </HashRouter>
    </QueryClientProvider>
  );
}
```

### 7. 安全配置

#### CSP 配置
```html
<!-- public/index.html -->
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' 'unsafe-inline';
  style-src 'self' 'unsafe-inline';
  img-src 'self' data: file:;
  connect-src 'self' http://localhost:8000;
">
```

### 8. 调试配置

#### React DevTools
```javascript
// main.js 中添加
if (isDev) {
  // 安装 React DevTools
  const { default: installExtension, REACT_DEVELOPER_TOOLS } = require('electron-devtools-installer');
  
  app.whenReady().then(() => {
    installExtension(REACT_DEVELOPER_TOOLS)
      .then((name) => console.log(`Added Extension: ${name}`))
      .catch((err) => console.log('An error occurred: ', err));
  });
}
```

### 9. 性能优化

#### 预加载优化
```javascript
// main.js
const mainWindow = new BrowserWindow({
  show: false, // 初始隐藏窗口
  webPreferences: {
    preload: path.join(__dirname, 'preload.js'),
    webSecurity: false, // 仅在开发环境
  }
});

// 窗口准备好后显示
mainWindow.once('ready-to-show', () => {
  mainWindow.show();
});
```

#### 资源缓存
```typescript
// 在 Electron 中缓存静态资源
const cache = new Map();

export function getCachedResource(url: string): Promise<any> {
  if (cache.has(url)) {
    return Promise.resolve(cache.get(url));
  }
  
  return fetch(url)
    .then(response => response.json())
    .then(data => {
      cache.set(url, data);
      return data;
    });
}
```

### 10. 部署配置

#### Electron Builder 配置
```json
// package.json
{
  "build": {
    "appId": "com.mizzystar.app",
    "productName": "Mizzy Star",
    "directories": {
      "output": "release"
    },
    "files": [
      "dist/**/*",
      "node_modules/**/*"
    ],
    "mac": {
      "category": "public.app-category.productivity"
    },
    "win": {
      "target": "nsis"
    },
    "linux": {
      "target": "AppImage"
    }
  }
}
```

### 11. 迁移检查清单

#### 代码迁移
- [ ] 更新 API 基础 URL
- [ ] 修改文件路径处理
- [ ] 更新路由配置（使用 HashRouter）
- [ ] 添加 Electron 特定功能

#### 配置迁移
- [ ] 更新 Vite 配置
- [ ] 修改 Electron 主进程
- [ ] 配置 preload 脚本
- [ ] 设置安全策略

#### 测试验证
- [ ] 开发模式测试
- [ ] 生产构建测试
- [ ] 功能完整性测试
- [ ] 性能测试

### 12. 故障排除

#### 常见问题
1. **路由不工作**: 使用 HashRouter 而不是 BrowserRouter
2. **文件路径错误**: 检查 base 配置和文件路径处理
3. **API 调用失败**: 确认后端服务运行和 CORS 配置
4. **样式丢失**: 检查 CSS 文件路径和 CSP 配置

#### 调试技巧
- 使用 Electron DevTools
- 检查控制台错误
- 验证文件路径
- 测试 API 连接
