import React, { useState } from 'react';
import { 
  Settings, 
  Maximize2, 
  Database, 
  Search,
  ChevronDown,
  ChevronRight,
  Star,
  Code,
  Camera,
  Eye,
  FileText,
  Brain,
  Filter,
  FilterX,
  Plus,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Shield,
  ShieldOff,
  Workflow
} from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from './ui/dropdown-menu';
import { Dialog, DialogContent, DialogTrigger, DialogTitle, DialogDescription } from './ui/dialog';
import { LibraryManagement } from './LibraryManagement';

interface NavigationSidebarProps {
  onToggleSidebars: () => void;
  onToggleWorkspace: () => void;
  onSwapGalleryWorkspace: () => void;
  selectedTags: string[];
  onSelectedTagsChange: (tags: string[]) => void;
  currentLibrary: string;
  onCurrentLibraryChange: (library: string) => void;
}

interface TagUnit {
  id: string;
  name: string;
  icon: React.ComponentType;
  tags: string[];
  expanded: boolean;
  editable: boolean;
}

export const NavigationSidebar: React.FC<NavigationSidebarProps> = ({
  onToggleSidebars,
  onToggleWorkspace,
  onSwapGalleryWorkspace,
  selectedTags,
  onSelectedTagsChange,
  currentLibrary,
  onCurrentLibraryChange,
}) => {
  const [tagSearch, setTagSearch] = useState('');
  const [isLibraryDialogOpen, setIsLibraryDialogOpen] = useState(false);
  const [newTagInput, setNewTagInput] = useState('');
  const [showNewTagInput, setShowNewTagInput] = useState(false);
  const [blockedTags, setBlockedTags] = useState<string[]>([]);
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [sortBy, setSortBy] = useState<'name' | 'recent' | 'count'>('name');

  const [tagUnits, setTagUnits] = useState<TagUnit[]>([
    {
      id: 'favorites',
      name: '标签看板',
      icon: Star,
      tags: ['中原摄影(3)', '街头摄影(12)', '人像(8)'],
      expanded: true,
      editable: true
    },
    {
      id: 'rules',
      name: '规则标签',
      icon: Code,
      tags: ['自定义规则1(5)', '文件名规则(23)', '批量标签(15)'],
      expanded: true,
      editable: true
    },
    {
      id: 'metadata',
      name: '元数据',
      icon: FileText,
      tags: ['JPEG(45)', '高分辨率(23)', '横向(67)', 'Canon EOS(12)', 'f/2.8(8)', '1/250s(15)', 'ISO 400(10)'],
      expanded: false,
      editable: false
    },
    {
      id: 'cv',
      name: '计算机视觉',
      icon: Eye,
      tags: ['高质量(34)', '清晰度优秀(28)', '构图良好(31)', '色彩丰富(22)'],
      expanded: false,
      editable: false
    },
    {
      id: 'content',
      name: '内容识别',
      icon: Camera,
      tags: ['人物(45)', '建筑(23)', '风景(67)', '汽车(12)', '动物(8)', '食物(15)'],
      expanded: false,
      editable: true
    },
    {
      id: 'semantic',
      name: '语义识别',
      icon: Brain,
      tags: ['快乐情绪(12)', '商业场景(8)', '艺术作品(15)', '纪实摄影(22)', '创意作品(9)'],
      expanded: false,
      editable: true
    }
  ]);

  const toggleTagUnit = (unitId: string) => {
    setTagUnits(prev => prev.map(unit => 
      unit.id === unitId ? { ...unit, expanded: !unit.expanded } : unit
    ));
  };

  const handleTagClick = (tag: string) => {
    const tagName = tag.split('(')[0];
    if (blockedTags.includes(tagName)) return;
    
    if (selectedTags.includes(tagName)) {
      onSelectedTagsChange(selectedTags.filter(t => t !== tagName));
    } else {
      onSelectedTagsChange([...selectedTags, tagName]);
    }
  };

  const handleSortOrderToggle = () => {
    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
  };

  const handleTagBlock = (tag: string) => {
    if (blockedTags.includes(tag)) {
      setBlockedTags(blockedTags.filter(t => t !== tag));
    } else {
      setBlockedTags([...blockedTags, tag]);
    }
  };

  const clearBlocked = () => {
    setBlockedTags([]);
  };

  // 排序标签
  const sortTags = (tags: string[]) => {
    return [...tags].sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'name':
          comparison = a.localeCompare(b, 'zh-CN');
          break;
        case 'count':
          const countA = parseInt(a.match(/\((\d+)\)/)?.[1] || '0');
          const countB = parseInt(b.match(/\((\d+)\)/)?.[1] || '0');
          comparison = countA - countB;
          break;
        case 'recent':
          // 这里可以添加最近使用时间的逻辑
          comparison = 0;
          break;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });
  };

  const addNewTag = () => {
    if (newTagInput.trim()) {
      setTagUnits(prev => prev.map(unit => 
        unit.id === 'rules' 
          ? { ...unit, tags: [...unit.tags, `${newTagInput.trim()}(0)`] }
          : unit
      ));
      setNewTagInput('');
      setShowNewTagInput(false);
    }
  };

  const filteredTagUnits = tagSearch 
    ? tagUnits.map(unit => ({
        ...unit,
        tags: unit.tags.filter(tag => 
          tag.toLowerCase().includes(tagSearch.toLowerCase())
        )
      })).filter(unit => unit.tags.length > 0)
    : tagUnits;

  return (
    <div className="h-full flex flex-col" style={{ color: 'var(--mizzy-content)' }}>
      {/* 顶部工具栏 */}
      <div className="p-4 border-b" style={{ borderColor: 'var(--mizzy-border)' }}>
        <div className="flex items-center justify-between mb-4">
          {/* 设置按钮 */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                size="sm"
                className="hover:bg-opacity-10"
                style={{ color: 'var(--mizzy-icon)' }}
              >
                <Settings className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem>快捷键</DropdownMenuItem>
              <DropdownMenuItem>偏好设置</DropdownMenuItem>
              <DropdownMenuItem>调试工具</DropdownMenuItem>
              <DropdownMenuItem>回收站</DropdownMenuItem>
              <DropdownMenuItem>清除缓存</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* 工作台切换按钮 */}
          <Button 
            variant="ghost" 
            size="sm"
            onClick={onToggleWorkspace}
            className="hover:bg-opacity-10"
            style={{ color: 'var(--mizzy-icon)' }}
          >
            <Workflow className="h-4 w-4" />
          </Button>

          {/* 展开视图按钮 */}
          <Button 
            variant="ghost" 
            size="sm"
            onClick={onToggleSidebars}
            className="hover:bg-opacity-10"
            style={{ color: 'var(--mizzy-icon)' }}
          >
            <Maximize2 className="h-4 w-4" />
          </Button>

          {/* 档案库管理按钮 */}
          <Dialog open={isLibraryDialogOpen} onOpenChange={setIsLibraryDialogOpen}>
            <DialogTrigger asChild>
              <Button 
                variant="ghost" 
                size="sm"
                className="hover:bg-opacity-10"
                style={{ color: 'var(--mizzy-icon)' }}
              >
                <Database className="h-4 w-4" />
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl">
              <DialogTitle className="sr-only">档案库管理</DialogTitle>
              <DialogDescription className="sr-only">
                管理您的图像档案库，创建新档案库或选择现有档案库
              </DialogDescription>
              <LibraryManagement 
                currentLibrary={currentLibrary}
                onLibraryChange={onCurrentLibraryChange}
                onClose={() => setIsLibraryDialogOpen(false)}
              />
            </DialogContent>
          </Dialog>
        </div>

        {/* 当前档案库显示 */}
        <div className="mb-4">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                className="w-full justify-start p-2 h-auto"
                style={{ 
                  color: 'var(--mizzy-content)',
                  background: 'var(--mizzy-button)'
                }}
              >
                <span className="truncate">{currentLibrary}</span>
                <ChevronDown className="h-4 w-4 ml-auto" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => onCurrentLibraryChange('默认档案库')}>
                默认档案库
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onCurrentLibraryChange('工作项目')}>
                工作项目
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onCurrentLibraryChange('个人收藏')}>
                个人收藏
              </DropdownMenuItem>
              <DropdownMenuItem className="text-red-500">
                关闭当前档案库
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* 标签搜索输入框 */}
        <div className="mb-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4" style={{ color: 'var(--mizzy-icon)' }} />
            <Input
              placeholder="输入标签名称"
              value={tagSearch}
              onChange={(e) => setTagSearch(e.target.value)}
              className="pl-10"
              style={{ 
                background: 'var(--mizzy-input)',
                color: 'var(--mizzy-content)',
                borderColor: 'var(--mizzy-border)'
              }}
            />
          </div>
        </div>

        {/* 标签排序和升序降序 */}
        <div className="flex items-center gap-2 mb-4">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                size="sm"
                className="hover:bg-opacity-10"
                style={{ color: 'var(--mizzy-icon)' }}
              >
                <ArrowUpDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setSortBy('name')}>
                按标签名称排序
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy('recent')}>
                按最近使用排序
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy('count')}>
                按标签数量排序
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button 
            variant="ghost" 
            size="sm"
            onClick={handleSortOrderToggle}
            className="hover:bg-opacity-10"
            style={{ color: 'var(--mizzy-icon)' }}
          >
            {sortOrder === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />}
          </Button>

          {/* 屏蔽器 */}
          {selectedTags.length > 0 && (
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => selectedTags.forEach(tag => handleTagBlock(tag))}
              className="hover:bg-opacity-10"
              style={{ color: 'var(--mizzy-icon)' }}
            >
              <Shield className="h-4 w-4" />
            </Button>
          )}

          {/* 解除屏蔽 */}
          {blockedTags.length > 0 && (
            <Button 
              variant="ghost" 
              size="sm"
              onClick={clearBlocked}
              className="hover:bg-opacity-10"
              style={{ color: 'var(--mizzy-icon)' }}
            >
              <ShieldOff className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* 标签列表 */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-2">
          {filteredTagUnits.map((unit) => (
            <div key={unit.id} className="mb-4">
              {/* 标签单元标题 */}
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <unit.icon className="h-4 w-4" style={{ color: 'var(--mizzy-icon)' }} />
                  <span className="text-sm font-medium" style={{ color: 'var(--mizzy-title)' }}>
                    {unit.name}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleTagUnit(unit.id)}
                    className="h-6 w-6 p-0 hover:bg-opacity-10"
                    style={{ color: 'var(--mizzy-icon)' }}
                  >
                    {unit.expanded ? <ChevronDown className="h-3 w-3" /> : <ChevronRight className="h-3 w-3" />}
                  </Button>
                </div>
              </div>

              {/* 原子标签 */}
              {unit.expanded && (
                <div className="flex flex-wrap gap-1">
                  {sortTags(unit.tags).map((tag) => {
                    const isBlocked = blockedTags.includes(tag);
                    const isSelected = selectedTags.includes(tag);
                    
                    return (
                      <Button
                        key={tag}
                        variant="ghost"
                        size="sm"
                        onClick={() => handleTagClick(tag)}
                        className={`h-6 px-2 text-xs ${
                          isBlocked ? 'opacity-50' : ''
                        } ${isSelected ? 'ring-1' : ''}`}
                        style={{
                          background: isBlocked ? 'var(--mizzy-content)' : 'var(--mizzy-button)',
                          color: isBlocked ? 'var(--mizzy-input)' : 'var(--mizzy-content)',
                          borderColor: isSelected ? 'var(--mizzy-highlight)' : 'transparent'
                        }}
                      >
                        {tag}
                      </Button>
                    );
                  })}
                  
                  {/* 新增标签按钮（仅对可编辑的标签单元显示） */}
                  {unit.editable && unit.id === 'rules' && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowNewTagInput(true)}
                      className="h-6 w-6 p-0 hover:bg-opacity-10"
                      style={{ color: 'var(--mizzy-icon)' }}
                    >
                      <Plus className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              )}

              {/* 新增标签输入框 */}
              {showNewTagInput && unit.id === 'rules' && (
                <div className="mt-2 flex items-center gap-2">
                  <Input
                    value={newTagInput}
                    onChange={(e) => setNewTagInput(e.target.value)}
                    placeholder="输入新标签名称"
                    className="flex-1"
                    style={{ 
                      background: 'var(--mizzy-input)',
                      color: 'var(--mizzy-content)',
                      borderColor: 'var(--mizzy-border)'
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        addNewTag();
                      } else if (e.key === 'Escape') {
                        setShowNewTagInput(false);
                        setNewTagInput('');
                      }
                    }}
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={addNewTag}
                    className="h-6 px-2"
                    style={{ color: 'var(--mizzy-highlight)' }}
                  >
                    确认
                  </Button>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};