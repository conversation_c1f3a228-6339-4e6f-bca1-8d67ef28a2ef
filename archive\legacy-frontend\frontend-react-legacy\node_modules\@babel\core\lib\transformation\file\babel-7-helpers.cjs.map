{"version": 3, "names": ["exports", "getModuleName", "require"], "sources": ["../../../src/transformation/file/babel-7-helpers.cjs"], "sourcesContent": ["// TODO(Babel 8): Remove this file\n\nif (!process.env.BABEL_8_BREAKING) {\n  exports.getModuleName = () =>\n    require(\"@babel/helper-module-transforms\").getModuleName;\n} else if (process.env.IS_PUBLISH) {\n  throw new Error(\n    \"Internal Babel error: This file should only be loaded in Babel 7\",\n  );\n}\n"], "mappings": "AAEmC;EACjCA,OAAO,CAACC,aAAa,GAAG,MACtBC,OAAO,CAAC,iCAAiC,CAAC,CAACD,aAAa;AAC5D;AAAC", "ignoreList": []}