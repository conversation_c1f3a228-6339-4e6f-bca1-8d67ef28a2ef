import { j as jsxRuntimeExports } from "./state-management-CeNIv-64.js";
import { R as React, a as useNavigate, r as reactExports } from "./router-DbSvV1fW.js";
import { c as cn } from "./index-BaeIiao7.js";
const CaseCard = React.memo(({
  case: caseItem,
  onEdit,
  onDelete,
  className
}) => {
  var _a;
  const navigate = useNavigate();
  const formattedDate = reactExports.useMemo(() => {
    return new Date(caseItem.created_at).toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "short",
      day: "numeric"
    });
  }, [caseItem.created_at]);
  const coverImageUrl = reactExports.useMemo(() => {
    if (caseItem.cover_image_url) {
      return caseItem.cover_image_url;
    }
    return "/placeholder-case.png";
  }, [caseItem.cover_image_url]);
  const handleCardClick = reactExports.useCallback(() => {
    navigate(`/cases/${caseItem.id}`);
  }, [navigate, caseItem.id]);
  const handleEdit = reactExports.useCallback((e) => {
    e.stopPropagation();
    if (onEdit) {
      onEdit(caseItem);
    }
  }, [onEdit, caseItem]);
  const handleDelete = reactExports.useCallback((e) => {
    e.stopPropagation();
    if (onDelete) {
      onDelete(caseItem);
    }
  }, [onDelete, caseItem]);
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(
    "div",
    {
      className: cn(
        "case-card bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",
        "hover:shadow-md hover:border-gray-300 transition-all duration-200 cursor-pointer",
        "group",
        className
      ),
      onClick: handleCardClick,
      "data-case-id": caseItem.id,
      children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "aspect-video bg-gray-100 overflow-hidden", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
          "img",
          {
            src: coverImageUrl,
            alt: caseItem.case_name,
            className: "w-full h-full object-cover group-hover:scale-105 transition-transform duration-200",
            onError: (e) => {
              const target = e.target;
              target.src = "/placeholder-case.png";
            }
          }
        ) }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-4", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-start justify-between mb-2", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "font-semibold text-gray-900 text-lg line-clamp-1", children: caseItem.case_name }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                "button",
                {
                  onClick: handleEdit,
                  className: "p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors",
                  title: "编辑案例",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-4 h-4", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                    "path",
                    {
                      strokeLinecap: "round",
                      strokeLinejoin: "round",
                      strokeWidth: 2,
                      d: "M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                    }
                  ) })
                }
              ),
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                "button",
                {
                  onClick: handleDelete,
                  className: "p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors",
                  title: "删除案例",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-4 h-4", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                    "path",
                    {
                      strokeLinecap: "round",
                      strokeLinejoin: "round",
                      strokeWidth: 2,
                      d: "M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                    }
                  ) })
                }
              )
            ] })
          ] }),
          caseItem.description && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-600 text-sm line-clamp-2 mb-3", children: caseItem.description }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between text-sm text-gray-500", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-4", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "flex items-center", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-4 h-4 mr-1", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                  "path",
                  {
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: 2,
                    d: "M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                  }
                ) }),
                ((_a = caseItem.files) == null ? void 0 : _a.length) || 0,
                " 文件"
              ] }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "flex items-center", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-4 h-4 mr-1", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                  "path",
                  {
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: 2,
                    d: "M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  }
                ) }),
                formattedDate
              ] })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "text-xs bg-gray-100 px-2 py-1 rounded", children: [
              "ID: ",
              caseItem.id
            ] })
          ] })
        ] })
      ]
    }
  );
});
export {
  CaseCard as C
};
