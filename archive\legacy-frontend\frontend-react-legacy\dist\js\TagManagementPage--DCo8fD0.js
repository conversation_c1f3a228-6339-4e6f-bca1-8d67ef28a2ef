var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __objRest = (source, exclude) => {
  var target = {};
  for (var prop in source)
    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)
      target[prop] = source[prop];
  if (source != null && __getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(source)) {
      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))
        target[prop] = source[prop];
    }
  return target;
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
import { c as create, j as jsxRuntimeExports, b as useQuery, u as useQueryClient, d as useMutation } from "./state-management-CeNIv-64.js";
import { r as reactExports, R as React, a as useNavigate, d as useParams, e as useSearchParams } from "./router-DbSvV1fW.js";
import { d as devtools, u as useAppStore, q as queryKeys, c as cn } from "./index-BaeIiao7.js";
import { a as apiClient, C as CaseService, M as Modal } from "./caseService-rs6u721W.js";
import { B as Button } from "./Button-CswqCd84.js";
import "./react-vendor-ZA51SWXd.js";
import "./ui-vendor-DgYk2OaC.js";
import "./index-DEw2ppt0.js";
const useTagManagementStore = create()(
  devtools(
    (set, get) => ({
      // 初始状态
      caseId: null,
      sourceFileId: null,
      searchQuery: "",
      selectedTags: /* @__PURE__ */ new Set(),
      selectedTagType: null,
      selectedTagKey: null,
      selectedTagValue: null,
      expandedCategories: /* @__PURE__ */ new Set(["custom"]),
      // 默认展开自定义标签
      selectedFiles: /* @__PURE__ */ new Set(),
      isSelectionMode: false,
      highlightedFileTags: /* @__PURE__ */ new Set(),
      originImageId: null,
      currentFiles: [],
      filteredFiles: [],
      galleryTitle: "选择标签查看相关文件",
      panelWidth: 400,
      isResizing: false,
      showImageModal: false,
      currentImageId: null,
      showAddTagModal: false,
      showCustomTagModal: false,
      editingCustomTag: null,
      showBatchActions: false,
      // Actions - 基础设置
      setCaseId: (caseId) => set({ caseId }, false, "setCaseId"),
      setSourceFileId: (fileId) => set({ sourceFileId: fileId }, false, "setSourceFileId"),
      // Actions - 搜索
      setSearchQuery: (query) => set({ searchQuery: query }, false, "setSearchQuery"),
      clearSearch: () => set({ searchQuery: "" }, false, "clearSearch"),
      // Actions - 标签选择
      selectTag: (tagType, tagKey, tagValue) => set({
        selectedTagType: tagType,
        selectedTagKey: tagKey,
        selectedTagValue: tagValue || tagKey
      }, false, "selectTag"),
      clearTagSelection: () => set({
        selectedTagType: null,
        selectedTagKey: null,
        selectedTagValue: null
      }, false, "clearTagSelection"),
      toggleTagSelection: (tagId) => set((state) => {
        const newSelectedTags = new Set(state.selectedTags);
        if (newSelectedTags.has(tagId)) {
          newSelectedTags.delete(tagId);
        } else {
          newSelectedTags.add(tagId);
        }
        return { selectedTags: newSelectedTags };
      }, false, "toggleTagSelection"),
      // Actions - 分类展开
      toggleCategoryExpansion: (category) => set((state) => {
        const newExpandedCategories = new Set(state.expandedCategories);
        if (newExpandedCategories.has(category)) {
          newExpandedCategories.delete(category);
        } else {
          newExpandedCategories.add(category);
        }
        return { expandedCategories: newExpandedCategories };
      }, false, "toggleCategoryExpansion"),
      expandCategory: (category) => set((state) => ({
        expandedCategories: /* @__PURE__ */ new Set([...state.expandedCategories, category])
      }), false, "expandCategory"),
      collapseCategory: (category) => set((state) => {
        const newExpandedCategories = new Set(state.expandedCategories);
        newExpandedCategories.delete(category);
        return { expandedCategories: newExpandedCategories };
      }, false, "collapseCategory"),
      // Actions - 文件选择
      toggleFileSelection: (fileId) => set((state) => {
        const newSelectedFiles = new Set(state.selectedFiles);
        if (newSelectedFiles.has(fileId)) {
          newSelectedFiles.delete(fileId);
        } else {
          newSelectedFiles.add(fileId);
        }
        const showBatchActions = newSelectedFiles.size > 0;
        return {
          selectedFiles: newSelectedFiles,
          showBatchActions
        };
      }, false, "toggleFileSelection"),
      selectAllFiles: () => set((state) => {
        const allFileIds = new Set(state.filteredFiles.map((file) => file.id));
        return {
          selectedFiles: allFileIds,
          showBatchActions: allFileIds.size > 0
        };
      }, false, "selectAllFiles"),
      clearFileSelection: () => set({
        selectedFiles: /* @__PURE__ */ new Set(),
        showBatchActions: false
      }, false, "clearFileSelection"),
      enterSelectionMode: () => set({ isSelectionMode: true }, false, "enterSelectionMode"),
      exitSelectionMode: () => set({
        isSelectionMode: false,
        selectedFiles: /* @__PURE__ */ new Set(),
        showBatchActions: false
      }, false, "exitSelectionMode"),
      // Actions - 画廊
      setCurrentFiles: (files) => set({ currentFiles: files }, false, "setCurrentFiles"),
      setFilteredFiles: (files) => set({ filteredFiles: files }, false, "setFilteredFiles"),
      setGalleryTitle: (title) => set({ galleryTitle: title }, false, "setGalleryTitle"),
      // Actions - 面板
      setPanelWidth: (width) => set({ panelWidth: width }, false, "setPanelWidth"),
      setIsResizing: (resizing) => set({ isResizing: resizing }, false, "setIsResizing"),
      // Actions - 模态框
      openImageModal: (imageId) => set({
        showImageModal: true,
        currentImageId: imageId
      }, false, "openImageModal"),
      closeImageModal: () => set({
        showImageModal: false,
        currentImageId: null
      }, false, "closeImageModal"),
      openAddTagModal: () => set({ showAddTagModal: true }, false, "openAddTagModal"),
      closeAddTagModal: () => set({ showAddTagModal: false }, false, "closeAddTagModal"),
      openCustomTagModal: (tag) => set({
        showCustomTagModal: true,
        editingCustomTag: tag || null
      }, false, "openCustomTagModal"),
      closeCustomTagModal: () => set({
        showCustomTagModal: false,
        editingCustomTag: null
      }, false, "closeCustomTagModal"),
      // Actions - 文件标签高亮
      setHighlightedFileTags: (tags) => set({ highlightedFileTags: tags }, false, "setHighlightedFileTags"),
      clearHighlightedFileTags: () => set({ highlightedFileTags: /* @__PURE__ */ new Set() }, false, "clearHighlightedFileTags"),
      // Actions - 原始图片高亮
      setOriginImageId: (imageId) => set({ originImageId: imageId }, false, "setOriginImageId"),
      // Actions - 批量操作
      showBatchActionsBar: () => set({ showBatchActions: true }, false, "showBatchActionsBar"),
      hideBatchActionsBar: () => set({ showBatchActions: false }, false, "hideBatchActionsBar")
    }),
    {
      name: "tag-management-store"
    }
  )
);
const TagSearchInput = () => {
  const { searchQuery, setSearchQuery, clearSearch } = useTagManagementStore();
  const handleSearchChange = reactExports.useCallback((e) => {
    setSearchQuery(e.target.value);
  }, [setSearchQuery]);
  reactExports.useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.ctrlKey && e.key === "f") {
        e.preventDefault();
        const searchInput = document.getElementById("tag-search");
        if (searchInput) {
          searchInput.focus();
        }
      } else if (e.key === "Escape" && searchQuery) {
        clearSearch();
      }
    };
    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [searchQuery, clearSearch]);
  const searchIcon = /* @__PURE__ */ jsxRuntimeExports.jsx(
    "svg",
    {
      className: "w-5 h-5 text-gray-400",
      fill: "none",
      stroke: "currentColor",
      viewBox: "0 0 24 24",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx(
        "path",
        {
          strokeLinecap: "round",
          strokeLinejoin: "round",
          strokeWidth: 2,
          d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
        }
      )
    }
  );
  const clearIcon = searchQuery ? /* @__PURE__ */ jsxRuntimeExports.jsx(
    "button",
    {
      onClick: clearSearch,
      className: "text-gray-400 hover:text-gray-600 transition-colors",
      title: "清除搜索",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx(
        "svg",
        {
          className: "w-5 h-5",
          fill: "none",
          stroke: "currentColor",
          viewBox: "0 0 24 24",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(
            "path",
            {
              strokeLinecap: "round",
              strokeLinejoin: "round",
              strokeWidth: 2,
              d: "M6 18L18 6M6 6l12 12"
            }
          )
        }
      )
    }
  ) : null;
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "relative", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      Input,
      {
        id: "tag-search",
        type: "text",
        placeholder: "搜索标签...",
        value: searchQuery,
        onChange: handleSearchChange,
        leftIcon: searchIcon,
        rightIcon: clearIcon,
        className: "w-full"
      }
    ),
    searchQuery && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "absolute top-full left-0 right-0 mt-1 text-xs text-gray-500 bg-white border border-gray-200 rounded-md px-2 py-1 shadow-sm z-10", children: [
      '搜索: "',
      searchQuery,
      '" (按 ESC 清除)'
    ] })
  ] });
};
const TagStatistics = ({
  totalTags = 0,
  customTags = 0,
  activeFiles = 0
}) => {
  const { filteredFiles } = useTagManagementStore();
  const displayActiveFiles = filteredFiles.length || activeFiles;
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-4 border-b border-gray-200 bg-gray-50", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid grid-cols-3 gap-2 text-center text-sm", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "font-semibold text-blue-600", id: "total-tags", children: totalTags }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-gray-500", children: "总标签" })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "font-semibold text-green-600", id: "custom-tags", children: customTags }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-gray-500", children: "自定义" })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "font-semibold text-purple-600", id: "active-files", children: displayActiveFiles }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-gray-500", children: "文件数" })
    ] })
  ] }) });
};
class TagService {
  // 获取标签树
  static getTagTree(caseId) {
    return __async(this, null, function* () {
      const response = yield apiClient.get(`/api/v1/cases/${caseId}/tags/tree`);
      return response.data;
    });
  }
  // 获取按分类组织的标签
  static getTagsByCategory(caseId) {
    return __async(this, null, function* () {
      const response = yield apiClient.get(
        `/api/v1/cases/${caseId}/tags/by-category`
      );
      return response.data;
    });
  }
  // 获取所有标签
  static getTags() {
    return __async(this, null, function* () {
      const response = yield apiClient.get("/api/v1/tags/");
      return response.data;
    });
  }
  // 创建标签
  static createTag(data) {
    return __async(this, null, function* () {
      const response = yield apiClient.post("/api/v1/tags/", data);
      return response.data;
    });
  }
  // 更新标签
  static updateTag(id, data) {
    return __async(this, null, function* () {
      const response = yield apiClient.put(`/api/v1/tags/${id}`, data);
      return response.data;
    });
  }
  // 删除标签
  static deleteTag(id) {
    return __async(this, null, function* () {
      yield apiClient.delete(`/api/v1/tags/${id}`);
    });
  }
  // 为文件添加标签
  static addTagToFile(fileId, tagId) {
    return __async(this, null, function* () {
      yield apiClient.post(`/api/v1/files/${fileId}/tags/${tagId}`);
    });
  }
  // 从文件移除标签
  static removeTagFromFile(fileId, tagId) {
    return __async(this, null, function* () {
      yield apiClient.delete(`/api/v1/files/${fileId}/tags/${tagId}`);
    });
  }
  // 批量为文件添加标签
  static addTagsToFiles(fileIds, tagIds) {
    return __async(this, null, function* () {
      yield apiClient.post("/api/v1/files/batch-tag", {
        file_ids: fileIds,
        tag_ids: tagIds
      });
    });
  }
  // 批量从文件移除标签
  static removeTagsFromFiles(fileIds, tagIds) {
    return __async(this, null, function* () {
      yield apiClient.post("/api/v1/files/batch-untag", {
        file_ids: fileIds,
        tag_ids: tagIds
      });
    });
  }
  // 根据标签筛选文件
  static getFilesByTags(caseId, tagIds, operator = "AND") {
    return __async(this, null, function* () {
      const response = yield apiClient.get(
        `/api/v1/cases/${caseId}/files/by-tags`,
        {
          params: {
            tag_ids: tagIds.join(","),
            operator
          }
        }
      );
      return response.data;
    });
  }
  // 搜索标签
  static searchTags(caseId, query) {
    return __async(this, null, function* () {
      const response = yield apiClient.get(`/api/v1/cases/${caseId}/tags/search`, {
        params: { q: query }
      });
      return response.data;
    });
  }
  // 获取文件的标签
  static getFileTags(fileId) {
    return __async(this, null, function* () {
      const response = yield apiClient.get(
        `/api/v1/files/${fileId}/tags`
      );
      return response.data;
    });
  }
  // 文件名提取功能
  static extractFilenameTagsPreview(caseId, fileId, rules) {
    return __async(this, null, function* () {
      const response = yield apiClient.post(
        `/api/v1/cases/${caseId}/files/${fileId}/preview-filename-extraction`,
        {
          file_ids: [fileId],
          extraction_rules: rules,
          options: {
            overwrite_existing: false,
            skip_on_error: true
          }
        }
      );
      return response.data;
    });
  }
  static extractFilenameTags(_0, _1, _2) {
    return __async(this, arguments, function* (caseId, fileIds, rules, options = {}) {
      const response = yield apiClient.post(
        `/api/v1/cases/${caseId}/files/extract-filename-tags`,
        {
          file_ids: fileIds,
          extraction_rules: rules,
          options: {
            overwrite_existing: options.overwrite_existing || false,
            skip_on_error: options.skip_on_error !== false
          }
        }
      );
      return response.data;
    });
  }
  static getExtractionTaskStatus(taskId) {
    return __async(this, null, function* () {
      const response = yield apiClient.get(
        `/api/v1/tasks/filename-extraction/${taskId}/status`
      );
      return response.data;
    });
  }
}
const useTagTree = (caseId) => {
  const { addNotification } = useAppStore();
  return useQuery({
    queryKey: queryKeys.tagTree(caseId),
    queryFn: () => TagService.getTagTree(caseId),
    enabled: !!caseId,
    onError: (error) => {
      addNotification({
        type: "error",
        title: "获取标签树失败",
        message: error.message || "无法获取标签树结构"
      });
    }
  });
};
const useCustomTags = (caseId) => {
  const { addNotification } = useAppStore();
  return useQuery({
    queryKey: ["custom-tags", caseId],
    queryFn: () => __async(null, null, function* () {
      const response = yield fetch(`http://localhost:8000/api/v1/cases/${caseId}/custom-tags`);
      if (!response.ok) throw new Error("Failed to fetch custom tags");
      return response.json();
    }),
    enabled: !!caseId,
    onError: (error) => {
      addNotification({
        type: "error",
        title: "获取自定义标签失败",
        message: error.message || "无法获取自定义标签"
      });
    }
  });
};
const useCaseInfo = (caseId) => {
  const { addNotification } = useAppStore();
  return useQuery({
    queryKey: queryKeys.case(caseId),
    queryFn: () => CaseService.getCase(caseId),
    enabled: !!caseId,
    onError: (error) => {
      addNotification({
        type: "error",
        title: "获取案例信息失败",
        message: error.message || "无法获取案例信息"
      });
    }
  });
};
const useCreateCustomTag = () => {
  const queryClient = useQueryClient();
  const { addNotification } = useAppStore();
  const { caseId, closeCustomTagModal } = useTagManagementStore();
  return useMutation({
    mutationFn: (_0) => __async(null, [_0], function* ({ name, color }) {
      if (!caseId) throw new Error("Case ID is required");
      const response = yield fetch(`http://localhost:8000/api/v1/cases/${caseId}/custom-tags`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name, color })
      });
      if (!response.ok) throw new Error("Failed to create custom tag");
      return response.json();
    }),
    onSuccess: (newTag) => {
      queryClient.invalidateQueries({ queryKey: ["custom-tags", caseId] });
      queryClient.invalidateQueries({ queryKey: queryKeys.tagTree(caseId) });
      closeCustomTagModal();
      addNotification({
        type: "success",
        title: "标签创建成功",
        message: `自定义标签 "${newTag.name}" 已成功创建`
      });
    },
    onError: (error) => {
      addNotification({
        type: "error",
        title: "创建标签失败",
        message: error.message || "无法创建自定义标签"
      });
    }
  });
};
const useUpdateCustomTag = () => {
  const queryClient = useQueryClient();
  const { addNotification } = useAppStore();
  const { caseId, closeCustomTagModal } = useTagManagementStore();
  return useMutation({
    mutationFn: (_0) => __async(null, [_0], function* ({
      tagId,
      name,
      color
    }) {
      if (!caseId) throw new Error("Case ID is required");
      const response = yield fetch(`http://localhost:8000/api/v1/cases/${caseId}/custom-tags/${tagId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name, color })
      });
      if (!response.ok) throw new Error("Failed to update custom tag");
      return response.json();
    }),
    onSuccess: (updatedTag) => {
      queryClient.invalidateQueries({ queryKey: ["custom-tags", caseId] });
      queryClient.invalidateQueries({ queryKey: queryKeys.tagTree(caseId) });
      closeCustomTagModal();
      addNotification({
        type: "success",
        title: "标签更新成功",
        message: `自定义标签 "${updatedTag.name}" 已成功更新`
      });
    },
    onError: (error) => {
      addNotification({
        type: "error",
        title: "更新标签失败",
        message: error.message || "无法更新自定义标签"
      });
    }
  });
};
const useDeleteCustomTag = () => {
  const queryClient = useQueryClient();
  const { addNotification } = useAppStore();
  const { caseId } = useTagManagementStore();
  return useMutation({
    mutationFn: (tagId) => __async(null, null, function* () {
      if (!caseId) throw new Error("Case ID is required");
      const response = yield fetch(`http://localhost:8000/api/v1/cases/${caseId}/custom-tags/${tagId}`, {
        method: "DELETE"
      });
      if (!response.ok) throw new Error("Failed to delete custom tag");
    }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["custom-tags", caseId] });
      queryClient.invalidateQueries({ queryKey: queryKeys.tagTree(caseId) });
      addNotification({
        type: "success",
        title: "标签删除成功",
        message: "自定义标签已成功删除"
      });
    },
    onError: (error) => {
      addNotification({
        type: "error",
        title: "删除标签失败",
        message: error.message || "无法删除自定义标签"
      });
    }
  });
};
const TagColorDot = ({
  color = "#6B7280",
  size = "md"
}) => {
  const sizeClasses = {
    sm: "w-2 h-2",
    md: "w-3 h-3",
    lg: "w-4 h-4"
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx(
    "div",
    {
      className: cn("rounded-full flex-shrink-0", sizeClasses[size]),
      style: { backgroundColor: color }
    }
  );
};
const TagCounter = ({
  count,
  variant = "default"
}) => {
  const variantClasses = {
    default: "text-xs text-secondary bg-secondary-bg px-1.5 py-0.5 rounded",
    compact: "text-xs text-tertiary",
    badge: "text-xs text-white bg-gray-500 px-1.5 py-0.5 rounded-full"
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: cn(variantClasses[variant]), children: count });
};
const TagActions = ({
  onEdit,
  onDelete,
  onToggle,
  showEdit = false,
  showDelete = false,
  showToggle = false
}) => {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity", children: [
    showToggle && onToggle && /* @__PURE__ */ jsxRuntimeExports.jsx(
      "button",
      {
        onClick: (e) => {
          e.stopPropagation();
          onToggle();
        },
        className: "p-1 hover:bg-secondary-bg rounded transition-colors",
        title: "切换",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-xs", children: "⚡" })
      }
    ),
    showEdit && onEdit && /* @__PURE__ */ jsxRuntimeExports.jsx(
      "button",
      {
        onClick: (e) => {
          e.stopPropagation();
          onEdit();
        },
        className: "p-1 hover:bg-secondary-bg rounded transition-colors",
        title: "编辑",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-xs", children: "✏️" })
      }
    ),
    showDelete && onDelete && /* @__PURE__ */ jsxRuntimeExports.jsx(
      "button",
      {
        onClick: (e) => {
          e.stopPropagation();
          onDelete();
        },
        className: "p-1 hover:bg-red-100 text-red-600 rounded transition-colors",
        title: "删除",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-xs", children: "🗑️" })
      }
    )
  ] });
};
const TagItem = (_a) => {
  var _b = _a, {
    tag,
    selected = false,
    disabled = false,
    showCount = true,
    showActions = false,
    variant = "default",
    size = "md",
    onClick,
    onEdit,
    onDelete,
    onToggle,
    className
  } = _b, props = __objRest(_b, [
    "tag",
    "selected",
    "disabled",
    "showCount",
    "showActions",
    "variant",
    "size",
    "onClick",
    "onEdit",
    "onDelete",
    "onToggle",
    "className"
  ]);
  const handleClick = (e) => {
    if (disabled) return;
    e.preventDefault();
    onClick == null ? void 0 : onClick(tag);
  };
  const variantClasses = {
    default: cn(
      "flex items-center justify-between p-2 rounded-lg cursor-pointer transition-colors",
      "hover:bg-secondary-bg-hover",
      selected && "bg-highlight-border text-main shadow-sm",
      disabled && "opacity-50 cursor-not-allowed"
    ),
    compact: cn(
      "flex items-center gap-1 px-2 py-1 rounded cursor-pointer transition-colors",
      "hover:bg-secondary-bg-hover",
      selected && "bg-highlight-border text-main",
      disabled && "opacity-50 cursor-not-allowed"
    ),
    badge: cn(
      "inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs cursor-pointer transition-colors",
      "bg-secondary-bg hover:bg-secondary-bg-hover",
      selected && "bg-highlight-border text-white",
      disabled && "opacity-50 cursor-not-allowed"
    ),
    button: cn(
      "inline-flex items-center gap-1 px-3 py-1.5 rounded-md border cursor-pointer transition-colors",
      "border-gray-300 hover:border-gray-400 hover:bg-gray-50",
      selected && "border-highlight-border bg-highlight-border text-white",
      disabled && "opacity-50 cursor-not-allowed"
    )
  };
  const sizeClasses = {
    sm: "text-xs",
    md: "text-sm",
    lg: "text-base"
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(
    "div",
    __spreadProps(__spreadValues({}, props), {
      className: cn(
        "group",
        variantClasses[variant],
        sizeClasses[size],
        className
      ),
      onClick: handleClick,
      "data-tag-id": tag.id,
      "data-tag-name": tag.name,
      title: tag.description || tag.name,
      children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2 flex-1 min-w-0", children: [
          tag.color && /* @__PURE__ */ jsxRuntimeExports.jsx(TagColorDot, { color: tag.color, size }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: cn(
            "font-medium truncate",
            variant === "compact" && "font-normal"
          ), children: tag.name }),
          showCount && tag.count !== void 0 && /* @__PURE__ */ jsxRuntimeExports.jsx(
            TagCounter,
            {
              count: tag.count,
              variant: variant === "badge" ? "badge" : "default"
            }
          )
        ] }),
        showActions && /* @__PURE__ */ jsxRuntimeExports.jsx(
          TagActions,
          {
            onEdit: () => onEdit == null ? void 0 : onEdit(tag),
            onDelete: () => onDelete == null ? void 0 : onDelete(tag),
            onToggle: () => onToggle == null ? void 0 : onToggle(tag),
            showEdit: !!onEdit,
            showDelete: !!onDelete,
            showToggle: !!onToggle
          }
        )
      ]
    })
  );
};
const CustomTagItem = ({
  tag,
  isSelected = false,
  onSelect,
  onEdit
}) => {
  const { openCustomTagModal } = useTagManagementStore();
  const deleteCustomTag = useDeleteCustomTag();
  const handleClick = () => {
    if (onSelect) {
      onSelect(tag);
    }
  };
  const handleEdit = (e) => {
    if (onEdit) {
      onEdit(tag);
    } else {
      openCustomTagModal(tag);
    }
  };
  const handleDelete = (e) => __async(null, null, function* () {
    if (window.confirm(`确定要删除标签 "${tag.name}" 吗？`)) {
      try {
        yield deleteCustomTag.mutateAsync(tag.id);
      } catch (error) {
        console.error("删除标签失败:", error);
      }
    }
  });
  const tagData = {
    id: tag.id.toString(),
    name: tag.name,
    color: tag.color,
    count: tag.file_count
  };
  const handleEditWrapper = () => {
    handleEdit();
  };
  const handleDeleteWrapper = () => {
    handleDelete();
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx(
    TagItem,
    {
      tag: tagData,
      selected: isSelected,
      showCount: true,
      showActions: true,
      onClick: () => handleClick(),
      onEdit: handleEditWrapper,
      onDelete: handleDeleteWrapper,
      className: cn(
        "custom-tag-item",
        "border border-transparent",
        isSelected && "border-blue-200 selected"
      ),
      "data-tag-id": tag.id,
      "data-tag-name": tag.name
    }
  );
};
const CustomTagSection = () => {
  const {
    caseId,
    searchQuery,
    selectedTagType,
    selectedTagKey,
    selectTag,
    openCustomTagModal
  } = useTagManagementStore();
  const { data: customTags = [], isLoading, error } = useCustomTags(caseId);
  const filteredTags = React.useMemo(() => {
    if (!searchQuery) return customTags;
    return customTags.filter(
      (tag) => tag.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [customTags, searchQuery]);
  const handleTagSelect = (tag) => {
    selectTag("custom", tag.id.toString(), tag.name);
  };
  const handleAddCustomTag = () => {
    openCustomTagModal();
  };
  if (error) {
    return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-4 border-b border-gray-200", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-red-600 text-sm", children: "加载自定义标签失败" }) });
  }
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-4 border-b border-gray-200", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between mb-3", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "font-semibold text-gray-900", children: "自定义标签" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "button",
        {
          id: "add-custom-tag-btn",
          onClick: handleAddCustomTag,
          className: "text-blue-600 hover:text-blue-800 p-1 transition-colors",
          title: "添加自定义标签",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-5 h-5", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
            "path",
            {
              strokeLinecap: "round",
              strokeLinejoin: "round",
              strokeWidth: 2,
              d: "M12 6v6m0 0v6m0-6h6m-6 0H6"
            }
          ) })
        }
      )
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { id: "custom-tags-list", className: "space-y-2", children: isLoading ? /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center py-4", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "mt-1 text-xs text-gray-500", children: "加载中..." })
    ] }) : filteredTags.length === 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-center py-4 text-gray-500 text-sm", children: searchQuery ? "没有找到匹配的标签" : "暂无自定义标签" }) : filteredTags.map((tag) => /* @__PURE__ */ jsxRuntimeExports.jsx(
      CustomTagItem,
      {
        tag,
        isSelected: selectedTagType === "custom" && selectedTagKey === tag.id.toString(),
        onSelect: handleTagSelect
      },
      tag.id
    )) }),
    searchQuery && filteredTags.length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mt-2 text-xs text-gray-500", children: [
      "找到 ",
      filteredTags.length,
      " 个匹配的自定义标签"
    ] })
  ] });
};
const SystemTagItem = ({
  category,
  tagKey,
  tagValue,
  fileCount,
  isSelected = false,
  isHighlighted = false,
  onSelect
}) => {
  const handleClick = () => {
    if (onSelect) {
      onSelect(category, tagKey, tagValue);
    }
  };
  const getDisplayText = () => {
    if (tagKey === tagValue) {
      return tagKey;
    }
    return `${tagKey} = ${tagValue}`;
  };
  const getTagIcon = () => {
    switch (category) {
      case "properties":
        return "⭐";
      case "metadata":
        return "📋";
      case "cv":
        return "🤖";
      case "user":
        return "👤";
      case "ai":
        return "🧠";
      default:
        return "🏷️";
    }
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(
    "div",
    {
      className: cn(
        "tag-item flex items-center justify-between p-2 rounded-lg cursor-pointer transition-colors",
        "hover:bg-gray-50 border border-transparent",
        isSelected && "bg-blue-50 border-blue-200 selected",
        isHighlighted && !isSelected && "bg-yellow-50 border-yellow-200 highlighted"
      ),
      onClick: handleClick,
      "data-category": category,
      "data-tag-name": tagKey,
      "data-tag-value": tagValue,
      children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2 flex-1 min-w-0", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm flex-shrink-0", children: getTagIcon() }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm text-gray-900 truncate", children: getDisplayText() })
        ] }),
        fileCount !== void 0 && fileCount > 0 && /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-xs text-gray-500 bg-gray-100 px-1.5 py-0.5 rounded flex-shrink-0", children: fileCount })
      ]
    }
  );
};
const TagCategory = ({
  category,
  displayName,
  tags,
  searchQuery = ""
}) => {
  const {
    expandedCategories,
    toggleCategoryExpansion,
    selectedTags,
    toggleTagSelection,
    highlightedFileTags
  } = useTagManagementStore();
  const isExpanded = expandedCategories.has(category);
  const filteredTags = React.useMemo(() => {
    if (!searchQuery) return tags;
    const filtered = {};
    Object.entries(tags).forEach(([key, value]) => {
      const searchLower = searchQuery.toLowerCase();
      const keyLower = key.toLowerCase();
      const valueLower = typeof value === "string" ? value.toLowerCase() : "";
      if (keyLower.includes(searchLower) || valueLower.includes(searchLower)) {
        filtered[key] = value;
      }
    });
    return filtered;
  }, [tags, searchQuery]);
  const tagCount = Object.keys(filteredTags).length;
  const totalTagCount = Object.keys(tags).length;
  const handleToggleExpansion = () => {
    toggleCategoryExpansion(category);
  };
  const handleTagSelect = (category2, tagKey, tagValue) => {
    const tagId = `${category2}:${tagKey}:${tagValue}`;
    toggleTagSelection(tagId);
  };
  if (searchQuery && tagCount === 0) {
    return null;
  }
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mb-4", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs(
      "div",
      {
        className: "tag-category-header flex items-center justify-between p-2 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors",
        onClick: handleToggleExpansion,
        "data-category": category,
        children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "svg",
              {
                className: cn(
                  "w-4 h-4 transform transition-transform",
                  isExpanded && "rotate-90"
                ),
                fill: "none",
                stroke: "currentColor",
                viewBox: "0 0 24 24",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                  "path",
                  {
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: 2,
                    d: "M9 5l7 7-7 7"
                  }
                )
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium text-gray-900", children: displayName })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "tag-count text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded", children: searchQuery && tagCount !== totalTagCount ? `${tagCount}/${totalTagCount}` : totalTagCount })
        ]
      }
    ),
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      "div",
      {
        className: cn(
          "tag-category-content ml-4 mt-2 space-y-1 transition-all duration-200",
          !isExpanded && "hidden"
        ),
        id: `${category}-tags`,
        children: tagCount === 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-sm text-gray-500 py-2", children: "暂无标签" }) : Object.entries(filteredTags).map(([key, value]) => {
          const parts = key.split(": ");
          const tagKey = parts[0];
          const tagValue = parts[1] || key;
          const tagId = `${category}:${tagKey}:${tagValue}`;
          const isSelected = selectedTags.has(tagId);
          const isHighlighted = highlightedFileTags.has(tagId);
          return /* @__PURE__ */ jsxRuntimeExports.jsx(
            SystemTagItem,
            {
              category,
              tagKey,
              tagValue,
              fileCount: typeof value === "number" ? value : void 0,
              isSelected,
              isHighlighted,
              onSelect: handleTagSelect
            },
            tagId
          );
        })
      }
    ),
    searchQuery && tagCount > 0 && tagCount !== totalTagCount && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "ml-4 mt-1 text-xs text-gray-500", children: [
      "找到 ",
      tagCount,
      " 个匹配的标签"
    ] })
  ] });
};
const SystemTagSection = () => {
  var _a, _b, _c, _d;
  const { caseId, searchQuery } = useTagManagementStore();
  const { data: caseData, isLoading, error } = useCaseInfo(caseId);
  const tagTree = React.useMemo(() => {
    if (!(caseData == null ? void 0 : caseData.files)) {
      console.log("🔍 SystemTagSection: 没有案例数据或文件");
      return null;
    }
    console.log("🔍 SystemTagSection: 开始构建标签树，文件数量:", caseData.files.length);
    const aggregatedTags = {
      properties: {},
      metadata: {},
      cv: {},
      user: {},
      ai: {}
    };
    caseData.files.forEach((file, index) => {
      var _a2, _b2, _c2, _d2;
      console.log(`🔍 处理文件 ${index + 1}:`, file.file_name);
      console.log("🔍 文件tags结构:", file.tags);
      if ((_a2 = file.tags) == null ? void 0 : _a2.properties) {
        console.log("🔍 发现属性标签:", file.tags.properties);
        Object.entries(file.tags.properties).forEach(([key, value]) => {
          const tagKey = `${key}: ${value}`;
          aggregatedTags.properties[tagKey] = (aggregatedTags.properties[tagKey] || 0) + 1;
        });
      }
      if ((_c2 = (_b2 = file.tags) == null ? void 0 : _b2.tags) == null ? void 0 : _c2.metadata) {
        console.log("🔍 发现元数据标签:", file.tags.tags.metadata);
        Object.entries(file.tags.tags.metadata).forEach(([key, value]) => {
          const tagKey = `${key}: ${value}`;
          aggregatedTags.metadata[tagKey] = (aggregatedTags.metadata[tagKey] || 0) + 1;
        });
      } else {
        console.log("🔍 未发现元数据标签，tags结构:", (_d2 = file.tags) == null ? void 0 : _d2.tags);
      }
    });
    console.log("🔍 最终聚合结果:");
    console.log("  属性标签:", aggregatedTags.properties);
    console.log("  元数据标签:", aggregatedTags.metadata);
    return {
      tags: {
        metadata: aggregatedTags.metadata,
        cv: aggregatedTags.cv,
        user: aggregatedTags.user,
        ai: aggregatedTags.ai
      },
      properties: aggregatedTags.properties,
      custom: []
    };
  }, [caseData == null ? void 0 : caseData.files]);
  if (error) {
    return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-4", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-red-600 text-sm", children: "加载系统标签失败" }) });
  }
  if (isLoading) {
    return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-4", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "font-semibold text-gray-900 mb-3", children: "系统标签" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center py-4", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "mt-1 text-xs text-gray-500", children: "加载中..." })
      ] })
    ] });
  }
  const tagCategories = [
    {
      key: "properties",
      displayName: "属性标签",
      data: (tagTree == null ? void 0 : tagTree.properties) || {}
    },
    {
      key: "metadata",
      displayName: "元数据标签",
      data: ((_a = tagTree == null ? void 0 : tagTree.tags) == null ? void 0 : _a.metadata) || {}
    },
    {
      key: "cv",
      displayName: "计算机视觉",
      data: ((_b = tagTree == null ? void 0 : tagTree.tags) == null ? void 0 : _b.cv) || {}
    },
    {
      key: "user",
      displayName: "用户标签",
      data: ((_c = tagTree == null ? void 0 : tagTree.tags) == null ? void 0 : _c.user) || {}
    },
    {
      key: "ai",
      displayName: "AI标签",
      data: ((_d = tagTree == null ? void 0 : tagTree.tags) == null ? void 0 : _d.ai) || {}
    }
  ];
  const totalSystemTags = tagCategories.reduce(
    (total, category) => total + Object.keys(category.data).length,
    0
  );
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-4", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("h3", { className: "font-semibold text-gray-900 mb-3", children: [
      "系统标签",
      totalSystemTags > 0 && /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "ml-2 text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded", children: totalSystemTags })
    ] }),
    totalSystemTags === 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-center py-8 text-gray-500 text-sm", children: "暂无系统标签" }) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "space-y-2", children: tagCategories.map((category) => /* @__PURE__ */ jsxRuntimeExports.jsx(
      TagCategory,
      {
        category: category.key,
        displayName: category.displayName,
        tags: category.data,
        searchQuery
      },
      category.key
    )) }),
    searchQuery && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mt-4 p-2 bg-blue-50 rounded-lg", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-xs text-blue-700", children: [
      '搜索 "',
      searchQuery,
      '" 的结果'
    ] }) })
  ] });
};
const TagPanel = () => {
  const navigate = useNavigate();
  const { caseId, panelWidth } = useTagManagementStore();
  const { data: caseInfo } = useCaseInfo(caseId);
  const { data: customTags = [] } = useCustomTags(caseId);
  const { data: tagTree } = useTagTree(caseId);
  const customTagsCount = customTags.length;
  const systemTagsCount = React.useMemo(() => {
    var _a, _b, _c, _d;
    if (!tagTree) return 0;
    let count = 0;
    count += Object.keys(tagTree.properties || {}).length;
    count += Object.keys(((_a = tagTree.tags) == null ? void 0 : _a.metadata) || {}).length;
    count += Object.keys(((_b = tagTree.tags) == null ? void 0 : _b.cv) || {}).length;
    count += Object.keys(((_c = tagTree.tags) == null ? void 0 : _c.user) || {}).length;
    count += Object.keys(((_d = tagTree.tags) == null ? void 0 : _d.ai) || {}).length;
    return count;
  }, [tagTree]);
  const totalTags = customTagsCount + systemTagsCount;
  const handleBack = () => {
    navigate(`/cases/${caseId}`);
  };
  const handleRefresh = () => {
    window.location.reload();
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(
    "div",
    {
      id: "tag-panel",
      className: "bg-white border-r border-gray-200 flex flex-col",
      style: {
        width: `${panelWidth}px`,
        minWidth: "300px",
        maxWidth: "600px"
      },
      children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-4 border-b border-gray-200", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between mb-4", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("h1", { className: "text-xl font-bold text-gray-900", children: "标签管理" }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                "button",
                {
                  id: "back-btn",
                  onClick: handleBack,
                  className: "text-gray-500 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-100 transition-colors",
                  title: "返回案例",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-5 h-5", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                    "path",
                    {
                      strokeLinecap: "round",
                      strokeLinejoin: "round",
                      strokeWidth: 2,
                      d: "M10 19l-7-7m0 0l7-7m-7 7h18"
                    }
                  ) })
                }
              ),
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                "button",
                {
                  id: "refresh-btn",
                  onClick: handleRefresh,
                  className: "text-gray-500 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-100 transition-colors",
                  title: "刷新",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-5 h-5", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                    "path",
                    {
                      strokeLinecap: "round",
                      strokeLinejoin: "round",
                      strokeWidth: 2,
                      d: "M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                    }
                  ) })
                }
              )
            ] })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { id: "case-info", className: "mb-4 p-3 bg-blue-50 rounded-lg", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-sm text-blue-600", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("span", { id: "case-name", children: (caseInfo == null ? void 0 : caseInfo.name) || "加载中..." }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "mx-2", children: "•" }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { id: "case-id", children: [
              "ID: ",
              caseId || "--"
            ] })
          ] }) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(TagSearchInput, {})
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          TagStatistics,
          {
            totalTags,
            customTags: customTagsCount
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex-1 overflow-y-auto", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(CustomTagSection, {}),
          /* @__PURE__ */ jsxRuntimeExports.jsx(SystemTagSection, {})
        ] })
      ]
    }
  );
};
const ImageThumbnail = React.memo(({
  file,
  caseId,
  isSelectionMode: propIsSelectionMode,
  isSelected: propIsSelected = false,
  isHighlighted = false,
  onSelect
}) => {
  const {
    isSelectionMode: storeIsSelectionMode,
    selectedFiles,
    sourceFileId,
    toggleFileSelection,
    openImageModal,
    setHighlightedFileTags,
    clearHighlightedFileTags,
    originImageId
  } = useTagManagementStore();
  const isSelectionMode = propIsSelectionMode !== void 0 ? propIsSelectionMode : storeIsSelectionMode;
  const isSelected = propIsSelected !== void 0 ? propIsSelected : selectedFiles.has(file.id);
  const isSourceFile = sourceFileId === file.id;
  const isOriginImage = originImageId === file.id;
  const handleClick = reactExports.useCallback(() => {
    if (isSelectionMode) {
      if (onSelect) {
        onSelect(file, !isSelected);
      } else {
        toggleFileSelection(file.id);
      }
    } else {
      openImageModal(file.id);
    }
  }, [isSelectionMode, isSelected, onSelect, toggleFileSelection, openImageModal, file]);
  const handleCheckboxChange = reactExports.useCallback((e) => {
    e.stopPropagation();
    if (onSelect) {
      onSelect(file, !isSelected);
    } else {
      toggleFileSelection(file.id);
    }
  }, [isSelected, onSelect, toggleFileSelection, file]);
  const getFileTagIds = reactExports.useCallback(() => {
    var _a, _b, _c, _d, _e;
    const tagIds = /* @__PURE__ */ new Set();
    if ((_a = file.tags) == null ? void 0 : _a.properties) {
      Object.entries(file.tags.properties).forEach(([key, value]) => {
        tagIds.add(`properties:${key}:${value}`);
      });
    }
    if ((_b = file.ttags) == null ? void 0 : _b.metadata) {
      Object.entries(file.ttags.metadata).forEach(([key, value]) => {
        tagIds.add(`metadata:${key}:${value}`);
      });
    }
    if ((_c = file.ttags) == null ? void 0 : _c.cv) {
      Object.entries(file.ttags.cv).forEach(([key, value]) => {
        tagIds.add(`cv:${key}:${value}`);
      });
    }
    if (Array.isArray((_d = file.ttags) == null ? void 0 : _d.user)) {
      file.ttags.user.forEach((tag) => {
        tagIds.add(`user:${tag}`);
      });
    }
    if (Array.isArray((_e = file.ttags) == null ? void 0 : _e.ai)) {
      file.ttags.ai.forEach((tag) => {
        tagIds.add(`ai:${tag}`);
      });
    }
    return tagIds;
  }, [file]);
  const handleMouseEnter = reactExports.useCallback(() => {
    const tagIds = getFileTagIds();
    setHighlightedFileTags(tagIds);
  }, [getFileTagIds, setHighlightedFileTags]);
  const handleMouseLeave = reactExports.useCallback(() => {
    clearHighlightedFileTags();
  }, [clearHighlightedFileTags]);
  const imageUrl = reactExports.useMemo(() => {
    if (file.thumbnail_small_path) {
      return `file://${file.thumbnail_small_path}`;
    }
    return `/api/v1/cases/${caseId}/files/${file.id}/thumbnail`;
  }, [file.thumbnail_small_path, caseId, file.id]);
  const formattedFileSize = reactExports.useMemo(() => {
    const bytes = file.file_size;
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
  }, [file.file_size]);
  const formattedDate = reactExports.useMemo(() => {
    return new Date(file.created_at).toLocaleDateString("zh-CN");
  }, [file.created_at]);
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(
    "div",
    {
      className: cn(
        "image-card relative bg-white rounded-lg shadow-sm border transition-all duration-200 cursor-pointer",
        "hover:shadow-md hover:scale-105",
        isSelected && "ring-2 ring-blue-500 border-blue-500",
        isSourceFile && "ring-2 ring-yellow-500 border-yellow-500",
        isHighlighted && "ring-2 ring-green-500 border-green-500",
        isOriginImage && "ring-4 ring-orange-500 border-orange-500 shadow-lg"
      ),
      onClick: handleClick,
      onMouseEnter: handleMouseEnter,
      onMouseLeave: handleMouseLeave,
      "data-file-id": file.id,
      children: [
        isSelectionMode && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "absolute top-2 left-2 z-10", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
          "input",
          {
            type: "checkbox",
            checked: isSelected,
            onChange: handleCheckboxChange,
            className: "file-checkbox w-4 h-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500"
          }
        ) }),
        isSourceFile && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "absolute top-2 right-2 z-10", children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "bg-yellow-500 text-white text-xs px-2 py-1 rounded", children: "源文件" }) }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "image-preview aspect-square overflow-hidden rounded-t-lg", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
          "img",
          {
            src: imageUrl,
            alt: file.filename,
            className: "w-full h-full object-cover",
            loading: "lazy",
            onError: (e) => {
              const target = e.target;
              target.src = "/placeholder-image.png";
            }
          }
        ) }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-3", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("h4", { className: "font-medium text-sm text-gray-900 truncate mb-1", children: file.filename }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-xs text-gray-500 space-y-1", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: "尺寸" }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { children: [
                file.width || 0,
                " × ",
                file.height || 0
              ] })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: "大小" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: formattedFileSize })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: "日期" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: formattedDate })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: "类型" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: file.file_type || "未知" })
            ] })
          ] }),
          file.tags && file.tags.length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mt-2 pt-2 border-t border-gray-100", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-wrap gap-1", children: [
            file.tags.slice(0, 3).map((tag, index) => /* @__PURE__ */ jsxRuntimeExports.jsx(
              "span",
              {
                className: "inline-block bg-blue-100 text-blue-800 text-xs px-1.5 py-0.5 rounded",
                children: tag.name
              },
              index
            )),
            file.tags.length > 3 && /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "text-xs text-gray-500", children: [
              "+",
              file.tags.length - 3
            ] })
          ] }) })
        ] })
      ]
    }
  );
});
const FilenameExtractionModal = ({
  isOpen,
  onClose,
  selectedFiles,
  onExtract
}) => {
  const [exampleFilename, setExampleFilename] = reactExports.useState("");
  const [rules, setRules] = reactExports.useState([]);
  const [isSelecting, setIsSelecting] = reactExports.useState(false);
  const [selectionStart, setSelectionStart] = reactExports.useState(null);
  const [selectionEnd, setSelectionEnd] = reactExports.useState(null);
  const [editingRuleId, setEditingRuleId] = reactExports.useState(null);
  const [newTagName, setNewTagName] = reactExports.useState("");
  reactExports.useEffect(() => {
    if (selectedFiles.length > 0) {
      const filename = selectedFiles[0].file_name;
      const nameWithoutExt = filename.replace(/\.[^/.]+$/, "");
      setExampleFilename(nameWithoutExt);
    }
  }, [selectedFiles]);
  const resetModal = reactExports.useCallback(() => {
    setRules([]);
    setIsSelecting(false);
    setSelectionStart(null);
    setSelectionEnd(null);
    setEditingRuleId(null);
    setNewTagName("");
  }, []);
  const handleTextSelection = reactExports.useCallback(() => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;
    const range = selection.getRangeAt(0);
    const container = range.commonAncestorContainer.parentElement;
    if (!(container == null ? void 0 : container.classList.contains("filename-example"))) return;
    const start = range.startOffset;
    const end = range.endOffset;
    const selectedText = exampleFilename.slice(start, end);
    if (selectedText && start !== end) {
      const hasOverlap = rules.some(
        (rule) => !(end <= rule.start || start >= rule.end)
      );
      if (hasOverlap) {
        alert("选择的文本与现有规则重叠，请重新选择");
        return;
      }
      setSelectionStart(start);
      setSelectionEnd(end);
      setIsSelecting(true);
      selection.removeAllRanges();
    }
  }, [exampleFilename, rules]);
  const addRule = reactExports.useCallback(() => {
    if (selectionStart === null || selectionEnd === null || !newTagName.trim()) {
      return;
    }
    const selectedText = exampleFilename.slice(selectionStart, selectionEnd);
    const newRule = {
      id: `rule_${Date.now()}`,
      start: selectionStart,
      end: selectionEnd,
      tagName: newTagName.trim(),
      selectedText,
      description: `提取 "${selectedText}" 作为 ${newTagName.trim()}`
    };
    setRules((prev) => [...prev, newRule].sort((a, b) => a.start - b.start));
    setIsSelecting(false);
    setSelectionStart(null);
    setSelectionEnd(null);
    setNewTagName("");
  }, [selectionStart, selectionEnd, newTagName, exampleFilename]);
  const removeRule = reactExports.useCallback((ruleId) => {
    setRules((prev) => prev.filter((rule) => rule.id !== ruleId));
  }, []);
  const renderHighlightedFilename = () => {
    if (!exampleFilename) return null;
    const parts = [];
    let lastIndex = 0;
    const sortedRules = [...rules].sort((a, b) => a.start - b.start);
    sortedRules.forEach((rule, index) => {
      if (rule.start > lastIndex) {
        parts.push(
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-gray-700", children: exampleFilename.slice(lastIndex, rule.start) }, `text-${index}`)
        );
      }
      parts.push(
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "span",
          {
            className: "bg-blue-200 text-blue-800 px-1 rounded font-medium cursor-pointer hover:bg-blue-300",
            title: rule.description,
            children: rule.selectedText
          },
          rule.id
        )
      );
      lastIndex = rule.end;
    });
    if (lastIndex < exampleFilename.length) {
      parts.push(
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-gray-700", children: exampleFilename.slice(lastIndex) }, "text-end")
      );
    }
    return parts;
  };
  const generatePreviewTags = () => {
    return rules.map((rule) => ({
      name: rule.tagName,
      value: rule.selectedText
    }));
  };
  const handleExtract = () => {
    if (rules.length === 0) {
      alert("请至少配置一个提取规则");
      return;
    }
    onExtract(rules);
    resetModal();
    onClose();
  };
  const handleClose = () => {
    resetModal();
    onClose();
  };
  if (!isOpen) return null;
  return /* @__PURE__ */ jsxRuntimeExports.jsx(
    Modal,
    {
      isOpen,
      onClose: handleClose,
      title: `📝 从文件名提取标签 (共选中 ${selectedFiles.length} 个文件)`,
      size: "large",
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-6", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-lg font-medium text-gray-900 mb-3", children: "示例文件名配置" }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "bg-gray-50 p-4 rounded-lg border", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-sm text-gray-600 mb-2", children: "请在下方文件名中拖拽选择要提取的字段：" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "div",
              {
                className: "filename-example text-lg font-mono bg-white p-3 rounded border cursor-text select-text",
                onMouseUp: handleTextSelection,
                children: renderHighlightedFilename()
              }
            )
          ] })
        ] }),
        isSelecting && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "bg-blue-50 p-4 rounded-lg border border-blue-200", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("h4", { className: "font-medium text-blue-900 mb-2", children: "为选中的字段命名标签" }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("p", { className: "text-sm text-blue-700 mb-3", children: [
            '选中文本: "',
            exampleFilename.slice(selectionStart, selectionEnd),
            '"'
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              Input,
              {
                value: newTagName,
                onChange: (e) => setNewTagName(e.target.value),
                placeholder: "输入标签名称，如：作者、拍摄年代等",
                className: "flex-1",
                onKeyPress: (e) => e.key === "Enter" && addRule(),
                autoFocus: true
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { onClick: addRule, disabled: !newTagName.trim(), children: "添加规则" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              Button,
              {
                variant: "secondary",
                onClick: () => {
                  setIsSelecting(false);
                  setSelectionStart(null);
                  setSelectionEnd(null);
                  setNewTagName("");
                },
                children: "取消"
              }
            )
          ] })
        ] }),
        rules.length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-lg font-medium text-gray-900 mb-3", children: "已配置的标签规则" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "space-y-2", children: rules.map((rule) => /* @__PURE__ */ jsxRuntimeExports.jsxs(
            "div",
            {
              className: "flex items-center justify-between bg-gray-50 p-3 rounded-lg",
              children: [
                /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex-1", children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "font-medium text-gray-900", children: [
                    rule.tagName,
                    ":"
                  ] }),
                  /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "ml-2 text-blue-600 font-mono", children: [
                    '"',
                    rule.selectedText,
                    '"'
                  ] }),
                  /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "ml-2 text-sm text-gray-500", children: [
                    "(字符 ",
                    rule.start,
                    "-",
                    rule.end,
                    ")"
                  ] })
                ] }),
                /* @__PURE__ */ jsxRuntimeExports.jsx(
                  Button,
                  {
                    variant: "secondary",
                    size: "small",
                    onClick: () => removeRule(rule.id),
                    children: "删除"
                  }
                )
              ]
            },
            rule.id
          )) })
        ] }),
        rules.length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-lg font-medium text-gray-900 mb-3", children: "实时预览" }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "bg-green-50 p-4 rounded-lg border border-green-200", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-sm text-green-700 mb-3", children: "将为每个文件生成以下标签:" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex flex-wrap gap-2", children: generatePreviewTags().map((tag, index) => /* @__PURE__ */ jsxRuntimeExports.jsxs(
              "span",
              {
                className: "inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800",
                children: [
                  "🏷️ ",
                  tag.name,
                  ": ",
                  tag.value
                ]
              },
              index
            )) })
          ] })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between pt-4 border-t", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { variant: "secondary", onClick: handleClose, children: "取消" }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2", children: [
            rules.length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsx(
              Button,
              {
                variant: "secondary",
                onClick: () => setRules([]),
                children: "清除所有规则"
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsxs(
              Button,
              {
                onClick: handleExtract,
                disabled: rules.length === 0,
                children: [
                  "为 ",
                  selectedFiles.length,
                  " 个文件生成标签"
                ]
              }
            )
          ] })
        ] })
      ] })
    }
  );
};
const useFilenameExtraction = (caseId) => {
  const { addNotification } = useAppStore();
  const [activeTaskId, setActiveTaskId] = reactExports.useState(null);
  const previewExtraction = useMutation({
    mutationFn: (_0) => __async(null, [_0], function* ({
      fileId,
      rules
    }) {
      return TagService.extractFilenameTagsPreview(caseId, fileId, rules);
    }),
    onError: (error) => {
      var _a, _b;
      addNotification({
        type: "error",
        title: "预览失败",
        message: ((_b = (_a = error.response) == null ? void 0 : _a.data) == null ? void 0 : _b.detail) || "无法预览提取结果"
      });
    }
  });
  const extractTags = useMutation({
    mutationFn: (_0) => __async(null, [_0], function* ({
      fileIds,
      rules,
      options = {}
    }) {
      return TagService.extractFilenameTags(caseId, fileIds, rules, options);
    }),
    onSuccess: (response) => {
      setActiveTaskId(response.task_id);
      addNotification({
        type: "success",
        title: "提取任务已启动",
        message: `正在为 ${response.files_count} 个文件提取标签，预计需要 ${response.estimated_time}`
      });
    },
    onError: (error) => {
      var _a, _b;
      addNotification({
        type: "error",
        title: "提取失败",
        message: ((_b = (_a = error.response) == null ? void 0 : _a.data) == null ? void 0 : _b.detail) || "无法启动标签提取任务"
      });
    }
  });
  const { data: taskStatus, isLoading: isTaskStatusLoading } = useQuery({
    queryKey: ["extraction-task-status", activeTaskId],
    queryFn: () => TagService.getExtractionTaskStatus(activeTaskId),
    enabled: !!activeTaskId,
    refetchInterval: (data) => {
      if ((data == null ? void 0 : data.status) === "completed" || (data == null ? void 0 : data.status) === "failed") {
        return false;
      }
      return 2e3;
    },
    onSuccess: (data) => {
      if (data.status === "completed") {
        addNotification({
          type: "success",
          title: "标签提取完成",
          message: `成功处理了 ${data.progress.completed} 个文件`
        });
        setActiveTaskId(null);
      } else if (data.status === "failed") {
        addNotification({
          type: "error",
          title: "标签提取失败",
          message: "任务执行过程中出现错误"
        });
        setActiveTaskId(null);
      }
    }
  });
  const cancelTask = reactExports.useCallback(() => {
    setActiveTaskId(null);
    addNotification({
      type: "info",
      title: "任务已取消",
      message: "标签提取任务已取消"
    });
  }, [addNotification]);
  const validateRules = reactExports.useCallback((rules) => {
    const errors = [];
    if (rules.length === 0) {
      errors.push("至少需要配置一个提取规则");
      return errors;
    }
    for (let i = 0; i < rules.length; i++) {
      const rule1 = rules[i];
      if (rule1.start >= rule1.end) {
        errors.push(`规则 "${rule1.tag_name}": 起始位置必须小于结束位置`);
        continue;
      }
      for (let j = i + 1; j < rules.length; j++) {
        const rule2 = rules[j];
        if (!(rule1.end <= rule2.start || rule2.end <= rule1.start)) {
          errors.push(`规则 "${rule1.tag_name}" 和 "${rule2.tag_name}" 的字符范围重叠`);
        }
      }
    }
    const tagNames = rules.map((rule) => rule.tag_name);
    const uniqueTagNames = new Set(tagNames);
    if (tagNames.length !== uniqueTagNames.size) {
      errors.push("标签名称不能重复");
    }
    rules.forEach((rule) => {
      if (!rule.tag_name.trim()) {
        errors.push("标签名称不能为空");
      } else if (rule.tag_name.length > 50) {
        errors.push(`标签名称 "${rule.tag_name}" 过长，最多50个字符`);
      }
    });
    return errors;
  }, []);
  return {
    // 状态
    isExtracting: extractTags.isPending,
    isPreviewing: previewExtraction.isPending,
    isTaskRunning: !!activeTaskId,
    taskStatus,
    isTaskStatusLoading,
    // 方法
    previewExtraction: previewExtraction.mutateAsync,
    extractTags: extractTags.mutateAsync,
    cancelTask,
    validateRules,
    // 数据
    previewResult: previewExtraction.data,
    extractionError: extractTags.error,
    previewError: previewExtraction.error
  };
};
const BatchOperationBar = ({
  selectedFiles,
  caseId,
  onClearSelection,
  onRefresh
}) => {
  const [showExtractionModal, setShowExtractionModal] = reactExports.useState(false);
  const { extractTags, isExtracting, isTaskRunning, taskStatus } = useFilenameExtraction(caseId);
  const handleFilenameExtraction = (rules) => __async(null, null, function* () {
    try {
      const fileIds = selectedFiles.map((file) => file.id);
      yield extractTags({
        fileIds,
        rules,
        options: {
          overwrite_existing: false,
          skip_on_error: true
        }
      });
      onClearSelection();
      if (onRefresh) {
        setTimeout(() => {
          onRefresh();
        }, 3e3);
      }
    } catch (error) {
      console.error("文件名提取失败:", error);
    }
  });
  const handleBatchDelete = () => {
    console.log("批量删除:", selectedFiles.map((f) => f.id));
  };
  const handleBatchMove = () => {
    console.log("批量移动:", selectedFiles.map((f) => f.id));
  };
  if (selectedFiles.length === 0) {
    return null;
  }
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "bg-white rounded-lg shadow-lg border border-gray-200 px-6 py-4", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-4", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "text-sm font-medium text-gray-700", children: [
            "已选中 ",
            selectedFiles.length,
            " 个文件"
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            Button,
            {
              variant: "secondary",
              size: "small",
              onClick: onClearSelection,
              children: "清除选择"
            }
          )
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-px h-6 bg-gray-300" }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs(
            Button,
            {
              onClick: () => setShowExtractionModal(true),
              disabled: isExtracting || isTaskRunning,
              className: "flex items-center gap-2",
              title: "从文件名提取标签",
              children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-lg", children: "✨" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: "提取标签" }),
                isTaskRunning && /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "text-xs text-blue-600", children: [
                  "(",
                  taskStatus == null ? void 0 : taskStatus.progress.completed,
                  "/",
                  taskStatus == null ? void 0 : taskStatus.progress.total,
                  ")"
                ] })
              ]
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(
            Button,
            {
              variant: "secondary",
              onClick: handleBatchDelete,
              className: "flex items-center gap-2",
              title: "批量删除文件",
              children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-lg", children: "🗑️" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: "删除" })
              ]
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(
            Button,
            {
              variant: "secondary",
              onClick: handleBatchMove,
              className: "flex items-center gap-2",
              title: "批量移动文件",
              children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-lg", children: "📁" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: "移动" })
              ]
            }
          )
        ] })
      ] }),
      isTaskRunning && taskStatus && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mt-3 pt-3 border-t border-gray-200", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between text-sm text-gray-600", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { children: [
            "正在处理: ",
            taskStatus.progress.current_file
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { children: [
            taskStatus.progress.completed,
            " / ",
            taskStatus.progress.total
          ] })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mt-2 w-full bg-gray-200 rounded-full h-2", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
          "div",
          {
            className: "bg-blue-600 h-2 rounded-full transition-all duration-300",
            style: {
              width: `${taskStatus.progress.completed / taskStatus.progress.total * 100}%`
            }
          }
        ) })
      ] })
    ] }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      FilenameExtractionModal,
      {
        isOpen: showExtractionModal,
        onClose: () => setShowExtractionModal(false),
        selectedFiles,
        onExtract: handleFilenameExtraction
      }
    )
  ] });
};
const GalleryPanel = () => {
  const {
    caseId,
    selectedTags,
    searchQuery
  } = useTagManagementStore();
  const [selectedFiles, setSelectedFiles] = reactExports.useState([]);
  const [isSelectionMode, setIsSelectionMode] = reactExports.useState(false);
  const {
    data: caseData,
    isLoading,
    refetch: refetchFiles
  } = useCaseInfo(caseId);
  const allFiles = (caseData == null ? void 0 : caseData.files) || [];
  React.useEffect(() => {
  }, [caseData, allFiles.length, selectedTags]);
  const handleRefresh = reactExports.useCallback(() => {
    console.log("🔄 手动刷新画廊数据...");
    refetchFiles();
  }, [refetchFiles]);
  const files = React.useMemo(() => {
    if (selectedTags.size === 0) {
      return allFiles;
    }
    const filteredFiles = allFiles.filter((file) => {
      var _a, _b, _c, _d, _e;
      const fileTagStrings = /* @__PURE__ */ new Set();
      if ((_a = file.tags) == null ? void 0 : _a.properties) {
        Object.entries(file.tags.properties).forEach(([key, value]) => {
          fileTagStrings.add(`properties:${key}:${value}`);
        });
      }
      if ((_b = file.ttags) == null ? void 0 : _b.metadata) {
        Object.entries(file.ttags.metadata).forEach(([key, value]) => {
          fileTagStrings.add(`metadata:${key}:${value}`);
        });
      }
      if ((_c = file.ttags) == null ? void 0 : _c.cv) {
        Object.entries(file.ttags.cv).forEach(([key, value]) => {
          fileTagStrings.add(`cv:${key}:${value}`);
        });
      }
      if (Array.isArray((_d = file.ttags) == null ? void 0 : _d.user)) {
        file.ttags.user.forEach((tag) => {
          fileTagStrings.add(`user:${tag}`);
        });
      }
      if (Array.isArray((_e = file.ttags) == null ? void 0 : _e.ai)) {
        file.ttags.ai.forEach((tag) => {
          fileTagStrings.add(`ai:${tag}`);
        });
      }
      for (const selectedTag of selectedTags) {
        if (fileTagStrings.has(selectedTag)) {
          return true;
        }
      }
      return false;
    });
    return filteredFiles;
  }, [allFiles, selectedTags]);
  const handleFileSelect = reactExports.useCallback((file, isSelected) => {
    if (isSelected) {
      setSelectedFiles((prev) => [...prev, file]);
    } else {
      setSelectedFiles((prev) => prev.filter((f) => f.id !== file.id));
    }
  }, []);
  const enterSelectionMode = reactExports.useCallback(() => {
    setIsSelectionMode(true);
  }, []);
  const exitSelectionMode = reactExports.useCallback(() => {
    setIsSelectionMode(false);
    setSelectedFiles([]);
  }, []);
  const selectAllFiles = reactExports.useCallback(() => {
    if (!isSelectionMode) {
      setIsSelectionMode(true);
    }
    setSelectedFiles([...files]);
  }, [files, isSelectionMode]);
  const clearSelection = reactExports.useCallback(() => {
    setSelectedFiles([]);
  }, []);
  const getGalleryTitle = () => {
    if (selectedTags.size === 0) {
      return "所有文件";
    }
    const tagNames = Array.from(selectedTags).map((tagId) => {
      return tagId;
    });
    return tagNames.join(" + ");
  };
  const renderEmptyState = () => /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-6xl mb-4", children: "📁" }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-lg font-medium text-gray-900 mb-2", children: selectedTags.size === 0 ? "选择标签查看文件" : "没有找到相关文件" }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-500", children: selectedTags.size === 0 ? "在左侧面板中选择一个或多个标签来查看相关的文件" : "当前选择的标签组合没有关联的文件" })
  ] }) });
  const renderLoadingState = () => /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4" }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-500", children: "加载文件中..." })
  ] }) });
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex-1 flex flex-col bg-white", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-4 border-b border-gray-200", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-lg font-semibold text-gray-900", children: getGalleryTitle() }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("p", { className: "text-sm text-gray-500", children: [
          isLoading ? "加载中..." : `共 ${files.length} 个文件`,
          selectedFiles.length > 0 && ` • 已选中 ${selectedFiles.length} 个`
        ] })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Button,
          {
            onClick: handleRefresh,
            disabled: isLoading,
            variant: "ghost",
            size: "sm",
            title: "刷新画廊数据",
            children: isLoading ? "刷新中..." : "🔄 刷新"
          }
        ),
        !isSelectionMode ? /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            onClick: enterSelectionMode,
            disabled: files.length === 0,
            className: "px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 disabled:opacity-50 disabled:cursor-not-allowed",
            title: "进入选择模式",
            children: "选择"
          }
        ) : /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "button",
            {
              onClick: selectAllFiles,
              className: "px-3 py-1 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200",
              title: "全选",
              children: "全选"
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "button",
            {
              onClick: clearSelection,
              disabled: selectedFiles.length === 0,
              className: "px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 disabled:opacity-50",
              title: "清除选择",
              children: "清除"
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "button",
            {
              onClick: exitSelectionMode,
              className: "px-3 py-1 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200",
              title: "退出选择模式",
              children: "退出"
            }
          )
        ] })
      ] })
    ] }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 overflow-y-auto", children: isLoading ? renderLoadingState() : files.length === 0 ? renderEmptyState() : /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-4", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-8 gap-4", children: files.map((file) => /* @__PURE__ */ jsxRuntimeExports.jsx(
      ImageThumbnail,
      {
        file,
        caseId,
        isSelectionMode,
        isSelected: selectedFiles.some((f) => f.id === file.id),
        onSelect: handleFileSelect
      },
      file.id
    )) }) }) }),
    selectedFiles.length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsx(
      BatchOperationBar,
      {
        selectedFiles,
        caseId,
        onClearSelection: clearSelection,
        onRefresh: refetchFiles
      }
    )
  ] });
};
const CustomTagModal = () => {
  const {
    showCustomTagModal,
    editingCustomTag,
    closeCustomTagModal,
    caseId
  } = useTagManagementStore();
  const [name, setName] = reactExports.useState("");
  const [color, setColor] = reactExports.useState("#3B82F6");
  const [isFormReady, setIsFormReady] = reactExports.useState(false);
  const createCustomTag = useCreateCustomTag();
  const updateCustomTag = useUpdateCustomTag();
  const isEdit = !!editingCustomTag;
  const colorPresets = [
    "#3B82F6",
    // blue-500
    "#EF4444",
    // red-500
    "#10B981",
    // emerald-500
    "#F59E0B",
    // amber-500
    "#8B5CF6",
    // violet-500
    "#EC4899",
    // pink-500
    "#06B6D4",
    // cyan-500
    "#84CC16",
    // lime-500
    "#F97316",
    // orange-500
    "#6366F1",
    // indigo-500
    "#14B8A6",
    // teal-500
    "#F43F5E"
    // rose-500
  ];
  reactExports.useEffect(() => {
    if (showCustomTagModal) {
      if (editingCustomTag) {
        setName(editingCustomTag.name);
        setColor(editingCustomTag.color);
      } else {
        setName("");
        setColor("#3B82F6");
      }
      setTimeout(() => setIsFormReady(true), 100);
    } else {
      setIsFormReady(false);
    }
  }, [showCustomTagModal, editingCustomTag]);
  const handleSubmit = (e) => __async(null, null, function* () {
    e.preventDefault();
    if (!isFormReady) {
      return;
    }
    if (!name.trim()) {
      alert("请输入标签名称");
      return;
    }
    try {
      if (isEdit && editingCustomTag) {
        yield updateCustomTag.mutateAsync({
          tagId: editingCustomTag.id,
          data: { name: name.trim(), color }
        });
      } else {
        yield createCustomTag.mutateAsync({
          name: name.trim(),
          color
        });
      }
    } catch (error) {
      console.error("保存标签失败:", error);
    }
  });
  const handleClose = () => {
    closeCustomTagModal();
    setName("");
    setColor("#3B82F6");
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx(
    Modal,
    {
      isOpen: showCustomTagModal,
      onClose: handleClose,
      title: isEdit ? "编辑自定义标签" : "添加自定义标签",
      size: "md",
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs("form", { onSubmit: handleSubmit, className: "space-y-6", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("label", { htmlFor: "tag-name", className: "block text-sm font-medium text-gray-700 mb-2", children: "标签名称" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            Input,
            {
              id: "tag-name",
              type: "text",
              value: name,
              onChange: (e) => setName(e.target.value),
              placeholder: "请输入标签名称",
              required: true,
              autoFocus: true
            }
          )
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("label", { htmlFor: "tag-color", className: "block text-sm font-medium text-gray-700 mb-2", children: "标签颜色" }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-3", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-3", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                "input",
                {
                  id: "tag-color",
                  type: "color",
                  value: color,
                  onChange: (e) => setColor(e.target.value),
                  className: "w-12 h-10 border border-gray-300 rounded cursor-pointer"
                }
              ),
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                Input,
                {
                  type: "text",
                  value: color,
                  onChange: (e) => setColor(e.target.value),
                  placeholder: "#3B82F6",
                  className: "flex-1"
                }
              )
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-xs text-gray-500 mb-2", children: "预设颜色:" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "grid grid-cols-6 gap-2", children: colorPresets.map((presetColor) => /* @__PURE__ */ jsxRuntimeExports.jsx(
                "button",
                {
                  type: "button",
                  onClick: () => setColor(presetColor),
                  className: `w-8 h-8 rounded border-2 transition-all ${color === presetColor ? "border-gray-400 scale-110" : "border-gray-200 hover:border-gray-300"}`,
                  style: { backgroundColor: presetColor },
                  title: presetColor
                },
                presetColor
              )) })
            ] })
          ] })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "预览" }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "div",
              {
                className: "w-4 h-4 rounded-full",
                style: { backgroundColor: color }
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm text-gray-700", children: name.trim() || "标签名称" })
          ] })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-end space-x-3 pt-4", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            Button,
            {
              type: "button",
              variant: "secondary",
              onClick: handleClose,
              children: "取消"
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            Button,
            {
              type: "submit",
              disabled: createCustomTag.isPending || updateCustomTag.isPending,
              children: createCustomTag.isPending || updateCustomTag.isPending ? "保存中..." : isEdit ? "保存" : "添加"
            }
          )
        ] })
      ] })
    }
  );
};
const TagManagementPage = () => {
  var _a;
  const { caseId } = useParams();
  const [searchParams] = useSearchParams();
  const {
    setCaseId,
    setSourceFileId,
    showImageModal,
    currentImageId,
    closeImageModal,
    toggleTagSelection,
    selectedTags,
    expandCategory,
    setOriginImageId
  } = useTagManagementStore();
  const { data: caseData } = useCaseInfo(parseInt(caseId));
  const currentImage = (_a = caseData == null ? void 0 : caseData.files) == null ? void 0 : _a.find((file) => file.id === currentImageId);
  const getCurrentImageTags = () => {
    var _a2, _b, _c, _d, _e;
    if (!currentImage) return { properties: {}, metadata: {}, cv: {}, user: [], ai: [] };
    console.log("getCurrentImageTags - currentImage:", currentImage);
    console.log("getCurrentImageTags - currentImage.tags:", currentImage.tags);
    console.log("getCurrentImageTags - currentImage.ttags:", currentImage.ttags);
    const tags = {
      properties: ((_a2 = currentImage.tags) == null ? void 0 : _a2.properties) || {},
      metadata: ((_b = currentImage.ttags) == null ? void 0 : _b.metadata) || {},
      cv: ((_c = currentImage.ttags) == null ? void 0 : _c.cv) || {},
      user: ((_d = currentImage.ttags) == null ? void 0 : _d.user) || [],
      ai: ((_e = currentImage.ttags) == null ? void 0 : _e.ai) || []
    };
    console.log("getCurrentImageTags - extracted tags:", tags);
    return tags;
  };
  const getImageUrl = (file) => {
    if (file == null ? void 0 : file.thumbnail_small_path) {
      return `file://${file.thumbnail_small_path}`;
    }
    return `/api/v1/cases/${caseId}/files/${file == null ? void 0 : file.id}/thumbnail`;
  };
  const getOriginalImageUrl = (file) => {
    if (file == null ? void 0 : file.file_path) {
      return `file://${file.file_path}`;
    }
    return `/api/v1/cases/${caseId}/files/${file == null ? void 0 : file.id}/view`;
  };
  const handleTagClick = (category, key, value) => {
    let tagId;
    if (category === "user" || category === "ai") {
      tagId = `${category}:${key}`;
    } else {
      tagId = `${category}:${key}:${value}`;
    }
    setOriginImageId(currentImageId);
    closeImageModal();
    expandCategory(category);
    toggleTagSelection(tagId);
  };
  reactExports.useEffect(() => {
    if (caseId) {
      setCaseId(parseInt(caseId));
    }
    const sourceFileId = searchParams.get("sourceFileId");
    if (sourceFileId) {
      setSourceFileId(parseInt(sourceFileId));
    }
    const targetTagType = searchParams.get("tagType");
    const targetTagKey = searchParams.get("tagKey");
    const targetTagText = searchParams.get("tagText");
    const targetTagId = searchParams.get("tagId");
    if (targetTagType && targetTagKey) {
      console.log("标签跳转参数:", {
        targetTagType,
        targetTagKey,
        targetTagText,
        targetTagId
      });
    }
  }, [caseId, searchParams, setCaseId, setSourceFileId]);
  if (!caseId) {
    return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "min-h-screen flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("h1", { className: "text-2xl font-bold text-gray-900 mb-2", children: "缺少案例ID参数" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-600", children: "请提供有效的案例ID来访问标签管理页面" })
    ] }) });
  }
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "h-screen bg-gray-50", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { id: "notifications", className: "fixed top-4 right-4 z-50 space-y-2" }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex h-full", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(TagPanel, {}),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "div",
        {
          id: "panel-divider",
          className: "w-1 bg-gray-200 hover:bg-blue-400 cursor-col-resize transition-colors"
        }
      ),
      /* @__PURE__ */ jsxRuntimeExports.jsx(GalleryPanel, {})
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      Modal,
      {
        isOpen: showImageModal,
        onClose: closeImageModal,
        size: "xl",
        children: currentImage && (() => {
          const imageTags = getCurrentImageTags();
          return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-6", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "img",
              {
                src: getOriginalImageUrl(currentImage),
                alt: currentImage.file_name,
                className: "w-full max-h-96 object-contain",
                onError: (e) => {
                  const target = e.target;
                  target.src = getImageUrl(currentImage);
                }
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "bg-gray-50 p-4 rounded-lg", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "font-semibold text-gray-900 mb-2", children: "文件信息" }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid grid-cols-2 gap-2 text-sm text-gray-600", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsxs("p", { children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsx("strong", { children: "文件名:" }),
                  " ",
                  currentImage.file_name
                ] }),
                /* @__PURE__ */ jsxRuntimeExports.jsxs("p", { children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsx("strong", { children: "尺寸:" }),
                  " ",
                  currentImage.width,
                  " × ",
                  currentImage.height
                ] }),
                /* @__PURE__ */ jsxRuntimeExports.jsxs("p", { children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsx("strong", { children: "文件类型:" }),
                  " ",
                  currentImage.file_type
                ] }),
                /* @__PURE__ */ jsxRuntimeExports.jsxs("p", { children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsx("strong", { children: "文件ID:" }),
                  " ",
                  currentImage.id
                ] })
              ] })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-4", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "font-semibold text-gray-900", children: "标签信息" }),
              Object.keys(imageTags.properties).length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "bg-blue-50 p-3 rounded-lg", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("h4", { className: "font-medium text-blue-900 mb-2", children: "属性标签" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex flex-wrap gap-2", children: Object.entries(imageTags.properties).map(([key, value]) => {
                  const tagId = `properties:${key}:${value}`;
                  const isSelected = selectedTags.has(tagId);
                  return /* @__PURE__ */ jsxRuntimeExports.jsxs(
                    "button",
                    {
                      onClick: () => handleTagClick("properties", key, String(value)),
                      className: `px-2 py-1 rounded text-xs transition-colors cursor-pointer hover:bg-blue-200 ${isSelected ? "bg-blue-200 text-blue-900 ring-2 ring-blue-400" : "bg-blue-100 text-blue-800"}`,
                      children: [
                        key,
                        ": ",
                        String(value)
                      ]
                    },
                    key
                  );
                }) })
              ] }),
              Object.keys(imageTags.metadata).length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "bg-green-50 p-3 rounded-lg", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("h4", { className: "font-medium text-green-900 mb-2", children: "元数据标签" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex flex-wrap gap-2", children: Object.entries(imageTags.metadata).map(([key, value]) => {
                  const tagId = `metadata:${key}:${value}`;
                  const isSelected = selectedTags.has(tagId);
                  return /* @__PURE__ */ jsxRuntimeExports.jsxs(
                    "button",
                    {
                      onClick: () => handleTagClick("metadata", key, String(value)),
                      className: `px-2 py-1 rounded text-xs transition-colors cursor-pointer hover:bg-green-200 ${isSelected ? "bg-green-200 text-green-900 ring-2 ring-green-400" : "bg-green-100 text-green-800"}`,
                      children: [
                        key,
                        ": ",
                        String(value)
                      ]
                    },
                    key
                  );
                }) })
              ] }),
              Object.keys(imageTags.cv).length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "bg-purple-50 p-3 rounded-lg", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("h4", { className: "font-medium text-purple-900 mb-2", children: "计算机视觉标签" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex flex-wrap gap-2", children: Object.entries(imageTags.cv).map(([key, value]) => {
                  const tagId = `cv:${key}:${value}`;
                  const isSelected = selectedTags.has(tagId);
                  return /* @__PURE__ */ jsxRuntimeExports.jsxs(
                    "button",
                    {
                      onClick: () => handleTagClick("cv", key, String(value)),
                      className: `px-2 py-1 rounded text-xs transition-colors cursor-pointer hover:bg-purple-200 ${isSelected ? "bg-purple-200 text-purple-900 ring-2 ring-purple-400" : "bg-purple-100 text-purple-800"}`,
                      children: [
                        key,
                        ": ",
                        String(value)
                      ]
                    },
                    key
                  );
                }) })
              ] }),
              Array.isArray(imageTags.user) && imageTags.user.length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "bg-yellow-50 p-3 rounded-lg", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("h4", { className: "font-medium text-yellow-900 mb-2", children: "用户标签" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex flex-wrap gap-2", children: imageTags.user.map((tag, index) => {
                  const tagId = `user:${tag}`;
                  const isSelected = selectedTags.has(tagId);
                  return /* @__PURE__ */ jsxRuntimeExports.jsx(
                    "button",
                    {
                      onClick: () => handleTagClick("user", String(tag), ""),
                      className: `px-2 py-1 rounded text-xs transition-colors cursor-pointer hover:bg-yellow-200 ${isSelected ? "bg-yellow-200 text-yellow-900 ring-2 ring-yellow-400" : "bg-yellow-100 text-yellow-800"}`,
                      children: String(tag)
                    },
                    index
                  );
                }) })
              ] }),
              Array.isArray(imageTags.ai) && imageTags.ai.length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "bg-red-50 p-3 rounded-lg", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("h4", { className: "font-medium text-red-900 mb-2", children: "AI标签" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex flex-wrap gap-2", children: imageTags.ai.map((tag, index) => {
                  const tagId = `ai:${tag}`;
                  const isSelected = selectedTags.has(tagId);
                  return /* @__PURE__ */ jsxRuntimeExports.jsx(
                    "button",
                    {
                      onClick: () => handleTagClick("ai", String(tag), ""),
                      className: `px-2 py-1 rounded text-xs transition-colors cursor-pointer hover:bg-red-200 ${isSelected ? "bg-red-200 text-red-900 ring-2 ring-red-400" : "bg-red-100 text-red-800"}`,
                      children: String(tag)
                    },
                    index
                  );
                }) })
              ] }),
              Object.keys(imageTags.properties).length === 0 && Object.keys(imageTags.metadata).length === 0 && Object.keys(imageTags.cv).length === 0 && imageTags.user.length === 0 && imageTags.ai.length === 0 && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-center py-4 text-gray-500", children: "该文件暂无标签信息" })
            ] })
          ] });
        })()
      }
    ),
    /* @__PURE__ */ jsxRuntimeExports.jsx(CustomTagModal, {})
  ] });
};
export {
  TagManagementPage as default
};
