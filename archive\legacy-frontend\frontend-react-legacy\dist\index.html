<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="./vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vite + React + TS</title>
    <script type="module" crossorigin src="./js/index-BaeIiao7.js"></script>
    <link rel="modulepreload" crossorigin href="./js/react-vendor-ZA51SWXd.js">
    <link rel="modulepreload" crossorigin href="./js/router-DbSvV1fW.js">
    <link rel="modulepreload" crossorigin href="./js/state-management-CeNIv-64.js">
    <link rel="modulepreload" crossorigin href="./js/ui-vendor-DgYk2OaC.js">
    <link rel="stylesheet" crossorigin href="./css/index-CHhT-GFw.css">
  </head>
  <body>
    <div id="root"></div>
  </body>
</html>
