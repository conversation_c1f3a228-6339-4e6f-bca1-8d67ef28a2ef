import { j as jsxRuntimeExports } from "./state-management-CeNIv-64.js";
import { r as reactExports } from "./router-DbSvV1fW.js";
import { c as cn } from "./index-BaeIiao7.js";
const SearchIcon = ({ className }) => /* @__PURE__ */ jsxRuntimeExports.jsx(
  "svg",
  {
    className: cn("w-4 h-4", className),
    fill: "none",
    stroke: "currentColor",
    viewBox: "0 0 24 24",
    children: /* @__PURE__ */ jsxRuntimeExports.jsx(
      "path",
      {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        strokeWidth: 2,
        d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
      }
    )
  }
);
const ClearButton = ({ onClick, className }) => /* @__PURE__ */ jsxRuntimeExports.jsx(
  "button",
  {
    type: "button",
    onClick,
    className: cn(
      "p-1 hover:bg-gray-100 rounded transition-colors",
      className
    ),
    title: "清除搜索",
    children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-4 h-4", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
      "path",
      {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        strokeWidth: 2,
        d: "M6 18L18 6M6 6l12 12"
      }
    ) })
  }
);
const SearchSuggestions = ({
  suggestions,
  onSelect,
  highlightedIndex = -1,
  maxHeight = "200px"
}) => {
  if (suggestions.length === 0) return null;
  return /* @__PURE__ */ jsxRuntimeExports.jsx(
    "div",
    {
      className: "absolute top-full left-0 right-0 z-50 bg-white border border-gray-200 rounded-md shadow-lg mt-1",
      style: { maxHeight },
      children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "py-1 max-h-full overflow-y-auto", children: suggestions.map((suggestion, index) => /* @__PURE__ */ jsxRuntimeExports.jsxs(
        "button",
        {
          type: "button",
          className: cn(
            "w-full px-3 py-2 text-left hover:bg-gray-50 transition-colors",
            "flex items-center gap-2",
            index === highlightedIndex && "bg-blue-50"
          ),
          onClick: () => onSelect(suggestion),
          children: [
            suggestion.icon && /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-gray-400", children: suggestion.icon }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex-1 min-w-0", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-sm text-gray-900 truncate", children: suggestion.text }),
              suggestion.category && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-xs text-gray-500", children: suggestion.category })
            ] })
          ]
        },
        suggestion.id
      )) })
    }
  );
};
const SearchBox = ({
  value,
  onChange,
  onSearch,
  onClear,
  placeholder = "搜索...",
  debounceMs = 300,
  showSuggestions = false,
  suggestions = [],
  onSuggestionSelect,
  disabled = false,
  size = "md",
  variant = "default",
  className
}) => {
  const [isFocused, setIsFocused] = reactExports.useState(false);
  const [highlightedIndex, setHighlightedIndex] = reactExports.useState(-1);
  const inputRef = reactExports.useRef(null);
  const debounceRef = reactExports.useRef();
  reactExports.useEffect(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }
    debounceRef.current = setTimeout(() => {
      if (value && onSearch) {
        onSearch(value);
      }
    }, debounceMs);
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, [value, onSearch, debounceMs]);
  const handleInputChange = (e) => {
    const newValue = e.target.value;
    onChange(newValue);
    setHighlightedIndex(-1);
  };
  const handleClear = () => {
    var _a;
    onChange("");
    onClear == null ? void 0 : onClear();
    (_a = inputRef.current) == null ? void 0 : _a.focus();
  };
  const handleKeyDown = (e) => {
    var _a;
    if (!showSuggestions || suggestions.length === 0) return;
    switch (e.key) {
      case "ArrowDown":
        e.preventDefault();
        setHighlightedIndex(
          (prev) => prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case "ArrowUp":
        e.preventDefault();
        setHighlightedIndex(
          (prev) => prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case "Enter":
        e.preventDefault();
        if (highlightedIndex >= 0 && suggestions[highlightedIndex]) {
          onSuggestionSelect == null ? void 0 : onSuggestionSelect(suggestions[highlightedIndex]);
        } else if (value && onSearch) {
          onSearch(value);
        }
        break;
      case "Escape":
        setIsFocused(false);
        (_a = inputRef.current) == null ? void 0 : _a.blur();
        break;
    }
  };
  const handleSuggestionSelect = (suggestion) => {
    var _a;
    onChange(suggestion.text);
    onSuggestionSelect == null ? void 0 : onSuggestionSelect(suggestion);
    setIsFocused(false);
    (_a = inputRef.current) == null ? void 0 : _a.blur();
  };
  const sizeClasses = {
    sm: "px-3 py-1.5 text-sm",
    md: "px-4 py-2 text-sm",
    lg: "px-4 py-3 text-base"
  };
  const variantClasses = {
    default: "border border-gray-300 bg-white",
    filled: "border-0 bg-gray-100",
    outlined: "border-2 border-gray-300 bg-transparent"
  };
  const iconSize = {
    sm: "w-4 h-4",
    md: "w-4 h-4",
    lg: "w-5 h-5"
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: cn("relative", className), children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "relative", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: cn(
        "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",
        size === "lg" ? "left-4" : "left-3"
      ), children: /* @__PURE__ */ jsxRuntimeExports.jsx(SearchIcon, { className: iconSize[size] }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "input",
        {
          ref: inputRef,
          type: "text",
          value,
          onChange: handleInputChange,
          onKeyDown: handleKeyDown,
          onFocus: () => setIsFocused(true),
          onBlur: () => setTimeout(() => setIsFocused(false), 200),
          placeholder,
          disabled,
          className: cn(
            "w-full rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500",
            "pl-10 pr-10",
            sizeClasses[size],
            variantClasses[variant],
            disabled && "opacity-50 cursor-not-allowed",
            size === "lg" && "pl-12 pr-12"
          )
        }
      ),
      value && !disabled && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: cn(
        "absolute right-3 top-1/2 transform -translate-y-1/2",
        size === "lg" ? "right-4" : "right-3"
      ), children: /* @__PURE__ */ jsxRuntimeExports.jsx(ClearButton, { onClick: handleClear }) })
    ] }),
    showSuggestions && isFocused && suggestions.length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsx(
      SearchSuggestions,
      {
        suggestions,
        onSelect: handleSuggestionSelect,
        highlightedIndex
      }
    )
  ] });
};
export {
  SearchBox as S
};
