import React, { useState } from 'react';
import { Resizable } from './components/ui/resizable';
import { NavigationSidebar } from './components/NavigationSidebar';
import { Gallery } from './components/Gallery';
import { Workspace } from './components/Workspace';
import { DetailsSidebar } from './components/DetailsSidebar';

const App: React.FC = () => {
  const [leftSidebarVisible, setLeftSidebarVisible] = useState(true);
  const [rightSidebarVisible, setRightSidebarVisible] = useState(true);
  const [workspaceVisible, setWorkspaceVisible] = useState(true); // 临时改为true以便测试
  const [galleryWorkspaceSwapped, setGalleryWorkspaceSwapped] = useState(false);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [currentLibrary, setCurrentLibrary] = useState('默认档案库');

  const toggleSidebars = () => {
    setLeftSidebarVisible(!leftSidebarVisible);
    setRightSidebarVisible(!rightSidebarVisible);
  };

  const toggleWorkspace = () => {
    setWorkspaceVisible(!workspaceVisible);
  };

  const swapGalleryWorkspace = () => {
    setGalleryWorkspaceSwapped(!galleryWorkspaceSwapped);
  };

  return (
    <div className="h-screen w-screen overflow-hidden" style={{ background: 'var(--mizzy-gallery)' }}>
      <div className="flex h-full">
        {/* 左侧导航栏 */}
        {leftSidebarVisible && (
          <div 
            className="min-w-[280px] max-w-[400px] w-[320px] border-r"
            style={{ 
              background: 'var(--mizzy-nav)',
              borderColor: 'var(--mizzy-border)'
            }}
          >
            <NavigationSidebar
              onToggleSidebars={toggleSidebars}
              onToggleWorkspace={toggleWorkspace}
              onSwapGalleryWorkspace={swapGalleryWorkspace}
              selectedTags={selectedTags}
              onSelectedTagsChange={setSelectedTags}
              currentLibrary={currentLibrary}
              onCurrentLibraryChange={setCurrentLibrary}
            />
          </div>
        )}

        {/* 中央区域 */}
        <div className="flex-1 flex flex-col">
          {/* 画廊区域 */}
          <div className={`flex-1 ${workspaceVisible && !galleryWorkspaceSwapped ? 'min-h-[300px]' : ''}`}>
            <Gallery
              selectedImages={selectedImages}
              onSelectedImagesChange={setSelectedImages}
              selectedTags={selectedTags}
              currentLibrary={currentLibrary}
              isVisible={!galleryWorkspaceSwapped || !workspaceVisible}
            />
          </div>

          {/* 工作台区域 */}
          {workspaceVisible && (
            <div 
              className={`border-t ${galleryWorkspaceSwapped ? 'flex-1' : 'h-[300px] min-h-[200px] max-h-[500px]'}`}
              style={{ borderColor: 'var(--mizzy-border)' }}
            >
              <Workspace
                onToggleWorkspace={toggleWorkspace}
                onSwapGalleryWorkspace={swapGalleryWorkspace}
                galleryWorkspaceSwapped={galleryWorkspaceSwapped}
                selectedImages={selectedImages}
                selectedTags={selectedTags}
              />
            </div>
          )}
        </div>

        {/* 右侧详情栏 */}
        {rightSidebarVisible && (
          <div 
            className="min-w-[280px] max-w-[400px] w-[320px] border-l"
            style={{ 
              background: 'var(--mizzy-nav)',
              borderColor: 'var(--mizzy-border)'
            }}
          >
            <DetailsSidebar
              selectedImages={selectedImages}
              selectedTags={selectedTags}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default App;