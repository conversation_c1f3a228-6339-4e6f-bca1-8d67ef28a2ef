var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
import { j as jsxRuntimeExports } from "./state-management-CeNIv-64.js";
import { r as reactExports, f as reactDomExports } from "./router-DbSvV1fW.js";
import { c as cn } from "./index-BaeIiao7.js";
import { a as axios } from "./index-DEw2ppt0.js";
const Modal = ({
  isOpen,
  onClose,
  title,
  children,
  size = "md",
  closeOnOverlayClick = true,
  closeOnEscape = true,
  className
}) => {
  reactExports.useEffect(() => {
    if (!isOpen || !closeOnEscape) return;
    const handleEscape = (e) => {
      if (e.key === "Escape") {
        console.log("🔍 Modal: ESC键被按下，关闭模态框");
        onClose();
      }
    };
    document.addEventListener("keydown", handleEscape);
    return () => document.removeEventListener("keydown", handleEscape);
  }, [isOpen, closeOnEscape, onClose]);
  reactExports.useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }
    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);
  if (!isOpen) return null;
  const sizeClasses = {
    sm: "max-w-md",
    md: "max-w-lg",
    lg: "max-w-2xl",
    xl: "max-w-4xl",
    full: "max-w-full mx-4"
  };
  const modalContent = /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "fixed inset-0 z-50 overflow-y-auto", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex min-h-screen items-center justify-center p-4", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      "div",
      {
        className: "fixed inset-0 bg-black bg-opacity-50 transition-opacity",
        onClick: closeOnOverlayClick ? () => {
          console.log("🔍 Modal: 点击遮罩层，关闭模态框");
          onClose();
        } : void 0
      }
    ),
    /* @__PURE__ */ jsxRuntimeExports.jsxs(
      "div",
      {
        className: cn(
          "relative bg-white rounded-lg shadow-xl w-full",
          sizeClasses[size],
          "transform transition-all",
          className
        ),
        onClick: (e) => e.stopPropagation(),
        children: [
          title && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between p-6 border-b border-gray-200", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-lg font-semibold text-gray-900", children: title }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "button",
              {
                onClick: onClose,
                className: "text-gray-400 hover:text-gray-600 transition-colors",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                  "svg",
                  {
                    className: "w-6 h-6",
                    fill: "none",
                    stroke: "currentColor",
                    viewBox: "0 0 24 24",
                    children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                      "path",
                      {
                        strokeLinecap: "round",
                        strokeLinejoin: "round",
                        strokeWidth: 2,
                        d: "M6 18L18 6M6 6l12 12"
                      }
                    )
                  }
                )
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: cn("p-6", title && "pt-0"), children })
        ]
      }
    )
  ] }) });
  return reactDomExports.createPortal(modalContent, document.body);
};
const API_BASE_URL = "http://localhost:8000";
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 1e4,
  headers: {
    "Content-Type": "application/json"
  }
});
apiClient.interceptors.request.use(
  (config) => {
    var _a;
    console.log(`API Request: ${(_a = config.method) == null ? void 0 : _a.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error("API Request Error:", error);
    return Promise.reject(error);
  }
);
apiClient.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error("API Response Error:", error);
    if (error.response) {
      const { status, data } = error.response;
      console.error(`API Error ${status}:`, data);
    } else if (error.request) {
      console.error("Network Error:", error.request);
    } else {
      console.error("Error:", error.message);
    }
    return Promise.reject(error);
  }
);
class CaseService {
  // 获取所有案例
  static getCases() {
    return __async(this, null, function* () {
      const response = yield apiClient.get("/api/v1/cases/");
      return response.data;
    });
  }
  // 获取单个案例
  static getCase(id) {
    return __async(this, null, function* () {
      const response = yield apiClient.get(`/api/v1/cases/${id}`);
      return response.data;
    });
  }
  // 创建案例
  static createCase(data) {
    return __async(this, null, function* () {
      const response = yield apiClient.post("/api/v1/cases/", data);
      return response.data.data;
    });
  }
  // 更新案例
  static updateCase(id, data) {
    return __async(this, null, function* () {
      const response = yield apiClient.put(`/api/v1/cases/${id}`, data);
      return response.data.data;
    });
  }
  // 删除案例
  static deleteCase(id) {
    return __async(this, null, function* () {
      yield apiClient.delete(`/api/v1/cases/${id}`);
    });
  }
  // 获取案例文件
  static getCaseFiles(caseId) {
    return __async(this, null, function* () {
      var _a, _b;
      const response = yield apiClient.get(`/api/v1/cases/${caseId}/files/`);
      console.log("🔍 CaseService.getCaseFiles - 原始响应:", response);
      console.log("🔍 CaseService.getCaseFiles - response.data:", response.data);
      console.log("🔍 CaseService.getCaseFiles - response.data.files:", (_a = response.data) == null ? void 0 : _a.files);
      console.log("🔍 CaseService.getCaseFiles - response类型:", typeof response);
      console.log("🔍 CaseService.getCaseFiles - response键:", Object.keys(response));
      const files = ((_b = response.data) == null ? void 0 : _b.files) || [];
      console.log("🔍 CaseService.getCaseFiles - 最终文件数组:", files);
      console.log("🔍 CaseService.getCaseFiles - 获取到", files.length, "个文件");
      return files;
    });
  }
  // 导入文件到案例（推荐：不复制文件，只记录路径）
  static importFiles(caseId, files, onProgress) {
    return __async(this, null, function* () {
      const results = [];
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        if (file.path) {
          const response = yield this.importFilesByPath(caseId, [file.path]);
          results.push(...response);
        } else {
          console.warn(`无法获取文件路径，使用上传模式: ${file.name}`);
          const uploadResult = yield this.uploadFiles(caseId, [file], onProgress);
          results.push(...uploadResult);
        }
        if (onProgress) {
          const progress = Math.round((i + 1) / files.length * 100);
          onProgress(progress);
        }
      }
      return results;
    });
  }
  // 上传文件到案例（特殊场景：会复制文件）
  static uploadFiles(caseId, files, onProgress) {
    return __async(this, null, function* () {
      const uploadedFiles = [];
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const formData = new FormData();
        formData.append("file", file);
        const response = yield apiClient.post(
          `/api/v1/cases/${caseId}/files/upload-and-copy`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data"
            },
            onUploadProgress: (progressEvent) => {
              if (progressEvent.total && onProgress) {
                const currentFileProgress = Math.round(
                  progressEvent.loaded * 100 / progressEvent.total
                );
                const totalProgress = Math.round(
                  (i * 100 + currentFileProgress) / files.length
                );
                onProgress(totalProgress);
              }
            }
          }
        );
        uploadedFiles.push(response.data);
      }
      return uploadedFiles;
    });
  }
  // 按路径导入本地文件（不复制文件）
  static importFilesByPath(caseId, filePaths) {
    return __async(this, null, function* () {
      const results = [];
      for (const filePath of filePaths) {
        const formData = new FormData();
        formData.append("file_path", filePath);
        const response = yield apiClient.post(
          `/api/v1/cases/${caseId}/files/import-by-path`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data"
            }
          }
        );
        results.push(response.data.data);
      }
      return results;
    });
  }
  // 删除文件
  static deleteFile(caseId, fileId) {
    return __async(this, null, function* () {
      yield apiClient.delete(`/api/v1/cases/${caseId}/files/${fileId}`);
    });
  }
  // 批量删除文件
  static deleteFiles(caseId, fileIds) {
    return __async(this, null, function* () {
      yield apiClient.post(`/api/v1/cases/${caseId}/files/batch-delete`, {
        file_ids: fileIds
      });
    });
  }
  // 搜索案例
  static searchCases(query) {
    return __async(this, null, function* () {
      const response = yield apiClient.get("/api/v1/cases/search", {
        params: { q: query }
      });
      return response.data.data;
    });
  }
}
export {
  CaseService as C,
  Modal as M,
  apiClient as a
};
