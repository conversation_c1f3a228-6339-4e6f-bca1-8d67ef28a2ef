var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
import { u as useQueryClient, j as jsxRuntimeExports } from "./state-management-CeNIv-64.js";
import { d as useParams, a as useNavigate, r as reactExports } from "./router-DbSvV1fW.js";
import { d as useCase } from "./useCases-mlPph2ta.js";
import { u as useAppStore, c as cn } from "./index-BaeIiao7.js";
import { B as Button } from "./Button-CswqCd84.js";
import { M as Modal, C as CaseService } from "./caseService-rs6u721W.js";
import { F as FileUpload, i as invalidateFileRelatedCaches } from "./cacheUtils-BRH6C8y1.js";
import "./react-vendor-ZA51SWXd.js";
import "./ui-vendor-DgYk2OaC.js";
import "./index-DEw2ppt0.js";
const CaseDetailPage = () => {
  const { caseId } = useParams();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [selectedFiles, setSelectedFiles] = reactExports.useState(/* @__PURE__ */ new Set());
  const [batchMode, setBatchMode] = reactExports.useState(false);
  const [showImageModal, setShowImageModal] = reactExports.useState(false);
  const [currentImage, setCurrentImage] = reactExports.useState(null);
  const [showUploadModal, setShowUploadModal] = reactExports.useState(false);
  const { addNotification } = useAppStore();
  const queryClient = useQueryClient();
  const { data: caseData, isLoading: caseLoading, error: caseError } = useCase(parseInt(caseId));
  const files = (caseData == null ? void 0 : caseData.files) || [];
  const filesLoading = caseLoading;
  const filteredFiles = reactExports.useMemo(() => {
    if (!searchTerm) return files;
    return files.filter(
      (file) => file.file_name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [files, searchTerm]);
  const handleBack = () => {
    navigate("/");
  };
  const handleTagManagement = () => {
    navigate(`/cases/${caseId}/tags`);
  };
  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };
  const handleFileSelect = (fileId) => {
    const newSelected = new Set(selectedFiles);
    if (newSelected.has(fileId)) {
      newSelected.delete(fileId);
    } else {
      newSelected.add(fileId);
    }
    setSelectedFiles(newSelected);
  };
  const handleSelectAll = () => {
    if (selectedFiles.size === filteredFiles.length) {
      setSelectedFiles(/* @__PURE__ */ new Set());
    } else {
      setSelectedFiles(new Set(filteredFiles.map((file) => file.id)));
    }
  };
  const handleBatchMode = () => {
    setBatchMode(!batchMode);
    setSelectedFiles(/* @__PURE__ */ new Set());
  };
  const handleImageClick = (file) => {
    if (batchMode) {
      handleFileSelect(file.id);
    } else {
      setCurrentImage(file);
      setShowImageModal(true);
    }
  };
  const getImageUrl = (file) => {
    return `http://localhost:8000/api/v1/cases/${caseId}/files/${file.id}/thumbnail`;
  };
  const getOriginalImageUrl = (file) => {
    return `http://localhost:8000/api/v1/cases/${caseId}/files/${file.id}/view`;
  };
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("zh-CN");
  };
  const handleFileUpload = (files2) => __async(null, null, function* () {
    if (!caseId) return;
    try {
      yield CaseService.uploadFiles(parseInt(caseId), files2);
      console.log("🔄 CaseDetailPage: 文件上传成功，开始失效所有相关缓存...");
      yield invalidateFileRelatedCaches(queryClient, parseInt(caseId));
      setShowUploadModal(false);
      addNotification({
        type: "success",
        title: "文件上传成功",
        message: `成功上传 ${files2.length} 个文件`
      });
    } catch (error) {
      console.error("文件上传失败:", error);
      throw error;
    }
  });
  if (caseError) {
    return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "min-h-screen flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-red-500 text-6xl mb-4", children: "⚠️" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("h1", { className: "text-2xl font-bold text-gray-900 mb-2", children: "案例加载失败" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-600 mb-4", children: "无法加载案例信息" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { onClick: handleBack, children: "返回案例列表" })
    ] }) });
  }
  const searchIcon = /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-5 h-5 text-gray-400", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" }) });
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "min-h-screen bg-gray-50", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "bg-white shadow", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between items-center py-4", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2 text-sm text-gray-600", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            onClick: handleBack,
            className: "text-blue-600 hover:text-blue-800 transition-colors",
            children: "案例管理"
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: "/" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: caseLoading ? "加载中..." : (caseData == null ? void 0 : caseData.case_name) || "未知案例" })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex space-x-4", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { onClick: () => setShowUploadModal(true), children: "上传文件" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { variant: "outline", onClick: handleTagManagement, children: "标签管理" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { variant: "outline", onClick: handleBatchMode, children: batchMode ? "退出批量" : "批量选择" })
      ] })
    ] }) }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "container mx-auto px-4 py-6", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between mb-6", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Input,
          {
            type: "text",
            placeholder: "搜索文件...",
            value: searchTerm,
            onChange: handleSearch,
            leftIcon: searchIcon,
            className: "w-64"
          }
        ),
        batchMode && selectedFiles.size > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-3", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "text-sm text-gray-600", children: [
            "已选择 ",
            selectedFiles.size,
            " 个文件"
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { size: "sm", onClick: handleSelectAll, children: selectedFiles.size === filteredFiles.length ? "取消全选" : "全选" })
        ] })
      ] }),
      filesLoading ? /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center py-8", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "mt-2 text-gray-600", children: "加载中..." })
      ] }) : filteredFiles.length === 0 ? /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center py-12", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-gray-400 text-6xl mb-4", children: "📁" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-lg font-medium text-gray-900 mb-2", children: searchTerm ? "没有找到匹配的文件" : "暂无图片文件" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-500 mb-4", children: searchTerm ? "尝试使用不同的关键词搜索" : "这个案例中还没有添加任何图片文件" })
      ] }) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4", children: filteredFiles.map((file) => /* @__PURE__ */ jsxRuntimeExports.jsxs(
        "div",
        {
          className: cn(
            "bg-white rounded-lg shadow-sm border overflow-hidden cursor-pointer transition-all duration-200",
            "hover:shadow-md hover:scale-105",
            selectedFiles.has(file.id) && "ring-2 ring-blue-500 border-blue-500"
          ),
          onClick: () => handleImageClick(file),
          children: [
            batchMode && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "absolute top-2 left-2 z-10", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "checkbox",
                checked: selectedFiles.has(file.id),
                onChange: () => handleFileSelect(file.id),
                className: "w-4 h-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500"
              }
            ) }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "aspect-square overflow-hidden", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
              "img",
              {
                src: getImageUrl(file),
                alt: file.file_name,
                className: "w-full h-full object-cover",
                loading: "lazy",
                onError: (e) => {
                  const target = e.target;
                  target.src = "/placeholder-image.png";
                }
              }
            ) }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-3", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("h4", { className: "font-medium text-sm text-gray-900 truncate mb-1", children: file.file_name }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-xs text-gray-500 space-y-1", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
                  file.width || 0,
                  " × ",
                  file.height || 0
                ] }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: file.file_type || "未知类型" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: formatDate(file.created_at) })
              ] })
            ] })
          ]
        },
        file.id
      )) })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      Modal,
      {
        isOpen: showImageModal,
        onClose: () => setShowImageModal(false),
        size: "xl",
        children: currentImage && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-4", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "img",
            {
              src: getOriginalImageUrl(currentImage),
              alt: currentImage.file_name,
              className: "w-full max-h-96 object-contain",
              onError: (e) => {
                const target = e.target;
                target.src = getImageUrl(currentImage);
              }
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid grid-cols-2 gap-4 text-sm", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium", children: "文件名:" }),
              " ",
              currentImage.file_name
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium", children: "尺寸:" }),
              " ",
              currentImage.width || 0,
              " × ",
              currentImage.height || 0
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium", children: "类型:" }),
              " ",
              currentImage.file_type || "未知"
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium", children: "拍摄时间:" }),
              " ",
              currentImage.taken_at ? formatDate(currentImage.taken_at) : "未知"
            ] })
          ] })
        ] })
      }
    ),
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      Modal,
      {
        isOpen: showUploadModal,
        onClose: () => setShowUploadModal(false),
        title: "上传文件",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-4", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-600", children: "选择要上传到案例的文件，支持图片、视频和PDF格式。" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            FileUpload,
            {
              onUpload: handleFileUpload,
              maxFiles: 10,
              className: "min-h-[200px]"
            }
          )
        ] })
      }
    )
  ] });
};
export {
  CaseDetailPage as default
};
